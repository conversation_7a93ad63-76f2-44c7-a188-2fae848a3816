<?xml version="1.0" encoding="UTF-8"?>
<!-- https://phpunit.readthedocs.io/en/latest/configuration.html -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd" backupGlobals="false" colors="true"
         bootstrap="tests/bootstrap.php">
    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">src</directory>
        </include>
    </coverage>
    <php>
        <ini name="display_errors" value="1"/>
        <ini name="error_reporting" value="-1"/>
        <ini name="memory_limit" value="-1"/>
        <server name="APP_DEBUG" value="false"/>
        <server name="APP_ENV" value="test" force="true"/>
        <server name="APP_DOMAIN" value="it" force="true"/>
        <server name="COUNTRY_TAG" value="it" force="true"/>
        <server name="DNS_DOMAINNAME" value="localhost" force="true"/>
        <server name="SHELL_VERBOSITY" value="-1"/>
        <server name="SYMFONY_PHPUNIT_REMOVE" value=""/>
        <server name="SYMFONY_PHPUNIT_VERSION" value="9.3"/>
        <env name="SYMFONY_DEPRECATIONS_HELPER" value="weak"/>
        <env name="SENTRY_DSN" value=""/>
    </php>
    <testsuites>
        <testsuite name="unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="integration">
            <directory>tests/Integration</directory>
        </testsuite>
        <testsuite name="functional">
            <directory>tests/Functional</directory>
        </testsuite>
    </testsuites>
    <listeners>
        <listener class="Symfony\Bridge\PhpUnit\SymfonyTestsListener"/>
    </listeners>
    <!-- Uncomment when adding extensions
      <extensions>
      </extensions>
      -->
</phpunit>
