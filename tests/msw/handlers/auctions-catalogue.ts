import { HttpResponse, http } from 'msw';
import auctions from '../..//mock/old-api/immobili/aste/catalogo/GET_auctions.json';
import auctionById from '../..//mock/old-api/immobili/aste/catalogo/GET_auction-by-id.json';
import regions from '../..//mock/old-api/immobili/aste/catalogo/lookup/GET_regions.json';
import procedureTypes from '../..//mock/old-api/immobili/aste/catalogo/lookup/GET_procedure-types.json';
import contents from '../..//mock/old-api/immobili/aste/catalogo/GET_contents.json';
import tos from '../..//mock/old-api/immobili/aste/catalogo/GET_tos.json';
import courts from '../..//mock/old-api/immobili/aste/catalogo/lookup/GET_courts.json';
import typologies from '../..//mock/old-api/immobili/aste/catalogo/lookup/GET_typologies.json';

export const handlers = [
    http.get('/immobili/aste/catalogo/auctions', ({ request }) => {
        return HttpResponse.json(auctions);
    }),
    http.get('/immobili/aste/catalogo/:id/detail', ({ request }) => {
        return HttpResponse.json(auctionById);
    }),
    http.get('/immobili/aste/catalogo/lookup/regions', ({ request }) => {
        return HttpResponse.json(regions);
    }),
    http.get('/immobili/aste/catalogo/contents', ({ request }) => {
        return HttpResponse.json(contents);
    }),
    http.get('/immobili/aste/catalogo/lookup/courts', ({ request }) => {
        return HttpResponse.json(courts);
    }),
    http.get('/immobili/aste/catalogo/lookup/procedure-types', ({ request }) => {
        return HttpResponse.json(procedureTypes);
    }),
    http.get('/immobili/aste/catalogo/tos', ({ request }) => {
        return HttpResponse.json(tos);
    }),
    http.post('/immobili/aste/catalogo/:id/import', ({ request }) => {
        return HttpResponse.json({ success: true });
    }),
    http.post('/immobili/aste/catalogo/:id/update', ({ request }) => {
        return HttpResponse.json({ success: true });
    }),
    http.get('/immobili/aste/catalogo/lookup/typologies', ({ request }) => {
        return HttpResponse.json(typologies);
    }),
];
