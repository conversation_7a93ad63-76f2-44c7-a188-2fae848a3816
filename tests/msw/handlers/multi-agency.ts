import { HttpResponse, http } from 'msw';

import agencyListPage1 from '../../mock/api/agencies/multi-agency/GET_members-1.json';
import agencyListPage2 from '../../mock/api/agencies/multi-agency/GET_members-2.json';

export const handlers = [
    http.get('/api/agencies/multi-agency/members', ({ request }) => {
        const url = new URL(request.url);

        const page = url.searchParams.get('page');

        if (page === '2') {
            return HttpResponse.json(agencyListPage2);
        }

        return HttpResponse.json(agencyListPage1);
    }),
];
