import { http, HttpResponse } from 'msw';

import allContactsEmail from '../../mock/old-api/v2/services/GET_all-contacts-email.json';
import contactEmailSuggest from '../../mock/old-api/v2/services/GET_contact-email-suggest.json';

export const handlers = [
    http.get('/v2/item/convert', () => {
        return HttpResponse.text();
    }),
    http.get('/v2/services/all-contacts-email', () => {
        return HttpResponse.json(allContactsEmail);
    }),
    http.get('/v2/services/contact-email-suggest', ({ request }) => {
        const url = new URL(request.url);
        const query = url.searchParams.get('q');

        // Return filtered results based on query
        if (query) {
            const filteredContacts = contactEmailSuggest.filter(contact =>
                contact.label.toLowerCase().includes(query.toLowerCase()) ||
                contact.value.toLowerCase().includes(query.toLowerCase())
            );
            return HttpResponse.json(filteredContacts);
        }

        return HttpResponse.json(contactEmailSuggest);
    }),
];
