import { HttpResponse, http } from 'msw';
import list from '../../mock/old-api/appointments/GET_list.json';
import getContactsStats60 from '../../mock/old-api/dashboard/GET_get-contacts-stats--60.json';
import getContactsStats7 from '../../mock/old-api/dashboard/GET_get-contacts-stats--7.json';
import getContactsStats360 from '../../mock/old-api/dashboard/GET_get-contacts-stats--360.json';
import matchesList from '../../mock/api/searches/GET_matches--list_1.json';
export const handlers = [
    http.post('/dashboard/get-contacts-stats', async ({ request }) => {
        const formData = await request.formData();

        switch (formData.get('days')) {
            case '7':
                return HttpResponse.json(getContactsStats7);
            case '60':
                return HttpResponse.json(getContactsStats60);
            case '360':
                return HttpResponse.json(getContactsStats360);
            default:
                return HttpResponse.json(getContactsStats360);
        }
    }),
    http.get('/appointments/list', (req) => {
        const { searchParams } = new URL(req.request.url);
        if (searchParams.get('start') && searchParams.get('end')) {
            return HttpResponse.json(list);
        }
    }),
    http.get('/api/searches/matches', async ({ request }) => {
        return HttpResponse.json(matchesList);
    }),
];
