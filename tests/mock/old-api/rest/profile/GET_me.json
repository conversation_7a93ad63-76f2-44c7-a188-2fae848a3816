{"idAgenzia": 93434, "idAgente": 205908, "isFromBackoffice": false, "isChangePasswordNeeded": false, "isDeviceVerified": true, "features": {"groups": {"agency": {"name": "agency", "enabled": 1}, "multisend": {"name": "multisend", "enabled": 1}, "acquisition": {"name": "acquisition", "enabled": 1}, "agency_estimates": {"name": "agency_estimates", "enabled": 1}, "propertyfinder": {"name": "propertyfinder", "enabled": 0}, "extra_services": {"name": "extra_services", "enabled": 1}, "virtual_tour_360": {"name": "virtual_tour_360", "enabled": 1}, "agenda": {"name": "agenda", "enabled": 1}, "dashboard": {"name": "dashboard", "enabled": 1}, "administration": {"name": "administration", "enabled": 1}, "settings": {"name": "settings", "enabled": 1}, "clients": {"name": "clients", "enabled": 1}, "customer_requests": {"name": "customer_requests", "enabled": 1}, "telefono_smart": {"name": "telefono_smart", "enabled": 1}, "ads": {"name": "ads", "enabled": 1}, "auctions": {"name": "auctions", "enabled": 1}, "projects_ads": {"name": "projects_ads", "enabled": 0}, "new_constructions_property": {"name": "new_constructions_property", "enabled": 1}, "visibility": {"name": "visibility", "enabled": 1}, "agency_leads": {"name": "agency_leads", "enabled": 1}, "appointments": {"name": "appointments", "enabled": 1}, "immovisita": {"name": "immovisita", "enabled": 1}, "youdomus": {"name": "<PERSON><PERSON>us", "enabled": 1}, "searches": {"name": "searches", "enabled": 1}, "zones": {"name": "zones", "enabled": 1}}, "features": {"multisend_property": {"name": "multisend_property", "enabled": 1, "group": "multisend"}, "multisend_project": {"name": "multisend_project", "enabled": 1, "group": "multisend"}, "multisend_portal": {"name": "multisend_portal", "enabled": 1, "group": "multisend"}, "acquisition_privates": {"name": "acquisition_privates", "enabled": 1, "group": "acquisition"}, "agency_estimates": {"name": "agency_estimates", "enabled": 1, "group": "agency_estimates"}, "agency_estimates_add": {"name": "agency_estimates_add", "enabled": 1, "group": "agency_estimates"}, "agency_estimates_list": {"name": "agency_estimates_list", "enabled": 1, "group": "agency_estimates"}, "real_estate_evaluation": {"name": "real_estate_evaluation", "enabled": 1, "group": "acquisition"}, "plans": {"name": "plans", "enabled": 1, "group": "extra_services"}, "youdomus": {"name": "<PERSON><PERSON>us", "enabled": 1, "group": "extra_services"}, "news": {"name": "news", "enabled": 1, "group": "extra_services"}, "web_service": {"name": "web_service", "enabled": 1, "group": "extra_services"}, "website": {"name": "website", "enabled": 1, "group": "extra_services"}, "evaluation": {"name": "evaluation", "enabled": 1, "group": "extra_services"}, "virtual_tour_360_property": {"name": "virtual_tour_360_property", "enabled": 1, "group": "virtual_tour_360"}, "virtual_tour_360_project": {"name": "virtual_tour_360_project", "enabled": 0, "group": "virtual_tour_360"}, "virtual_tour_360_new_constructions": {"name": "virtual_tour_360_new_constructions", "enabled": 1, "group": "virtual_tour_360"}, "agenda": {"name": "agenda", "enabled": 1, "group": "agenda"}, "dashboard": {"name": "dashboard", "enabled": 1, "group": "dashboard"}, "invoices_info": {"name": "invoices_info", "enabled": 1, "group": "administration"}, "contract_info": {"name": "contract_info", "enabled": 1, "group": "administration"}, "invoices": {"name": "invoices", "enabled": 1, "group": "administration"}, "general_settings": {"name": "general_settings", "enabled": 1, "group": "settings"}, "office_place_settings": {"name": "office_place_settings", "enabled": 1, "group": "settings"}, "images_video_settings": {"name": "images_video_settings", "enabled": 1, "group": "settings"}, "users_settings": {"name": "users_settings", "enabled": 1, "group": "settings"}, "immovisita_settings": {"name": "immovisita_settings", "enabled": 1, "group": "settings"}, "security_settings": {"name": "security_settings", "enabled": 1, "group": "settings"}, "zone_settings": {"name": "zone_settings", "enabled": 1, "group": "settings"}, "propertyfinder_search": {"name": "propertyfinder_search", "enabled": 1, "group": "propertyfinder"}, "propertyfinder_collaboration": {"name": "propertyfinder_collaboration", "enabled": 1, "group": "propertyfinder"}, "clients": {"name": "clients", "enabled": 1, "group": "clients"}, "general_customer_requests": {"name": "general_customer_requests", "enabled": 1, "group": "customer_requests"}, "direct_customer_requests": {"name": "direct_customer_requests", "enabled": 1, "group": "customer_requests"}, "managed_customer_requests": {"name": "managed_customer_requests", "enabled": 1, "group": "customer_requests"}, "requests_new": {"name": "requests_new", "enabled": 1, "group": "customer_requests"}, "telefono_smart_requests": {"name": "telefono_smart_requests", "enabled": 1, "group": "telefono_smart"}, "requests_intersections": {"name": "requests_intersections", "enabled": 1, "group": "customer_requests"}, "requests_messages": {"name": "requests_messages", "enabled": 1, "group": "customer_requests"}, "property_ads": {"name": "property_ads", "enabled": 1, "group": "ads"}, "portal_property_ads": {"name": "portal_property_ads", "enabled": 0, "group": "ads"}, "portal_properties": {"name": "portal_properties", "enabled": 1, "group": "ads"}, "project_ads": {"name": "project_ads", "enabled": 0, "group": "projects_ads"}, "projects_ads_list": {"name": "projects_ads_list", "enabled": 0, "group": "projects_ads"}, "projects_ads_new": {"name": "projects_ads_new", "enabled": 0, "group": "projects_ads"}, "portal_projects_ads": {"name": "portal_projects_ads", "enabled": 0, "group": "projects_ads"}, "new_constructions_ads": {"name": "new_constructions_ads", "enabled": 1, "group": "new_constructions_property"}, "agency_new_constructions_property": {"name": "agency_new_constructions_property", "enabled": 1, "group": "new_constructions_property"}, "portal_new_constructions_property": {"name": "portal_new_constructions_property", "enabled": 1, "group": "new_constructions_property"}, "new_constructions_property_add": {"name": "new_constructions_property_add", "enabled": 1, "group": "new_constructions_property"}, "auctions_ads": {"name": "auctions_ads", "enabled": 1, "group": "ads"}, "land_ads": {"name": "land_ads", "enabled": 1, "group": "ads"}, "land_virtual_ads": {"name": "land_virtual_ads", "enabled": 0, "group": "ads"}, "residential_ads": {"name": "residential_ads", "enabled": 1, "group": "ads"}, "commercial_ads": {"name": "commercial_ads", "enabled": 0, "group": "ads"}, "room_ads": {"name": "room_ads", "enabled": 1, "group": "ads"}, "buildings_ads": {"name": "buildings_ads", "enabled": 1, "group": "ads"}, "warehouse_ads": {"name": "warehouse_ads", "enabled": 1, "group": "ads"}, "garage_ads": {"name": "garage_ads", "enabled": 1, "group": "ads"}, "sheds_ads": {"name": "sheds_ads", "enabled": 1, "group": "ads"}, "office_ads": {"name": "office_ads", "enabled": 1, "group": "ads"}, "shop_ads": {"name": "shop_ads", "enabled": 1, "group": "ads"}, "sold_rented_ads": {"name": "sold_rented_ads", "enabled": 1, "group": "ads"}, "billboard": {"name": "billboard", "enabled": 1, "group": "ads"}, "luxury_ads": {"name": "luxury_ads", "enabled": 1, "group": "visibility"}, "foreign_ads": {"name": "foreign_ads", "enabled": 1, "group": "visibility"}, "guaranteed_property": {"name": "guaranteed_property", "enabled": 0, "group": "visibility"}, "convertible_ad": {"name": "convertible_ad", "enabled": 1, "group": "visibility"}, "validate_url": {"name": "validate_url", "enabled": 1, "group": "acquisition"}, "agency_leads": {"name": "agency_leads", "enabled": 1, "group": "agency_leads"}, "zone_requests": {"name": "zone_requests", "enabled": 1, "group": "agency_leads"}, "appointments": {"name": "appointments", "enabled": 1, "group": "appointments"}, "mortgage_advice": {"name": "mortgage_advice", "enabled": 1, "group": "extra_services"}, "auction_add": {"name": "auction_add", "enabled": 1, "group": "auctions"}, "auctions_catalogue": {"name": "auctions_catalogue", "enabled": 1, "group": "auctions"}, "auctions_list": {"name": "auctions_list", "enabled": 1, "group": "auctions"}, "auctions_list_portal": {"name": "auctions_list_portal", "enabled": 1, "group": "auctions"}, "immovisita_realestate_list": {"name": "immovisita_realestate_list", "enabled": 1, "group": "immovisita"}, "immovisita_scheduled_visits": {"name": "immovisita_scheduled_visits", "enabled": 1, "group": "immovisita"}, "youdomus_services": {"name": "youdomus_services", "enabled": 1, "group": "<PERSON><PERSON>us"}, "youdomus_monitoring": {"name": "youdomus_monitoring", "enabled": 1, "group": "<PERSON><PERSON>us"}, "youdomus_documents": {"name": "youdomus_documents", "enabled": 1, "group": "<PERSON><PERSON>us"}, "youdomus_customer": {"name": "youdomus_customer", "enabled": 1, "group": "<PERSON><PERSON>us"}, "youdomus_estates": {"name": "youdomus_estates", "enabled": 1, "group": "<PERSON><PERSON>us"}, "youdomus_subjects": {"name": "youdomus_subjects", "enabled": 1, "group": "<PERSON><PERSON>us"}, "youdomus_cadastre_on_map": {"name": "youdomus_cadastre_on_map", "enabled": 1, "group": "<PERSON><PERSON>us"}, "youdomus_ape_certifications": {"name": "youdomus_ape_certifications", "enabled": 1, "group": "<PERSON><PERSON>us"}, "real_estate_sales_requests": {"name": "real_estate_sales_requests", "enabled": 1, "group": "acquisition"}, "searches_list": {"name": "searches_list", "enabled": 1, "group": "searches"}, "multi_agency": {"name": "multi_agency", "enabled": 1, "group": "agency"}, "agency_services": {"name": "agency_services", "enabled": 0, "group": "extra_services"}, "matches": {"name": "matches", "enabled": 1, "group": "clients"}, "zones": {"name": "zones", "enabled": 1, "group": "zones"}, "agency_active_searches": {"name": "agency_active_searches", "enabled": 1, "group": "clients"}, "notification_settings": {"name": "notification_settings", "enabled": 1, "group": "clients"}}}, "userDataToken": "f46e9d912c83663fd1780c836fed648a", "multiAgency": {"isMaster": true, "masterId": null}, "responsible": {"idResponsabile": 2783, "codice": "AB_Antonio<PERSON>ozzi", "nome": "<PERSON>", "telefono": "06 6220 0941", "telefono_assistenza": "", "visibile": true, "email": "<EMAIL>", "dipartimento": "Team AB", "chatbot": false, "immagineResponsabile": 1380733401}, "modules": {"Nuove Costruzioni": {"name": "Nuove Costruzioni", "enabled": true}, "House 24": {"name": "House 24", "enabled": false}, "Estero": {"name": "Estero", "enabled": true}, "planimetria": {"name": "planimetria", "enabled": false}, "getrix": {"name": "getrix", "enabled": true}, "Sito agenzia": {"name": "<PERSON><PERSON> agenzia", "enabled": false}, "Profilo facebook": {"name": "Profilo facebook", "enabled": false}, "Youdomus.it": {"name": "Youdomus.it", "enabled": true}, "VirtualTour 360": {"name": "VirtualTour 360", "enabled": true}, "ADV": {"name": "ADV", "enabled": false}, "ImmoVox": {"name": "ImmoVox", "enabled": true}, "Multinvio": {"name": "Multinvio", "enabled": true}, "Acquisizione privati": {"name": "Acquisizione privati", "enabled": true}, "Valutazioni immobiliari": {"name": "Valutazioni immobiliari", "enabled": true}, "Report immobiliare": {"name": "Report immobiliare", "enabled": true}, "Agenda": {"name": "Agenda", "enabled": true}, "Clienti": {"name": "Clienti", "enabled": true}, "Richieste": {"name": "<PERSON><PERSON>", "enabled": true}, "Lista annunci": {"name": "Lista annunci", "enabled": true}, "Richiesta consulenza mutuo": {"name": "Richiesta consulenza mutuo", "enabled": false}, "Catalogo aste": {"name": "Catalogo aste", "enabled": true}, "Messaggistica Gestionale": {"name": "Messaggistica Gestionale", "enabled": true}, "Richieste vendita": {"name": "Richieste vendita", "enabled": true}, "Immoscout24": {"name": "Immoscout24", "enabled": false}, "Rilascio Prenota Visita": {"name": "<PERSON><PERSON><PERSON><PERSON> Visi<PERSON>", "enabled": true}, "Casa Click": {"name": "<PERSON>", "enabled": false}, "Repubblica": {"name": "Repubblica", "enabled": false}, "Altri Servizi": {"name": "<PERSON><PERSON>", "enabled": false}, "virtual tour": {"name": "virtual tour", "enabled": false}, "video tour": {"name": "video tour", "enabled": false}, "new video tour": {"name": "new video tour", "enabled": false}, "new agenda": {"name": "new agenda", "enabled": false}, "Getrix Social": {"name": "Getrix Social", "enabled": false}, "Subito.it": {"name": "Subito.it", "enabled": false}, "Immovisita": {"name": "Immov<PERSON><PERSON>", "enabled": false}, "Reportistica": {"name": "Reportistica", "enabled": true}, "Immobiliare.it": {"name": "Immobiliare.it", "enabled": false}, "Nepremicine": {"name": "Nepremicine", "enabled": false}, "Immowelt": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled": false}, "Brand": {"name": "Brand", "enabled": false}, "Brand Plus": {"name": "Brand Plus", "enabled": false}}}