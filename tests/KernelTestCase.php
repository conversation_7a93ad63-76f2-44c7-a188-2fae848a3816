<?php

declare(strict_types=1);

namespace Tests;

use App\Builder\Entity\AgencyBuilder;
use App\Builder\Entity\AgentBuilder;
use App\Kernel;
use App\Security\User\GetrixUser;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase as BaseKernelTestCase;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Tests\DataProvider\AbstractDataProvider;

class KernelTestCase extends BaseKernelTestCase
{
    protected static function createKernel(array $options = []): KernelInterface
    {
        return new Kernel(
            $options['domain'] ?? $_ENV['APP_DOMAIN'] ?? $_SERVER['APP_DOMAIN'] ?? 'it',
            $options['environment'] ?? $_ENV['APP_ENV'] ?? $_SERVER['APP_ENV'] ?? 'test',
            $options['debug'] ?? (bool) ($_ENV['APP_DEBUG'] ?? $_SERVER['APP_DEBUG'] ?? true)
        );
    }

    protected function setDataProviderMockData(array $dataProviderMockData): void
    {
        AbstractDataProvider::setDataProviderMockData($dataProviderMockData);
    }

    protected function setUser(
        string $username = 'user_test',
        int $agencyId = 93434,
        int $agentId = 235278,
        array $roles = ['ROLE_USER']
    ): UsernamePasswordToken {
        $user = $this->getGetrixUser($username, $agencyId, $agentId, $roles);
        $token = new UsernamePasswordToken(
            $user,
            null,
            'secured_area',
            $roles
        );

        $container = static::$kernel->getContainer();
        $container->get('security.token_storage')->setToken($token);

        return $token;
    }

    protected function getGetrixUser(
        string $username = 'user_test',
        int $agencyId = 93434,
        int $agentId = 235278,
        array $roles = ['ROLE_USER']
    ): GetrixUser {
        $user = new GetrixUser($username, $roles, null, $agencyId, $agentId);
        $user->setAgenzia(AgencyBuilder::newBuilder()->fromApiResponse($this->getAgency($agencyId))->build());
        $user->setAgente(AgentBuilder::newBuilder()->fromApiResponse($this->getAgent($agentId))->build());

        return $user;
    }

    protected function getAgency(int $agencyId): array
    {
        return \json_decode(
            \file_get_contents(__DIR__ . "/Fixtures/Helper/AgencyApiClientHelper/agency_{$agencyId}.json"),
            true
        );
    }

    protected function getAgent(int $agentId): array
    {
        return \json_decode(
            \file_get_contents(__DIR__ . "/Fixtures/Helper/AgentApiClientHelper/agent_{$agentId}.json"),
            true
        );
    }
}
