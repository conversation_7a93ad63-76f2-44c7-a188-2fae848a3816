export const gtxLoggedUser = (path: string) => {
    let splittedPath: any, constant: any;

    if (global.globalVars.APP_DEBUG && !path) {
        throw new Error('Percorso della risorsa mancante');
    }

    splittedPath = path.split('.');

    constant = splittedPath.reduce(function (current, i) {
        return current[i];
    }, global.gtxLoggedUser);

    if (global.globalVars.APP_DEBUG && typeof constant === 'undefined') {
        throw new Error('La proprietà ' + path + ' non è definita'); // the actual module has a wrong message
    }

    return constant;
};
