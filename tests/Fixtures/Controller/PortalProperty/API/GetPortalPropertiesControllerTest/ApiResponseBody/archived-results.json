{"data": {"pagination": {"page": 1, "results": 50, "total": 343}, "properties": [{"agencyNotes": null, "auction": null, "brokerageData": {"agent": {"advertisingVisibility": null, "agencyId": null, "chat": null, "cognome": "Napoli", "email": null, "firstname": "<PERSON>", "id": 235374, "idAgente": 235374, "lastname": "Napoli", "mobilePhone": null, "newEmail": null, "nome": "<PERSON>", "ownership": null, "phone": null, "profile": null, "profileImageId": 1500740701, "role": null, "status": null, "uuid": null, "uuidSocials": null, "verifiedEmail": null}, "collaboration": null, "mandateExpirationDate": null, "mandateType": null, "owner": null, "proposer": null, "reference": "Salve<PERSON><PERSON><PERSON><PERSON>", "workProgress": null}, "category": "Stanza/Camera", "categoryId": 5, "code": "Salve<PERSON><PERSON><PERSON><PERSON>", "contract": "Affitto", "contractId": 2, "created": "28/02/2025", "descriptions": [], "externalMedia": {"hasPhotoPlan": false, "planUrl": null, "videoCodes": [], "videoUrl": null, "virtualTourUrl": null}, "extraVisibilities": {"searchable": false, "showcase": false, "showcaseExpiration": null, "showcasePremium": false, "star": false, "starExpiration": null, "starPremium": false, "top": false, "topExpiration": null, "topPremium": false}, "favourite": false, "id": 116777493, "images": null, "integrationFlag": 2, "mainImageThumbUrl": "/img/getrix/common/img-placeholder.png", "mainImageUrl": "https://media.it.localhost/image//print.jpg", "modified": "28/02/2025", "performanceInvalidityReason": null, "performanceRelativeIndex": null, "performanceRelativeIndexMissingDays": null, "portalUrl": "https://localhost/annunci/116777493?anteprima=s", "price": "€ 50/mese", "priceByRequest": false, "project": null, "properties": [{"address": null, "availability": null, "availableForStudents": null, "bathrooms": null, "cadastralData": null, "category": "Stanza/Camera", "categoryId": 5, "city": null, "coWorkingTimetable": null, "composition": null, "condominiumCosts": null, "consistences": null, "country": null, "deposit": null, "energyClass": null, "features": null, "floor": null, "freeNow": null, "garage": null, "geographyInformation": null, "heatingCosts": null, "id": null, "images": null, "incomeProducingProperty": null, "industrial": null, "isMain": null, "land": null, "plans": null, "portions": [], "price": null, "propertyCondition": null, "propertyOwnership": null, "province": null, "reference": "Salve<PERSON><PERSON><PERSON><PERSON>", "region": null, "rentContractType": null, "rentToBuy": null, "rooms": null, "sharedApartmentInformation": null, "shop": null, "subTypology": "Stanza in appartamento", "subTypologyId": 194, "surface": "40 m²", "typology": "In casa", "typologyId": 191, "zone": null}], "ranking": 1, "searchPosition": null, "statusId": 4, "subTypology": "Stanza in appartamento", "subTypologyId": 194, "type": "property", "typology": "In casa", "typologyId": 191, "usersStats": {"hidden": 0, "saves": 0, "views": 0}}, {"agencyNotes": null, "auction": null, "brokerageData": {"agent": {"advertisingVisibility": null, "agencyId": null, "chat": null, "cognome": "Napoli", "email": null, "firstname": "<PERSON>", "id": 235374, "idAgente": 235374, "lastname": "Napoli", "mobilePhone": null, "newEmail": null, "nome": "<PERSON>", "ownership": null, "phone": null, "profile": null, "profileImageId": 1500740701, "role": null, "status": null, "uuid": null, "uuidSocials": null, "verifiedEmail": null}, "collaboration": null, "mandateExpirationDate": null, "mandateType": null, "owner": null, "proposer": null, "reference": "provaprovasa<PERSON>", "workProgress": null}, "category": "Stanza/Camera", "categoryId": 5, "code": "provaprovasa<PERSON>", "contract": "Affitto", "contractId": 2, "created": "27/02/2025", "descriptions": [], "externalMedia": {"hasPhotoPlan": false, "planUrl": null, "videoCodes": [], "videoUrl": null, "virtualTourUrl": null}, "extraVisibilities": {"searchable": false, "showcase": false, "showcaseExpiration": null, "showcasePremium": false, "star": false, "starExpiration": null, "starPremium": false, "top": false, "topExpiration": null, "topPremium": false}, "favourite": false, "id": 116777489, "images": null, "integrationFlag": 2, "mainImageThumbUrl": "/img/getrix/common/img-placeholder.png", "mainImageUrl": "https://media.it.localhost/image//print.jpg", "modified": "27/02/2025", "performanceInvalidityReason": null, "performanceRelativeIndex": null, "performanceRelativeIndexMissingDays": null, "portalUrl": "https://localhost/annunci/116777489?anteprima=s", "price": "€ 500/mese", "priceByRequest": false, "project": null, "properties": [{"address": null, "availability": null, "availableForStudents": null, "bathrooms": null, "cadastralData": null, "category": "Stanza/Camera", "categoryId": 5, "city": null, "coWorkingTimetable": null, "composition": null, "condominiumCosts": null, "consistences": null, "country": null, "deposit": null, "energyClass": null, "features": null, "floor": null, "freeNow": null, "garage": null, "geographyInformation": null, "heatingCosts": null, "id": null, "images": null, "incomeProducingProperty": null, "industrial": null, "isMain": null, "land": null, "plans": null, "portions": [], "price": null, "propertyCondition": null, "propertyOwnership": null, "province": null, "reference": "provaprovasa<PERSON>", "region": null, "rentContractType": null, "rentToBuy": null, "rooms": null, "sharedApartmentInformation": null, "shop": null, "subTypology": "Stanza in appartamento", "subTypologyId": 194, "surface": null, "typology": "In casa", "typologyId": 191, "zone": null}], "ranking": 1, "searchPosition": null, "statusId": 4, "subTypology": "Stanza in appartamento", "subTypologyId": 194, "type": "property", "typology": "In casa", "typologyId": 191, "usersStats": {"hidden": 0, "saves": 0, "views": 0}}, {"agencyNotes": null, "auction": null, "brokerageData": {"agent": {"advertisingVisibility": null, "agencyId": null, "chat": null, "cognome": "Napoli", "email": null, "firstname": "<PERSON>", "id": 235374, "idAgente": 235374, "lastname": "Napoli", "mobilePhone": null, "newEmail": null, "nome": "<PERSON>", "ownership": null, "phone": null, "profile": null, "profileImageId": 1500740701, "role": null, "status": null, "uuid": null, "uuidSocials": null, "verifiedEmail": null}, "collaboration": null, "mandateExpirationDate": null, "mandateType": null, "owner": null, "proposer": null, "reference": "NON MODIFICARE", "workProgress": null}, "category": "Uffici - Coworking", "categoryId": 255, "code": "NON MODIFICARE", "contract": "Affitto", "contractId": 2, "created": "27/07/2020", "descriptions": [], "externalMedia": {"hasPhotoPlan": false, "planUrl": null, "videoCodes": [], "videoUrl": null, "virtualTourUrl": "https://tour360.getrix.it/vrtours/711125/"}, "extraVisibilities": {"searchable": false, "showcase": false, "showcaseExpiration": null, "showcasePremium": false, "star": false, "starExpiration": null, "starPremium": false, "top": false, "topExpiration": null, "topPremium": false}, "favourite": true, "id": 81869282, "images": [{"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935005/vetrina.jpg", "url": "https://media.it.localhost/image/1616935005/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935031/vetrina.jpg", "url": "https://media.it.localhost/image/1616935031/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935059/vetrina.jpg", "url": "https://media.it.localhost/image/1616935059/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935081/vetrina.jpg", "url": "https://media.it.localhost/image/1616935081/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935111/vetrina.jpg", "url": "https://media.it.localhost/image/1616935111/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935131/vetrina.jpg", "url": "https://media.it.localhost/image/1616935131/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935163/vetrina.jpg", "url": "https://media.it.localhost/image/1616935163/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935189/vetrina.jpg", "url": "https://media.it.localhost/image/1616935189/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935215/vetrina.jpg", "url": "https://media.it.localhost/image/1616935215/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935241/vetrina.jpg", "url": "https://media.it.localhost/image/1616935241/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935263/vetrina.jpg", "url": "https://media.it.localhost/image/1616935263/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935283/vetrina.jpg", "url": "https://media.it.localhost/image/1616935283/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935329/vetrina.jpg", "url": "https://media.it.localhost/image/1616935329/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935355/vetrina.jpg", "url": "https://media.it.localhost/image/1616935355/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935385/vetrina.jpg", "url": "https://media.it.localhost/image/1616935385/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935407/vetrina.jpg", "url": "https://media.it.localhost/image/1616935407/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935433/vetrina.jpg", "url": "https://media.it.localhost/image/1616935433/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935461/vetrina.jpg", "url": "https://media.it.localhost/image/1616935461/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935483/vetrina.jpg", "url": "https://media.it.localhost/image/1616935483/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935505/vetrina.jpg", "url": "https://media.it.localhost/image/1616935505/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935527/vetrina.jpg", "url": "https://media.it.localhost/image/1616935527/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935549/vetrina.jpg", "url": "https://media.it.localhost/image/1616935549/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935577/vetrina.jpg", "url": "https://media.it.localhost/image/1616935577/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935625/vetrina.jpg", "url": "https://media.it.localhost/image/1616935625/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935641/vetrina.jpg", "url": "https://media.it.localhost/image/1616935641/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935653/vetrina.jpg", "url": "https://media.it.localhost/image/1616935653/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935659/vetrina.jpg", "url": "https://media.it.localhost/image/1616935659/print.jpg"}], "integrationFlag": 2, "mainImageThumbUrl": "https://media.it.localhost/image/1616935005/thumb.jpg", "mainImageUrl": "https://media.it.localhost/image/1616935005/print.jpg", "modified": "22/01/2025", "performanceInvalidityReason": {"id": 1, "name": "Ad not found"}, "performanceRelativeIndex": null, "performanceRelativeIndexMissingDays": null, "portalUrl": "https://localhost/annunci/81869282?anteprima=s", "price": null, "priceByRequest": null, "project": null, "properties": [{"address": "Via Roma ", "availability": null, "availableForStudents": null, "bathrooms": 2, "cadastralData": [{"address": null, "cadastralClass": null, "cadastralIncome": 9791, "cadastralType": {"id": 1, "name": "Fabbricato"}, "id": 707168, "otherCadastralData": "<PERSON><PERSON> dati catastali", "ownershipShares": "<PERSON>uota prop<PERSON>", "parcel": "<PERSON><PERSON><PERSON>", "section": "Sezione", "sheet": "<PERSON><PERSON><PERSON>", "stapledParcel": null, "stapledSheet": null, "stapledSubordinate": null, "subordinate": "Subalterno"}], "category": "Uffici - Coworking", "categoryId": 255, "city": "<PERSON><PERSON>", "coWorkingTimetable": null, "composition": null, "condominiumCosts": null, "consistences": null, "country": "IT", "deposit": null, "energyClass": null, "features": null, "floor": null, "freeNow": null, "garage": null, "geographyInformation": {"address": {"number": null, "street": "Via Roma"}, "city": {"chiefTown": null, "cityMacroZoneType": null, "coordinates": null, "id": "10803", "istatCode": "090061", "name": "<PERSON><PERSON>", "province": {"id": "SS", "name": "Sassari", "region": {"country": {"id": "IT", "name": "Italia"}, "id": "sar", "name": "Sardegna"}}}, "coordinates": {"latitude": 40.48429871, "longitude": 8.58650017}, "flagAddress": 1, "macroZone": null, "showAddress": true}, "heatingCosts": null, "id": 2909748, "images": [{"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935005/vetrina.jpg", "url": "https://media.it.localhost/image/1616935005/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935031/vetrina.jpg", "url": "https://media.it.localhost/image/1616935031/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935059/vetrina.jpg", "url": "https://media.it.localhost/image/1616935059/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935081/vetrina.jpg", "url": "https://media.it.localhost/image/1616935081/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935111/vetrina.jpg", "url": "https://media.it.localhost/image/1616935111/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935131/vetrina.jpg", "url": "https://media.it.localhost/image/1616935131/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935163/vetrina.jpg", "url": "https://media.it.localhost/image/1616935163/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935189/vetrina.jpg", "url": "https://media.it.localhost/image/1616935189/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935215/vetrina.jpg", "url": "https://media.it.localhost/image/1616935215/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935241/vetrina.jpg", "url": "https://media.it.localhost/image/1616935241/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935263/vetrina.jpg", "url": "https://media.it.localhost/image/1616935263/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935283/vetrina.jpg", "url": "https://media.it.localhost/image/1616935283/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935329/vetrina.jpg", "url": "https://media.it.localhost/image/1616935329/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935355/vetrina.jpg", "url": "https://media.it.localhost/image/1616935355/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935385/vetrina.jpg", "url": "https://media.it.localhost/image/1616935385/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935407/vetrina.jpg", "url": "https://media.it.localhost/image/1616935407/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935433/vetrina.jpg", "url": "https://media.it.localhost/image/1616935433/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935461/vetrina.jpg", "url": "https://media.it.localhost/image/1616935461/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935483/vetrina.jpg", "url": "https://media.it.localhost/image/1616935483/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935505/vetrina.jpg", "url": "https://media.it.localhost/image/1616935505/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935527/vetrina.jpg", "url": "https://media.it.localhost/image/1616935527/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935549/vetrina.jpg", "url": "https://media.it.localhost/image/1616935549/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935577/vetrina.jpg", "url": "https://media.it.localhost/image/1616935577/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1616935625/vetrina.jpg", "url": "https://media.it.localhost/image/1616935625/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935641/vetrina.jpg", "url": "https://media.it.localhost/image/1616935641/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935653/vetrina.jpg", "url": "https://media.it.localhost/image/1616935653/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1616935659/vetrina.jpg", "url": "https://media.it.localhost/image/1616935659/print.jpg"}], "incomeProducingProperty": null, "industrial": null, "isMain": null, "land": null, "plans": null, "portions": [], "price": null, "propertyCondition": null, "propertyOwnership": null, "province": "SS", "reference": "NON MODIFICARE", "region": null, "rentContractType": null, "rentToBuy": null, "rooms": null, "sharedApartmentInformation": null, "shop": null, "subTypology": "Coworking", "subTypologyId": 259, "surface": "44 m²", "typology": "Coworking", "typologyId": 257, "zone": null}], "ranking": 49, "searchPosition": null, "statusId": 4, "subTypology": "Coworking", "subTypologyId": 259, "type": "property", "typology": "Coworking", "typologyId": 257, "usersStats": {"hidden": 0, "saves": 0, "views": 371}}, {"agencyNotes": null, "auction": null, "brokerageData": null, "category": "Garage - Posti auto", "categoryId": 250, "code": "", "contract": "<PERSON><PERSON><PERSON>", "contractId": 1, "created": "12/09/2023", "descriptions": [], "externalMedia": {"hasPhotoPlan": false, "planUrl": null, "videoCodes": [], "videoUrl": null, "virtualTourUrl": "https://tour360.getrix.it/vrtours/1588939/"}, "extraVisibilities": {"searchable": false, "showcase": false, "showcaseExpiration": null, "showcasePremium": false, "star": false, "starExpiration": null, "starPremium": false, "top": false, "topExpiration": null, "topPremium": false}, "favourite": false, "id": 106052719, "images": [{"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602675/vetrina.jpg", "url": "https://media.it.localhost/image/1403602675/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602681/vetrina.jpg", "url": "https://media.it.localhost/image/1403602681/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602687/vetrina.jpg", "url": "https://media.it.localhost/image/1403602687/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602689/vetrina.jpg", "url": "https://media.it.localhost/image/1403602689/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602691/vetrina.jpg", "url": "https://media.it.localhost/image/1403602691/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602695/vetrina.jpg", "url": "https://media.it.localhost/image/1403602695/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602703/vetrina.jpg", "url": "https://media.it.localhost/image/1403602703/print.jpg"}], "integrationFlag": 2, "mainImageThumbUrl": "https://media.it.localhost/image/1403602675/thumb.jpg", "mainImageUrl": "https://media.it.localhost/image/1403602675/print.jpg", "modified": "23/10/2024", "performanceInvalidityReason": {"id": 1, "name": "Ad not found"}, "performanceRelativeIndex": null, "performanceRelativeIndexMissingDays": null, "portalUrl": "https://localhost/annunci/106052719?anteprima=s", "price": "€ 880.000", "priceByRequest": true, "project": null, "properties": [{"address": "<PERSON> ", "availability": null, "availableForStudents": null, "bathrooms": null, "cadastralData": null, "category": "Garage - Posti auto", "categoryId": 250, "city": "Napoli", "coWorkingTimetable": null, "composition": null, "condominiumCosts": null, "consistences": null, "country": "IT", "deposit": null, "energyClass": null, "features": null, "floor": null, "freeNow": null, "garage": null, "geographyInformation": {"address": {"number": null, "street": "<PERSON>"}, "city": {"chiefTown": null, "cityMacroZoneType": null, "coordinates": null, "id": "5685", "istatCode": "063049", "name": "Napoli", "province": {"id": "NA", "name": "Napoli", "region": {"country": {"id": "IT", "name": "Italia"}, "id": "cam", "name": "Campania"}}}, "coordinates": {"latitude": 40.84609985, "longitude": 14.28429985}, "flagAddress": 1, "macroZone": {"city": null, "id": 80, "keyUrl": null, "name": "Zona Industriale, Centro Direzionale, Poggioreale", "nameSn": null}, "showAddress": true}, "heatingCosts": null, "id": 14023621, "images": [{"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602675/vetrina.jpg", "url": "https://media.it.localhost/image/1403602675/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602681/vetrina.jpg", "url": "https://media.it.localhost/image/1403602681/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602687/vetrina.jpg", "url": "https://media.it.localhost/image/1403602687/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602689/vetrina.jpg", "url": "https://media.it.localhost/image/1403602689/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602691/vetrina.jpg", "url": "https://media.it.localhost/image/1403602691/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602695/vetrina.jpg", "url": "https://media.it.localhost/image/1403602695/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1403602703/vetrina.jpg", "url": "https://media.it.localhost/image/1403602703/print.jpg"}], "incomeProducingProperty": null, "industrial": null, "isMain": null, "land": null, "plans": [{"isHq": false, "thumbUrl": "https://media.it.localhost/plan/100376405/vetrina.jpg", "url": "https://media.it.localhost/plan/100376405/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/plan/100376403/vetrina.jpg", "url": "https://media.it.localhost/plan/100376403/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/plan/100376407/vetrina.jpg", "url": "https://media.it.localhost/plan/100376407/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/plan/100376409/vetrina.jpg", "url": "https://media.it.localhost/plan/100376409/print.jpg"}], "portions": [], "price": null, "propertyCondition": null, "propertyOwnership": null, "province": "NA", "reference": "", "region": null, "rentContractType": null, "rentToBuy": null, "rooms": null, "sharedApartmentInformation": null, "shop": null, "subTypology": "Garage - Box", "subTypologyId": 253, "surface": "44 m²", "typology": "Garage - Box", "typologyId": 251, "zone": "Zona Industriale, Centro Direzionale, Poggioreale"}], "ranking": 43, "searchPosition": null, "statusId": 4, "subTypology": "Garage - Box", "subTypologyId": 253, "type": "property", "typology": "Garage - Box", "typologyId": 251, "usersStats": {"hidden": 0, "saves": 0, "views": 2}}, {"agencyNotes": null, "auction": null, "brokerageData": null, "category": "Garage - Posti auto", "categoryId": 250, "code": "", "contract": "<PERSON><PERSON><PERSON>", "contractId": 1, "created": "15/07/2024", "descriptions": [], "externalMedia": {"hasPhotoPlan": false, "planUrl": null, "videoCodes": [], "videoUrl": null, "virtualTourUrl": "https://tour360.getrix.it/vrtours/1588939/"}, "extraVisibilities": {"searchable": false, "showcase": false, "showcaseExpiration": null, "showcasePremium": false, "star": false, "starExpiration": null, "starPremium": false, "top": false, "topExpiration": null, "topPremium": false}, "favourite": false, "id": 113313781, "images": [{"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161067/vetrina.jpg", "url": "https://media.it.localhost/image/1549161067/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161071/vetrina.jpg", "url": "https://media.it.localhost/image/1549161071/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161075/vetrina.jpg", "url": "https://media.it.localhost/image/1549161075/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161079/vetrina.jpg", "url": "https://media.it.localhost/image/1549161079/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161083/vetrina.jpg", "url": "https://media.it.localhost/image/1549161083/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161085/vetrina.jpg", "url": "https://media.it.localhost/image/1549161085/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161091/vetrina.jpg", "url": "https://media.it.localhost/image/1549161091/print.jpg"}], "integrationFlag": 2, "mainImageThumbUrl": "https://media.it.localhost/image/1549161067/thumb.jpg", "mainImageUrl": "https://media.it.localhost/image/1549161067/print.jpg", "modified": "18/10/2024", "performanceInvalidityReason": {"id": 1, "name": "Ad not found"}, "performanceRelativeIndex": null, "performanceRelativeIndexMissingDays": null, "portalUrl": "https://localhost/annunci/113313781?anteprima=s", "price": "€ 880.000", "priceByRequest": true, "project": null, "properties": [{"address": "Via Carlo <PERSON> 51", "availability": null, "availableForStudents": null, "bathrooms": null, "cadastralData": null, "category": "Garage - Posti auto", "categoryId": 250, "city": "Milano", "coWorkingTimetable": null, "composition": null, "condominiumCosts": null, "consistences": null, "country": "IT", "deposit": null, "energyClass": null, "features": null, "floor": null, "freeNow": null, "garage": null, "geographyInformation": {"address": {"number": "51", "street": "<PERSON>"}, "city": {"chiefTown": null, "cityMacroZoneType": null, "coordinates": null, "id": "8042", "istatCode": "015146", "name": "Milano", "province": {"id": "MI", "name": "Milano", "region": {"country": {"id": "IT", "name": "Italia"}, "id": "lom", "name": "Lombardia"}}}, "coordinates": {"latitude": 45.44070053, "longitude": 9.19219971}, "flagAddress": 1, "macroZone": {"city": null, "id": 10293, "keyUrl": null, "name": "<PERSON><PERSON><PERSON><PERSON>, Vigentino", "nameSn": null}, "showAddress": true}, "heatingCosts": null, "id": 16395473, "images": [{"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161067/vetrina.jpg", "url": "https://media.it.localhost/image/1549161067/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161071/vetrina.jpg", "url": "https://media.it.localhost/image/1549161071/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161075/vetrina.jpg", "url": "https://media.it.localhost/image/1549161075/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161079/vetrina.jpg", "url": "https://media.it.localhost/image/1549161079/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161083/vetrina.jpg", "url": "https://media.it.localhost/image/1549161083/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161085/vetrina.jpg", "url": "https://media.it.localhost/image/1549161085/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1549161091/vetrina.jpg", "url": "https://media.it.localhost/image/1549161091/print.jpg"}], "incomeProducingProperty": null, "industrial": null, "isMain": null, "land": null, "plans": [{"isHq": false, "thumbUrl": "https://media.it.localhost/plan/106967593/vetrina.jpg", "url": "https://media.it.localhost/plan/106967593/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/plan/106967595/vetrina.jpg", "url": "https://media.it.localhost/plan/106967595/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/plan/106967597/vetrina.jpg", "url": "https://media.it.localhost/plan/106967597/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/plan/106967599/vetrina.jpg", "url": "https://media.it.localhost/plan/106967599/print.jpg"}], "portions": [], "price": null, "propertyCondition": null, "propertyOwnership": null, "province": "MI", "reference": "", "region": null, "rentContractType": null, "rentToBuy": null, "rooms": null, "sharedApartmentInformation": null, "shop": null, "subTypology": "Garage - Box", "subTypologyId": 253, "surface": "44 m²", "typology": "Garage - Box", "typologyId": 251, "zone": "<PERSON><PERSON><PERSON><PERSON>, Vigentino"}], "ranking": 44, "searchPosition": 13, "statusId": 4, "subTypology": "Garage - Box", "subTypologyId": 253, "type": "property", "typology": "Garage - Box", "typologyId": 251, "usersStats": {"hidden": 0, "saves": 0, "views": 74}}, {"agencyNotes": null, "auction": null, "brokerageData": null, "category": "Garage - Posti auto", "categoryId": 250, "code": "", "contract": "<PERSON><PERSON><PERSON>", "contractId": 1, "created": "2/09/2024", "descriptions": [], "externalMedia": {"hasPhotoPlan": false, "planUrl": null, "videoCodes": [], "videoUrl": null, "virtualTourUrl": ""}, "extraVisibilities": {"searchable": false, "showcase": false, "showcaseExpiration": null, "showcasePremium": false, "star": false, "starExpiration": null, "starPremium": false, "top": false, "topExpiration": null, "topPremium": false}, "favourite": false, "id": 114238165, "images": null, "integrationFlag": 2, "mainImageThumbUrl": "/img/getrix/common/img-placeholder.png", "mainImageUrl": "https://media.it.localhost/image//print.jpg", "modified": "18/09/2024", "performanceInvalidityReason": null, "performanceRelativeIndex": null, "performanceRelativeIndexMissingDays": null, "portalUrl": "https://localhost/annunci/114238165?anteprima=s", "price": "€ 6.000", "priceByRequest": false, "project": null, "properties": [{"address": "Via Roma ", "availability": null, "availableForStudents": null, "bathrooms": null, "cadastralData": null, "category": "Garage - Posti auto", "categoryId": 250, "city": "<PERSON><PERSON><PERSON>", "coWorkingTimetable": null, "composition": null, "condominiumCosts": null, "consistences": null, "country": "IT", "deposit": null, "energyClass": null, "features": null, "floor": null, "freeNow": null, "garage": null, "geographyInformation": {"address": {"number": null, "street": "Via Roma"}, "city": {"chiefTown": null, "cityMacroZoneType": null, "coordinates": null, "id": "4525", "istatCode": "066037", "name": "<PERSON><PERSON><PERSON>", "province": {"id": "AQ", "name": "L'Aquila", "region": {"country": {"id": "IT", "name": "Italia"}, "id": "abr", "name": "Abruzzo"}}}, "coordinates": {"latitude": 42.0322113, "longitude": 13.77509022}, "flagAddress": 1, "macroZone": {"city": null, "id": 24049, "keyUrl": null, "name": "Centro", "nameSn": null}, "showAddress": true}, "heatingCosts": null, "id": 16685533, "images": null, "incomeProducingProperty": null, "industrial": null, "isMain": null, "land": null, "plans": null, "portions": [], "price": null, "propertyCondition": null, "propertyOwnership": null, "province": "AQ", "reference": "", "region": null, "rentContractType": null, "rentToBuy": null, "rooms": null, "sharedApartmentInformation": null, "shop": null, "subTypology": "Garage - Box", "subTypologyId": 253, "surface": "67 m²", "typology": "Garage - Box", "typologyId": 251, "zone": "Centro"}], "ranking": 26, "searchPosition": 1, "statusId": 4, "subTypology": "Garage - Box", "subTypologyId": 253, "type": "property", "typology": "Garage - Box", "typologyId": 251, "usersStats": {"hidden": 0, "saves": 0, "views": 40}}, {"agencyNotes": null, "auction": null, "brokerageData": null, "category": "Terreno", "categoryId": 4, "code": "", "contract": "<PERSON><PERSON><PERSON>", "contractId": 1, "created": "29/05/2019", "descriptions": [], "externalMedia": {"hasPhotoPlan": false, "planUrl": null, "videoCodes": [], "videoUrl": null, "virtualTourUrl": ""}, "extraVisibilities": {"searchable": false, "showcase": false, "showcaseExpiration": null, "showcasePremium": false, "star": false, "starExpiration": null, "starPremium": false, "top": false, "topExpiration": null, "topPremium": false}, "favourite": false, "id": 73907806, "images": [{"isHq": true, "thumbUrl": "https://media.it.localhost/image/1231705692/vetrina.jpg", "url": "https://media.it.localhost/image/1231705692/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1443334595/vetrina.jpg", "url": "https://media.it.localhost/image/1443334595/print.jpg"}], "integrationFlag": 2, "mainImageThumbUrl": "https://media.it.localhost/image/1231705692/thumb.jpg", "mainImageUrl": "https://media.it.localhost/image/1231705692/print.jpg", "modified": "30/04/2024", "performanceInvalidityReason": {"id": 1, "name": "Ad not found"}, "performanceRelativeIndex": null, "performanceRelativeIndexMissingDays": null, "portalUrl": "https://localhost/annunci/73907806?anteprima=s", "price": "€ 60.000", "priceByRequest": false, "project": null, "properties": [{"address": "Via Roma ", "availability": null, "availableForStudents": null, "bathrooms": null, "cadastralData": [{"address": {"address": {"number": "22", "street": "Via Roma"}, "city": {"chiefTown": null, "cityMacroZoneType": null, "coordinates": null, "id": 4874, "istatCode": "076050", "name": "Moliterno", "province": {"id": "PZ", "name": "Potenza", "region": {"country": {"id": "IT", "name": "Italia"}, "id": "bas", "name": "Basilicata"}}}}, "cadastralClass": null, "cadastralIncome": 4324.03, "cadastralType": {"id": 2, "name": "Terreno"}, "id": 1224702, "otherCadastralData": "", "ownershipShares": "", "parcel": "dd", "section": "---", "sheet": "add", "stapledParcel": null, "stapledSheet": null, "stapledSubordinate": null, "subordinate": "---"}], "category": "Terreno", "categoryId": 4, "city": "Moliterno", "coWorkingTimetable": null, "composition": null, "condominiumCosts": null, "consistences": null, "country": "IT", "deposit": null, "energyClass": null, "features": null, "floor": null, "freeNow": null, "garage": null, "geographyInformation": {"address": {"number": null, "street": "Via Roma"}, "city": {"chiefTown": null, "cityMacroZoneType": null, "coordinates": null, "id": "4874", "istatCode": "076050", "name": "Moliterno", "province": {"id": "PZ", "name": "Potenza", "region": {"country": {"id": "IT", "name": "Italia"}, "id": "bas", "name": "Basilicata"}}}, "coordinates": {"latitude": 40.24369812, "longitude": 15.86950016}, "flagAddress": 1, "macroZone": null, "showAddress": true}, "heatingCosts": null, "id": 2400588, "images": [{"isHq": true, "thumbUrl": "https://media.it.localhost/image/1231705692/vetrina.jpg", "url": "https://media.it.localhost/image/1231705692/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1443334595/vetrina.jpg", "url": "https://media.it.localhost/image/1443334595/print.jpg"}], "incomeProducingProperty": null, "industrial": null, "isMain": null, "land": null, "plans": [{"isHq": true, "thumbUrl": "https://media.it.localhost/plan/102116019/vetrina.jpg", "url": "https://media.it.localhost/plan/102116019/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/plan/102116037/vetrina.jpg", "url": "https://media.it.localhost/plan/102116037/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/plan/102116041/vetrina.jpg", "url": "https://media.it.localhost/plan/102116041/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/plan/102116057/vetrina.jpg", "url": "https://media.it.localhost/plan/102116057/print.jpg"}], "portions": [], "price": null, "propertyCondition": null, "propertyOwnership": null, "province": "PZ", "reference": "", "region": null, "rentContractType": null, "rentToBuy": null, "rooms": null, "sharedApartmentInformation": null, "shop": null, "subTypology": "Terreno edificabile", "subTypologyId": 261, "surface": "7 m²", "typology": "Terreno edificabile", "typologyId": 184, "zone": null}], "ranking": 37, "searchPosition": null, "statusId": 4, "subTypology": "Terreno edificabile", "subTypologyId": 261, "type": "property", "typology": "Terreno edificabile", "typologyId": 184, "usersStats": {"hidden": 0, "saves": 0, "views": 35}}, {"agencyNotes": null, "auction": null, "brokerageData": null, "category": "Residenziale", "categoryId": 1, "code": "", "contract": "<PERSON><PERSON><PERSON>", "contractId": 1, "created": "25/02/2022", "descriptions": [], "externalMedia": {"hasPhotoPlan": false, "planUrl": null, "videoCodes": [], "videoUrl": null, "virtualTourUrl": "https://tour360.getrix.it/vrtours/1446134/"}, "extraVisibilities": {"searchable": false, "showcase": false, "showcaseExpiration": null, "showcasePremium": false, "star": false, "starExpiration": null, "starPremium": false, "top": false, "topExpiration": null, "topPremium": false}, "favourite": false, "id": 97389104, "images": [{"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418798/vetrina.jpg", "url": "https://media.it.localhost/image/1207418798/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418804/vetrina.jpg", "url": "https://media.it.localhost/image/1207418804/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418808/vetrina.jpg", "url": "https://media.it.localhost/image/1207418808/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418818/vetrina.jpg", "url": "https://media.it.localhost/image/1207418818/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418826/vetrina.jpg", "url": "https://media.it.localhost/image/1207418826/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418830/vetrina.jpg", "url": "https://media.it.localhost/image/1207418830/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418836/vetrina.jpg", "url": "https://media.it.localhost/image/1207418836/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418840/vetrina.jpg", "url": "https://media.it.localhost/image/1207418840/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418846/vetrina.jpg", "url": "https://media.it.localhost/image/1207418846/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418848/vetrina.jpg", "url": "https://media.it.localhost/image/1207418848/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418852/vetrina.jpg", "url": "https://media.it.localhost/image/1207418852/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418856/vetrina.jpg", "url": "https://media.it.localhost/image/1207418856/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1210737636/vetrina.jpg", "url": "https://media.it.localhost/image/1210737636/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1437849923/vetrina.jpg", "url": "https://media.it.localhost/image/1437849923/print.jpg"}], "integrationFlag": 2, "mainImageThumbUrl": "https://media.it.localhost/image/1207418798/thumb.jpg", "mainImageUrl": "https://media.it.localhost/image/1207418798/print.jpg", "modified": "30/04/2024", "performanceInvalidityReason": {"id": 1, "name": "Ad not found"}, "performanceRelativeIndex": null, "performanceRelativeIndexMissingDays": null, "portalUrl": "https://localhost/annunci/97389104?anteprima=s", "price": "€ 230.000", "priceByRequest": false, "project": null, "properties": [{"address": "Via Abate <PERSON> 24", "availability": null, "availableForStudents": null, "bathrooms": 2, "cadastralData": [{"address": null, "cadastralClass": null, "cadastralIncome": null, "cadastralType": {"id": 1, "name": "Fabbricato"}, "id": null, "otherCadastralData": "", "ownershipShares": "", "parcel": "", "section": "", "sheet": "", "stapledParcel": null, "stapledSheet": null, "stapledSubordinate": null, "subordinate": ""}, {"address": null, "cadastralClass": null, "cadastralIncome": null, "cadastralType": {"id": 1, "name": "Fabbricato"}, "id": null, "otherCadastralData": "", "ownershipShares": "", "parcel": "", "section": "", "sheet": "", "stapledParcel": null, "stapledSheet": null, "stapledSubordinate": null, "subordinate": ""}], "category": "Residenziale", "categoryId": 1, "city": "Briga Alta", "coWorkingTimetable": null, "composition": null, "condominiumCosts": null, "consistences": null, "country": "IT", "deposit": null, "energyClass": null, "features": null, "floor": {"floorRange": {"id": 10, "name": "Terra"}, "id": 16, "name": "piano rialzato"}, "freeNow": null, "garage": null, "geographyInformation": {"address": {"number": "24", "street": "<PERSON>"}, "city": {"chiefTown": null, "cityMacroZoneType": null, "coordinates": null, "id": "9408", "istatCode": "004031", "name": "Briga Alta", "province": {"id": "CN", "name": "Cuneo", "region": {"country": {"id": "IT", "name": "Italia"}, "id": "pie", "name": "Piemonte"}}}, "coordinates": {"latitude": 44.083, "longitude": 7.7495}, "flagAddress": 1, "macroZone": {"city": null, "id": 20617, "keyUrl": null, "name": "Piaggia", "nameSn": null}, "showAddress": true}, "heatingCosts": null, "id": null, "images": [{"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418798/vetrina.jpg", "url": "https://media.it.localhost/image/1207418798/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418804/vetrina.jpg", "url": "https://media.it.localhost/image/1207418804/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418808/vetrina.jpg", "url": "https://media.it.localhost/image/1207418808/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418818/vetrina.jpg", "url": "https://media.it.localhost/image/1207418818/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418826/vetrina.jpg", "url": "https://media.it.localhost/image/1207418826/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418830/vetrina.jpg", "url": "https://media.it.localhost/image/1207418830/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418836/vetrina.jpg", "url": "https://media.it.localhost/image/1207418836/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418840/vetrina.jpg", "url": "https://media.it.localhost/image/1207418840/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418846/vetrina.jpg", "url": "https://media.it.localhost/image/1207418846/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418848/vetrina.jpg", "url": "https://media.it.localhost/image/1207418848/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418852/vetrina.jpg", "url": "https://media.it.localhost/image/1207418852/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1207418856/vetrina.jpg", "url": "https://media.it.localhost/image/1207418856/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1210737636/vetrina.jpg", "url": "https://media.it.localhost/image/1210737636/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1437849923/vetrina.jpg", "url": "https://media.it.localhost/image/1437849923/print.jpg"}], "incomeProducingProperty": null, "industrial": null, "isMain": null, "land": null, "plans": null, "portions": [], "price": null, "propertyCondition": {"id": 2, "name": "Buono / Abitabile"}, "propertyOwnership": null, "province": "CN", "reference": "", "region": null, "rentContractType": null, "rentToBuy": null, "rooms": null, "sharedApartmentInformation": null, "shop": null, "subTypology": "Appartamento", "subTypologyId": 14, "surface": "332 m²", "typology": "Appartamento", "typologyId": 7, "zone": "Piaggia"}], "ranking": 57, "searchPosition": null, "statusId": 4, "subTypology": "Appartamento", "subTypologyId": 14, "type": "property", "typology": "Appartamento", "typologyId": 7, "usersStats": {"hidden": 0, "saves": 0, "views": 163}}, {"agencyNotes": null, "auction": null, "brokerageData": null, "category": "Residenziale", "categoryId": 1, "code": "", "contract": "Affitto", "contractId": 2, "created": "30/08/2023", "descriptions": [], "externalMedia": {"hasPhotoPlan": false, "planUrl": null, "videoCodes": [], "videoUrl": null, "virtualTourUrl": "https://tour360.getrix.it/vrtours/1517409/"}, "extraVisibilities": {"searchable": false, "showcase": false, "showcaseExpiration": null, "showcasePremium": false, "star": false, "starExpiration": null, "starPremium": false, "top": false, "topExpiration": null, "topPremium": false}, "favourite": false, "id": 105531703, "images": [{"isHq": true, "thumbUrl": "https://media.it.localhost/image/1391195237/vetrina.jpg", "url": "https://media.it.localhost/image/1391195237/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1391195241/vetrina.jpg", "url": "https://media.it.localhost/image/1391195241/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1391195243/vetrina.jpg", "url": "https://media.it.localhost/image/1391195243/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1391195249/vetrina.jpg", "url": "https://media.it.localhost/image/1391195249/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1391195251/vetrina.jpg", "url": "https://media.it.localhost/image/1391195251/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1391195255/vetrina.jpg", "url": "https://media.it.localhost/image/1391195255/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1391195259/vetrina.jpg", "url": "https://media.it.localhost/image/1391195259/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1398403909/vetrina.jpg", "url": "https://media.it.localhost/image/1398403909/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1398404607/vetrina.jpg", "url": "https://media.it.localhost/image/1398404607/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1438826793/vetrina.jpg", "url": "https://media.it.localhost/image/1438826793/print.jpg"}], "integrationFlag": 2, "mainImageThumbUrl": "https://media.it.localhost/image/1391195237/thumb.jpg", "mainImageUrl": "https://media.it.localhost/image/1391195237/print.jpg", "modified": "30/04/2024", "performanceInvalidityReason": {"id": 1, "name": "Ad not found"}, "performanceRelativeIndex": null, "performanceRelativeIndexMissingDays": null, "portalUrl": "https://localhost/annunci/105531703?anteprima=s", "price": "€ 343/mese", "priceByRequest": false, "project": null, "properties": [{"address": "<PERSON> G<PERSON> ", "availability": null, "availableForStudents": null, "bathrooms": null, "cadastralData": null, "category": "Residenziale", "categoryId": 1, "city": "Briga Alta", "coWorkingTimetable": null, "composition": null, "condominiumCosts": null, "consistences": null, "country": "IT", "deposit": null, "energyClass": null, "features": null, "floor": null, "freeNow": null, "garage": null, "geographyInformation": {"address": {"number": "", "street": "<PERSON> G<PERSON>"}, "city": {"chiefTown": null, "cityMacroZoneType": null, "coordinates": null, "id": "9408", "istatCode": "004031", "name": "Briga Alta", "province": {"id": "CN", "name": "Cuneo", "region": {"country": {"id": "IT", "name": "Italia"}, "id": "pie", "name": "Piemonte"}}}, "coordinates": {"latitude": 44.0838, "longitude": 7.752}, "flagAddress": 1, "macroZone": null, "showAddress": true}, "heatingCosts": null, "id": null, "images": [{"isHq": true, "thumbUrl": "https://media.it.localhost/image/1391195237/vetrina.jpg", "url": "https://media.it.localhost/image/1391195237/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1391195241/vetrina.jpg", "url": "https://media.it.localhost/image/1391195241/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1391195243/vetrina.jpg", "url": "https://media.it.localhost/image/1391195243/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1391195249/vetrina.jpg", "url": "https://media.it.localhost/image/1391195249/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1391195251/vetrina.jpg", "url": "https://media.it.localhost/image/1391195251/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1391195255/vetrina.jpg", "url": "https://media.it.localhost/image/1391195255/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1391195259/vetrina.jpg", "url": "https://media.it.localhost/image/1391195259/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1398403909/vetrina.jpg", "url": "https://media.it.localhost/image/1398403909/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/image/1398404607/vetrina.jpg", "url": "https://media.it.localhost/image/1398404607/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/image/1438826793/vetrina.jpg", "url": "https://media.it.localhost/image/1438826793/print.jpg"}], "incomeProducingProperty": null, "industrial": null, "isMain": null, "land": null, "plans": [{"isHq": true, "thumbUrl": "https://media.it.localhost/plan/99914815/vetrina.jpg", "url": "https://media.it.localhost/plan/99914815/print.jpg"}, {"isHq": true, "thumbUrl": "https://media.it.localhost/plan/99914817/vetrina.jpg", "url": "https://media.it.localhost/plan/99914817/print.jpg"}, {"isHq": false, "thumbUrl": "https://media.it.localhost/plan/99914819/vetrina.jpg", "url": "https://media.it.localhost/plan/99914819/print.jpg"}], "portions": [], "price": null, "propertyCondition": null, "propertyOwnership": null, "province": "CN", "reference": "", "region": null, "rentContractType": null, "rentToBuy": null, "rooms": 6, "sharedApartmentInformation": null, "shop": null, "subTypology": "Appartamento", "subTypologyId": 14, "surface": "34 m²", "typology": "Appartamento", "typologyId": 7, "zone": null}], "ranking": 44, "searchPosition": null, "statusId": 4, "subTypology": "Appartamento", "subTypologyId": 14, "type": "property", "typology": "Appartamento", "typologyId": 7, "usersStats": {"hidden": 0, "saves": 0, "views": 7}}, {"agencyNotes": null, "auction": null, "brokerageData": null, "category": "Residenziale", "categoryId": 1, "code": "", "contract": "<PERSON><PERSON><PERSON>", "contractId": 1, "created": "30/10/2023", "descriptions": [], "externalMedia": {"hasPhotoPlan": false, "planUrl": null, "videoCodes": [], "videoUrl": null, "virtualTourUrl": ""}, "extraVisibilities": {"searchable": false, "showcase": false, "showcaseExpiration": null, "showcasePremium": false, "star": false, "starExpiration": null, "starPremium": false, "top": false, "topExpiration": null, "topPremium": false}, "favourite": false, "id": 107038151, "images": null, "integrationFlag": 2, "mainImageThumbUrl": "/img/getrix/common/img-placeholder.png", "mainImageUrl": "https://media.it.localhost/image//print.jpg", "modified": "30/04/2024", "performanceInvalidityReason": {"id": 1, "name": "Ad not found"}, "performanceRelativeIndex": null, "performanceRelativeIndexMissingDays": null, "portalUrl": "https://localhost/annunci/107038151?anteprima=s", "price": "€ 123.123", "priceByRequest": false, "project": null, "properties": [{"address": "Via San Giacomo 1", "availability": null, "availableForStudents": null, "bathrooms": null, "cadastralData": [{"address": null, "cadastralClass": null, "cadastralIncome": null, "cadastralType": {"id": 1, "name": "Fabbricato"}, "id": null, "otherCadastralData": "", "ownershipShares": "", "parcel": "", "section": "", "sheet": "", "stapledParcel": null, "stapledSheet": null, "stapledSubordinate": null, "subordinate": ""}], "category": "Residenziale", "categoryId": 1, "city": "Briga Alta", "coWorkingTimetable": null, "composition": null, "condominiumCosts": null, "consistences": null, "country": "IT", "deposit": null, "energyClass": null, "features": null, "floor": null, "freeNow": null, "garage": null, "geographyInformation": {"address": {"number": "1", "street": "Via San Giacomo"}, "city": {"chiefTown": null, "cityMacroZoneType": null, "coordinates": null, "id": "9408", "istatCode": "004031", "name": "Briga Alta", "province": {"id": "CN", "name": "Cuneo", "region": {"country": {"id": "IT", "name": "Italia"}, "id": "pie", "name": "Piemonte"}}}, "coordinates": {"latitude": 44.0826, "longitude": 7.7482}, "flagAddress": 1, "macroZone": {"city": null, "id": 20617, "keyUrl": null, "name": "Piaggia", "nameSn": null}, "showAddress": true}, "heatingCosts": null, "id": null, "images": null, "incomeProducingProperty": null, "industrial": null, "isMain": null, "land": null, "plans": [{"isHq": true, "thumbUrl": "https://media.it.localhost/plan/101829189/vetrina.jpg", "url": "https://media.it.localhost/plan/101829189/print.jpg"}], "portions": [], "price": null, "propertyCondition": null, "propertyOwnership": null, "province": "CN", "reference": "", "region": null, "rentContractType": null, "rentToBuy": null, "rooms": 3, "sharedApartmentInformation": null, "shop": null, "subTypology": "<PERSON><PERSON>tto unifamilia<PERSON>", "subTypologyId": 21, "surface": "123 m²", "typology": "Terracielo o Terratetto", "typologyId": 10, "zone": "Piaggia"}], "ranking": 26, "searchPosition": null, "statusId": 4, "subTypology": "<PERSON><PERSON>tto unifamilia<PERSON>", "subTypologyId": 21, "type": "property", "typology": "Terracielo o Terratetto", "typologyId": 10, "usersStats": {"hidden": 0, "saves": 1, "views": 139}}], "searchFilters": {"agent": null, "category": null, "city": null, "code": null, "contract": null, "country": null, "favourite": null, "mandateFrom": null, "mandateTo": null, "performanceRelativeIndexes": null, "priceFrom": null, "priceTo": null, "province": null, "region": null, "sort": "-modified", "status": "archived", "visibility": "", "zones": null}, "totalCount": 343}, "status": "success"}