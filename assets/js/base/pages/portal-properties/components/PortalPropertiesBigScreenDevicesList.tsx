import React, { useEffect, useState } from 'react';
//@ts-ignore
import { Loader as ListLoader } from '@gx-design/loader';
import gtxLoggedUser from '@getrix/common/js/gtx-logged-user';
import { GxTable } from 'gtx-react/components/GxTable/GxTable';
import { trans } from '@pepita-i18n/babelfish';
import { useSelector } from 'react-redux';
import gtxConstants from '@getrix/common/js/gtx-constants';
import { useModalContext } from '../hooks/useModalContext';
import { Badge } from '@gx-design/badge';
import { IReduxRootState } from '../types/redux';
import { endpoints, PERFORMANCE_PATH } from '../web-api/endpoints';
import { isSearchActive } from '../utils/searchFilters';
import { TableColumn, TableSort } from 'gtx-react/components/GxTable/types';
import { ContractVisibilitySpaces } from 'gtx-react/containers/ContractVisibilitySpaces';
import { PremiumSpaces } from 'gtx-react/containers/PremiumSpaces';
import { PortalPropertiesListAlert } from './PortalPropertiesListAlert';
import { usePortalPropertiesListContext } from '../hooks/usePortalPropertiesListContext';
import { getPropertyQualityDetailsApi } from '../web-api/api';
import { useQueryClient } from '@tanstack/react-query';
import { CACHE_CONFIGS, CACHE_QUALITY_DETAILS_KEY_PREFIX } from '../constants';
import {
    IEcommerceProduct,
    IPortalPropertiesListItem,
} from 'types/api/property';
import {
    isTuristicProperty,
    isNotResidentialProperty,
} from 'lib/propertyCategories';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { ucFirst } from 'lib/strings-formatter';
import {
    PREMIUM_VISIBILITY_NAME,
    SECRET_PROPERTY_VISIBILITY_KEY,
} from 'constants/propertyVisibilities';
import { useFeatureIsOn } from '@growthbook/growthbook-react';

export const PortalPropertiesBigScreenDevicesList: React.FC = () => {
    const {
        ecommerceProducts,
        listConfigs,
        listActions,
        listItems,
        renderMapping,
    } = usePortalPropertiesListContext();

    const { trackEvent } = useMixpanelContext();

    const queryClient = useQueryClient();

    const searchFilters = useSelector(
        (data: IReduxRootState) => data.searchFilters
    );

    const propertiesSpaces = useSelector(
        (data: IReduxRootState) => data.propertiesSpaces
    );

    const agency = useSelector((data: IReduxRootState) => data.agency);

    const listLoader = useSelector((data: IReduxRootState) => data.listLoader);

    const { sort } = searchFilters;

    const sortData: TableSort = {
        key: sort ? (sort as string).substring(1) : 'modified',
        direction:
            sort && (sort as string).substring(0, 1) === '-' ? 'DESC' : 'ASC',
    };

    const { showModal } = useModalContext();
    const [clearSelection, setClearSelection] = useState(false);

    useEffect(() => {
        setClearSelection((prevState) => !prevState);
    }, [window.location.search]);

    const isSecretPropertyEnabled = useFeatureIsOn('crm_secret_property');

    const columns = React.useMemo<TableColumn[]>(
        () => [
            {
                key: 'reference',
                header: trans('label.reference'),
                main: true,
                renderCell: renderMapping['reference'],
                sortable: true,
                sortKey: 'code',
            },
            {
                key: 'place',
                header: trans('label.place'),
                renderCell: renderMapping['place'],
                fixedWidth: true,
                sortable: true,
                sortKey: 'city',
            },
            {
                key: 'price',
                header: trans('label.price'),
                renderCell: renderMapping['price'],
                sortable: true,
                sortKey: 'price',
            },
            {
                key: 'visibility',
                header: trans('label.visibility'),
                renderCell: renderMapping['visibility'],
                fixedCellAction: {
                    label: trans('label.promote'),
                    link: (id: IPortalPropertiesListItem['id']) =>
                        endpoints.PROPERTY_EDIT.replace('{step}', '4').replace(
                            '{id}',
                            id
                        ),
                    target: '_blank',
                    action: (id: IPortalPropertiesListItem['id']) =>
                        trackEvent({
                            event: 'properties_promote_listing',
                            extra: {
                                ['property_id']: id,
                                ['list_tab']: ucFirst(
                                    '' + searchFilters.status
                                ),
                            },
                        }),
                    startIcon: 'megaphone',
                },
                sortable: false,
            },
            {
                key: 'quality',
                header: trans('label.quality'),
                className: 'performance-quality-cell',
                asyncCellRender: async (data: IPortalPropertiesListItem) => {
                    if (!data?.id) {
                        return;
                    }

                    // turistic real estates exception
                    if (isTuristicProperty(data)) {
                        return new Promise((_, reject) => reject());
                    }

                    const qualityData = await queryClient.fetchQuery({
                        queryKey: [
                            `${CACHE_QUALITY_DETAILS_KEY_PREFIX}${data.id}`,
                        ],
                        queryFn: () =>
                            getPropertyQualityDetailsApi(data.id).then(
                                (res) => res.data
                            ),
                        ...CACHE_CONFIGS,
                    });

                    const openRankingDetailModal = () => {
                        showModal({
                            isOpen: true,
                            title: `${trans('label.ad_quality_calculation')}: ${
                                data.id
                            }`,
                            type: 'rankingDetailModal',
                            id: data.id,
                        });
                    };

                    return renderMapping['quality'](
                        data,
                        openRankingDetailModal,
                        qualityData,
                        gtxLoggedUser('roles.ROLE_USER_BACKOFFICE')
                    );
                },
                fixedCellAction: {
                    label: trans('label.improve'),
                    action: (id: IPortalPropertiesListItem['id']) => {
                        const property = listItems.find(
                            (item) => item.id === id
                        );

                        trackEvent({
                            event: 'properties_improve_listing',
                            extra: {
                                ['property_id']: id,
                                ['list_tab']: ucFirst(
                                    '' + searchFilters.status
                                ),
                            },
                        });
                        showModal({
                            isOpen: true,
                            title: trans('label.improve_ad_quality'),
                            type: 'improvePropertyQuality',
                            id,
                            propertyData: {
                                id: property.id,
                                ranking: property?.ranking,
                                properties: property.properties,
                            },
                        });
                    },
                    startIcon: 'cockade',
                },
                sortable: true,
                sortKey: 'ranking',
            },
            {
                key: 'performance',
                header: trans('label.performances'),
                renderCell: renderMapping['performance'],
                shouldRenderCellAction: (
                    property: IPortalPropertiesListItem
                ) => {
                    if (
                        isNotResidentialProperty(property) ||
                        !property.performanceRelativeIndex ||
                        !property.zoneSimilarPropertiesPosition
                    ) {
                        return false;
                    }
                    return true;
                },
                fixedCellAction: {
                    label: trans('label.explore'),
                    startIcon: 'podium',
                    customClass: 'explore-cta-promotion',
                    action: (id: IPortalPropertiesListItem['id']) => {
                        trackEvent({
                            event: 'properties_view_performance_listing',
                            extra: {
                                ['property_id']: id,
                                ['list_tab']: ucFirst(
                                    '' + searchFilters.status
                                ),
                            },
                        });

                        window.open(PERFORMANCE_PATH.replace(':id', id));
                    },
                    target: '_blank',
                },
                sortable: true,
                sortKey: 'absoluteIndex',
                visible:
                    Boolean(gtxConstants('PROPERTY_LIST_PERFORMANCE')) &&
                    (agency.isPerformancesModuleOn ||
                        Boolean(gtxLoggedUser('roles.ROLE_USER_BACKOFFICE'))),
            },
            {
                key: 'position',
                header: trans('label.position'),
                renderCell: renderMapping['position'],
                sortable: true,
                sortKey: 'searchPosition',
            },
            {
                key: 'statistics',
                header: trans('label.statistics'),
                renderCell: renderMapping['statistics'],
                sortable: false,
                fixedCellAction: {
                    label: trans('label.compare'),
                    startIcon: 'statistics',
                    customClass: 'explore-cta-promotion',

                    action: (id: IPortalPropertiesListItem['id']) => {
                        trackEvent({
                            event: 'properties_view_stats_listing',
                            extra: {
                                ['property_id']: id,
                                ['list_tab']: ucFirst(
                                    '' + searchFilters.status
                                ),
                            },
                        });

                        window.open(
                            `${PERFORMANCE_PATH.replace(':id', id)}?view=stats`
                        );
                    },
                    target: '_blank',
                },
            },
            {
                key: 'threads',
                header: trans('label.messages'),
                renderCell: renderMapping['threads'],
                sortable: false,
                visible: Boolean(gtxConstants('PROPERTY_LIST_MATCH_COLUMN')),
            },
            {
                key: 'match',
                header: trans('label.matches.page_title'),
                renderCell: renderMapping['match'],
                sortable: false,
                visible: Boolean(gtxConstants('PROPERTY_LIST_MATCH_COLUMN')),
            },
            {
                key: 'date',
                header: trans('label.modified_date'),
                renderCell: renderMapping['date'],
                sortable: true,
                sortKey: 'modified',
            },
        ],
        [listItems]
    );

    const data = {
        items: listItems,
        extraItemFields: ['id', 'status', 'favourite'],
        actions: listActions,
        sortData,
    };

    if (agency.externalPropertyUpdates) {
        data.extraItemFields.push('integrationFlag');
    }

    return (
        <div className="gx-container" style={{ position: 'relative' }}>
            <PortalPropertiesListAlert />
            <div className="gx-visibility-bar">
                <Badge
                    text={PREMIUM_VISIBILITY_NAME}
                    className="visibility-premium"
                />
                <PremiumSpaces
                    spaces={propertiesSpaces}
                    contractId={gtxConstants('CONTRATTO_VENDITA')}
                    label={trans('label.sale')}
                />
                <PremiumSpaces
                    spaces={propertiesSpaces}
                    contractId={gtxConstants('CONTRATTO_AFFITTO')}
                    label={trans('label.rent')}
                />
                {ecommerceProducts &&
                    ecommerceProducts
                        .filter((item: IEcommerceProduct) => item.active)
                        .filter((item: IEcommerceProduct) => {
                            if (
                                item.service === SECRET_PROPERTY_VISIBILITY_KEY
                            ) {
                                return isSecretPropertyEnabled;
                            } else {
                                return true;
                            }
                        })
                        .map((item: IEcommerceProduct) => (
                            <ContractVisibilitySpaces
                                key={`visibility_${item.name}`}
                                visibility={item}
                            />
                        ))}
            </div>
            {listLoader && listLoader.isLoading && <ListLoader />}
            <GxTable
                columns={columns}
                data={data}
                configs={listConfigs}
                actions={listActions}
                clearSelection={clearSelection}
                noInitialItems={!data.items && !isSearchActive(searchFilters)}
            />
        </div>
    );
};
