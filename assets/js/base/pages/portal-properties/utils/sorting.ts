import { SortingDirectionLabel, SortingDirectionSymbol } from 'types/sorting';

export const getSortingDirectionSymbol = (value: SortingDirectionLabel) => {
    if (value === 'DESC') {
        return '-';
    } else if (value === 'ASC') {
        return '+';
    }

    return '';
};

export const getSortingDirection = (value: SortingDirectionSymbol) => {
    if (value === '-') {
        return 'DESC';
    } else if (value === '+') {
        return 'ASC';
    }

    return '';
};
