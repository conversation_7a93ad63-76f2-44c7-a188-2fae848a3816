import {
    QueryCache,
    QueryClient,
    QueryClientProvider,
} from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Store } from 'gtx-react/components';
import { Loader } from '@gx-design/loader';
import {
    REACT_QUERY_DEV_TOOLS_CLOSE_BTN,
    REACT_QUERY_DEV_TOOLS_TOGGLE_BTN,
} from 'gtx-react/constants';
import { NotifyProvider } from '@gx-design/snackbar';
import { createPortal } from 'react-dom';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import { initList } from './actions';
import {
    DefaultTopHeader,
    PortalAuctionsListHeader,
} from './components/PortalAuctionsListHeader';
import { MixpanelProvider } from 'gtx-react/contexts/MixpanelProvider';
import { MIXPANEL_CONFIG } from './constants';
import { AlertProvider } from './containers/AlertProvider';
import { ConfirmDialogProvider } from './containers/ConfirmDialogProvider';
import { DetailPrintProvider } from './containers/DetailPrintProvider';
import { ErrorPage } from './containers/Error';
import { ModalProvider } from './containers/ModalProvider';
import { PortalAuctionsList } from './containers/PortalAuctionsList';
import * as listReducers from './reducers';
import { BASE_PATH } from './web-api/endpoints';
import { GrowthBookProvider } from 'gtx-react/components/GrowthBookProvider';
import { FeaturesReady } from '@growthbook/growthbook-react';

const queryClient = new QueryClient({
    queryCache: new QueryCache({
        onError: (error, query) => {
            if (typeof query.meta?.errorCallback === 'function') {
                query.meta.errorCallback();
            }
        },
    }),
});

const MIXPANEL_ADDITIONAL_DATA = {
    ['property_type']: MIXPANEL_CONFIG.propertyType,
};
// headersx
const DefaultTopHeaderPortal = () =>
    createPortal(<DefaultTopHeader />, document.getElementById('header'));
const ListTopHeaderPortal = () =>
    createPortal(<DefaultTopHeader />, document.getElementById('header'));

// list
const ListContentPortal = () =>
    createPortal(
        <>
            <PortalAuctionsListHeader />
            <QueryClientProvider client={queryClient}>
                <ReactQueryDevtools
                    toggleButtonProps={REACT_QUERY_DEV_TOOLS_TOGGLE_BTN}
                    closeButtonProps={REACT_QUERY_DEV_TOOLS_CLOSE_BTN}
                    initialIsOpen={false}
                />
                <NotifyProvider>
                    <MixpanelProvider
                        additionalData={MIXPANEL_ADDITIONAL_DATA}
                        mixpanelConfig={MIXPANEL_CONFIG}
                    >
                        <GrowthBookProvider>
                            <FeaturesReady timeout={500} fallback={<Loader />}>
                                <ModalProvider>
                                    <AlertProvider>
                                        <ConfirmDialogProvider>
                                            <DetailPrintProvider>
                                                <PortalAuctionsList />
                                            </DetailPrintProvider>
                                        </ConfirmDialogProvider>
                                    </AlertProvider>
                                </ModalProvider>
                            </FeaturesReady>
                        </GrowthBookProvider>
                    </MixpanelProvider>
                </NotifyProvider>
            </QueryClientProvider>
        </>,
        document.getElementById('content')
    );

const LoadingPage = () => (
    <>
        <DefaultTopHeaderPortal />
        <Loader variant="fixed" />
    </>
);

function List() {
    return (
        <Store api={initList} reducers={listReducers} onApiError={ErrorPage}>
            {(loaded) =>
                loaded ? (
                    <>
                        <ListTopHeaderPortal />
                        <ListContentPortal />
                    </>
                ) : (
                    <LoadingPage />
                )
            }
        </Store>
    );
}

function EntryPoint() {
    return (
        <BrowserRouter>
            <Routes>
                <Route path={BASE_PATH} Component={List} />
            </Routes>
        </BrowserRouter>
    );
}

export default EntryPoint;
