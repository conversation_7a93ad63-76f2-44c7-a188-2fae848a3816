import { createActions } from 'redux-actions';
import * as actions from 'gtx-react/actions/list';
import { getInitialListDataApi, getThreadsApi } from '../../web-api';
import { getQueryString } from 'lib/utility';
import { fromEntries, flatten } from 'gtx-react/utils/utility';
import { ThreadsAPIPayload } from '../../types';

export const { quickSearchLoadingStart, quickSearchLoadingEnd } = createActions(
    'QUICK_SEARCH_LOADING_START',
    'QUICK_SEARCH_LOADING_END'
);

export const initList = () => {
    const payload = getQueryString();

    return getInitialListDataApi(payload).then(res =>
        fromEntries(flatten(res.map(Object.entries)))
    );
};

export const paginate = (data: ThreadsAPIPayload) => dispatch => {
    dispatch(actions.paginationStart(data));

    getThreadsApi(data)
        .then(result => {
            dispatch(actions.paginationEnd({ ...result, append: true }));
        })
        .catch(() => {
            dispatch(actions.paginationError());
        });
};
