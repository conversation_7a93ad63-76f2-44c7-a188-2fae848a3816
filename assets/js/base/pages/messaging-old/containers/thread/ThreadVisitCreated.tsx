import * as React from 'react';
import { useSelector } from 'react-redux';
import classNames from 'classnames';
//@ts-ignore
import { Image } from 'gtx-react/components';
import { ThreadMessageFooter } from './ThreadMessageFooter';
import { trans } from '@pepita-i18n/babelfish';
import {
    contentSelector,
    getRealEstateId,
    getRealEstatePortalLink,
    getRealEstateMainThumbUrl,
    getPropertyImagePlaceholder,
    getProperty,
    getProject,
    getContactedPropertyId,
    threadSelector,
    getPropertyPrice,
    getRealEstateReference,
    getProjectPrices,
    getRealEstateStatus,
} from '../../selectors/thread';
import { formatThreadDetailProjectPrice } from '../../utils';
import {
    MessageRealVisitPlan,
    MessageVirtualVisitPlan,
    RealEstateStatus,
    VisitType,
} from '../../types';
import { isVirtualVisitPlanMessage } from '../../types/guards';
import { join } from '@pepita/querystring';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { getAgencyLanguageId } from 'lib/languages';
import { format } from 'date-fns';
import { getLocale } from 'lib/datefns-locales';

type ThreadVisitCreatedProps = {
    hour: string;
    position?: string;
    self: boolean;
    message: MessageRealVisitPlan | MessageVirtualVisitPlan;
    scheduledMessageRef: React.MutableRefObject<any>;
};

const RealEstateDetails = ({
    contactedPropertyId,
    price,
    realEstate,
    content,
    realEstateReference,
}) => {
    const realEstateStatus = getRealEstateStatus(realEstate);
    return (
        <div className="messaging-messageVisitContent__infoProperty">
            <div
                className={classNames('messaging-reInfo', {
                    'messaging-reInfo--visit': true,
                    'messaging-reInfo--disabled':
                        realEstateStatus === RealEstateStatus.DELETED,
                })}
            >
                <Image
                    className="annuncio-image"
                    loading="lazy"
                    src={getRealEstateMainThumbUrl(realEstate)}
                    fallbackSrc={getPropertyImagePlaceholder(content)}
                />
                <div className="messaging-reInfo__content">
                    <div className="messaging-reInfo__rif">
                        <a
                            href={getRealEstatePortalLink(realEstate)}
                            target="_blank"
                        >
                            {realEstateReference
                                ? `${trans(
                                      'label.reference_short'
                                  ).toLocaleUpperCase()} ${realEstateReference}`
                                : contactedPropertyId
                                ? contactedPropertyId
                                : getRealEstateId(realEstate)}
                        </a>
                    </div>
                    <div className="messaging-reInfo__price">{price}</div>
                </div>
            </div>
        </div>
    );
};

export const ThreadVisitCreated = React.memo(
    ({
        position,
        hour,
        self,
        message,
        scheduledMessageRef,
    }: ThreadVisitCreatedProps) => {
        const { visitType, visitDateTime, note } = message.data.visitPlan;
        const locale = getLocale(getAgencyLanguageId());

        let agentLink = null;

        if (isVirtualVisitPlanMessage(message)) {
            agentLink = message.data.visitPlan.agentLink;
            agentLink = join(agentLink, 'iv_source=chat');
        }

        const content = useSelector(contentSelector);
        const thread = useSelector(threadSelector);
        const contactedPropertyId = getContactedPropertyId(thread);
        const realEstate = getProperty(thread)
            ? getProperty(thread)
            : getProject(thread)
            ? getProject(thread)
            : null;
        const realEstateReference = getRealEstateReference(realEstate);
        return (
            <div
                ref={scheduledMessageRef}
                className={classNames(
                    'messaging-message messaging-message--visit messaging-message--self',
                    { 'messaging-message--isFirst': position === 'first' },
                    { 'messaging-message--isLast': position === 'last' }
                )}
            >
                <div className="messaging-messageVisitContent">
                    <div className="messaging-messageVisitContent__icon">
                        <Icon name="calendar" />
                    </div>
                    <div className="messaging-messageVisitContent__info">
                        {visitType === VisitType.VIRTUAL ? (
                            <p>
                                {trans(
                                    'label.scheduled_visit.visit_by_distance_created'
                                )}
                            </p>
                        ) : (
                            <p>
                                {trans(
                                    'label.scheduled_visit.visit_in_person_created'
                                )}
                            </p>
                        )}

                        <div className="messaging-messageVisitContent__infoDate">
                            {format(new Date(visitDateTime), 'EEEE d MMMM', {
                                locale,
                            })}
                            <br></br>
                            {trans('label.hours', '', {
                                capitalize: true,
                            })}{' '}
                            {format(new Date(visitDateTime), 'HH:mm')}
                        </div>

                        {visitType === VisitType.VIRTUAL && agentLink && (
                            <>
                                <Button
                                    className="messaging-messageVisitContent__infoAction"
                                    variant="accent"
                                    as="a"
                                    href={agentLink}
                                    target="_blank"
                                >
                                    {trans('label.participate')}
                                </Button>
                                <div className="messaging-messageVisitContent__infoLink">
                                    {agentLink}
                                </div>
                            </>
                        )}

                        {getProperty(thread) ? (
                            <RealEstateDetails
                                contactedPropertyId={contactedPropertyId}
                                price={getPropertyPrice(thread)}
                                realEstate={realEstate}
                                content={content}
                                realEstateReference={realEstateReference}
                            />
                        ) : getProject(thread) ? (
                            <RealEstateDetails
                                contactedPropertyId={contactedPropertyId}
                                price={formatThreadDetailProjectPrice(
                                    getProjectPrices(thread)
                                )}
                                realEstate={realEstate}
                                content={content}
                                realEstateReference={realEstateReference}
                            />
                        ) : null}

                        {note && (
                            <div className="messaging-messageVisitContent__infoNotes">
                                <span>
                                    {trans('label.additional_agent_notes')}
                                </span>
                                <p>{note}</p>
                            </div>
                        )}
                    </div>
                </div>
                <ThreadMessageFooter
                    hour={hour}
                    self={self}
                    status={'read'}
                    customClassNames="messaging-message__hour messaging-message__hour--visit"
                />
            </div>
        );
    }
);
