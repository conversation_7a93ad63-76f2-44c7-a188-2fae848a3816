import { useSelector, useDispatch } from 'react-redux';
import { Image } from 'gtx-react/components';
import { trans } from '@pepita-i18n/babelfish';
import { setSelectedThread } from '../../actions/list/threads';
import { preferredSuccess } from '../../actions/thread';
import { getSelectedThreads } from '../../selectors/list/selectedThreads';
import { setThreadAsPreferredApi } from '../../web-api';
import {
    getContactFullName,
    getThreadUpdateDate,
    getThreadId,
    getThreadReadingStatus,
    getThreadUnreadMessages,
    getProperty,
    getPropertyPrice,
    getProject,
    getProjectPrices,
    getThreadPreferred,
    getContactPhone,
} from '../../selectors/thread';
import {
    contentSelector,
    getAgencyPageImagePlaceholder,
    getAgencyPageImageRetinaPlaceholder,
} from '../../selectors/list';
import { THREAD_PATH } from '../../web-api/endpoints';
import { ListItemProps, ThreadReadingStatus } from '../../types';
import {
    formatThreadsListProjectPrice,
    getUnreadMessagesLabel,
} from '../../utils';
import useClickWithExcludedElements from 'gtx-react/hooks/useClickWithExcludedElements';
import { useRef } from 'react';
import { ThreadActionsDropdownActive } from './ThreadActionsDropDownActive';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { NotificationBadge } from '@gx-design/notification-badge';
import { ThreadRowStatusIcon } from './ThreadRowStatusIcon';
import { RealEstateCol } from './RealEstateCol';
import { Checkbox } from '@gx-design/checkbox';
import { Tooltip } from '@gx-design/tooltip';
import { ThreadRowActionsDropdownArchived } from './ThreadActionsDropDownArchived';

export const ThreadCard = ({
    thread,
    isArchived,
}: ListItemProps & { isArchived?: boolean }) => {
    const content = useSelector(contentSelector);
    const dispatch = useDispatch();
    const threadReadingStatus = getThreadReadingStatus(thread);
    const unreadMessages = getThreadUnreadMessages(thread);
    const threadPreferred = isArchived ? false : getThreadPreferred(thread);
    const selectedThreads = useSelector(getSelectedThreads);
    const selectedRef = useRef<HTMLDivElement>(null);
    const actionsRef = useRef<HTMLDivElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);

    const setPreferredThread = (threadId: string, isPreferred: boolean) => {
        //non blocking patch for optimistic update
        setThreadAsPreferredApi(threadId, isPreferred);
        dispatch(preferredSuccess(threadId, isPreferred));
    };

    const handlePreferredThread = () => {
        setPreferredThread(thread.id, !threadPreferred);
    };

    const goToDetail = () => {
        return window.open(THREAD_PATH.replace(':id', thread.id), '_blank');
    };

    const onCardClick = useClickWithExcludedElements(
        [selectedRef, actionsRef, dropdownRef],
        goToDetail
    );

    const handleSelectedThread = () => {
        return dispatch(setSelectedThread(thread.id));
    };

    const ActionsDropdown = isArchived
        ? ThreadRowActionsDropdownArchived
        : ThreadActionsDropdownActive;

    return (
        <div
            onClick={onCardClick}
            className={`messaging-card ${
                threadReadingStatus === ThreadReadingStatus.UNREAD
                    ? 'messaging-card--unread'
                    : ''
            }`}
        >
            <div className="messaging-card__topbar">
                <div className="messaging-card__info">
                    <div ref={selectedRef} className="styled-checkbox">
                        <Checkbox
                            checked={selectedThreads.includes(thread.id)}
                            onChange={handleSelectedThread}
                        />
                    </div>
                    <ThreadRowStatusIcon thread={thread} />
                    <div className="messaging-listMessage__contact">
                        <div>
                            {getContactFullName(thread)}{' '}
                            {getContactPhone(thread) ? (
                                <div>{getContactPhone(thread)}</div>
                            ) : null}
                        </div>
                        {threadReadingStatus === ThreadReadingStatus.UNREAD ? (
                            isArchived ? (
                                <Tooltip
                                    position="top"
                                    text={getUnreadMessagesLabel(
                                        unreadMessages
                                    )}
                                >
                                    <NotificationBadge
                                        number={unreadMessages.toString()}
                                    />
                                </Tooltip>
                            ) : (
                                <NotificationBadge
                                    number={unreadMessages.toString()}
                                />
                            )
                        ) : null}
                    </div>
                </div>
                <div>{getThreadUpdateDate(thread)}</div>
            </div>
            <div className="messaging-listMessage__ref">
                {getProperty(thread) ? (
                    <RealEstateCol
                        thread={thread}
                        price={getPropertyPrice(thread)}
                    />
                ) : getProject(thread) ? (
                    <RealEstateCol
                        thread={thread}
                        price={formatThreadsListProjectPrice(
                            getProjectPrices(thread)
                        )}
                    />
                ) : (
                    <>
                        <div className="list__field__thumb">
                            <div className="property-block__photo">
                                <Image
                                    src={getAgencyPageImagePlaceholder(content)}
                                    srcSet={getAgencyPageImageRetinaPlaceholder(
                                        content
                                    )}
                                    loading="lazy"
                                />
                            </div>
                        </div>
                        <div className="generic-content-block truncate messaging-listMessage__refInfo">
                            {trans('label.messaging.from_agency_page')}
                        </div>
                    </>
                )}
            </div>
            <div ref={actionsRef} className="messaging-listMessage__actions">
                <Button
                    as="a"
                    href={THREAD_PATH.replace(':id', getThreadId(thread))}
                >
                    <Icon name="eye" />
                    <span>{trans('label.see')}</span>
                </Button>
                {isArchived ? null : (
                    <Button
                        iconOnly
                        onClick={handlePreferredThread}
                        className={`${
                            threadPreferred ? 'messaging-preferred-active' : ''
                        }`}
                    >
                        <Icon
                            name={threadPreferred ? 'star--active' : 'star'}
                        />
                    </Button>
                )}
                <ActionsDropdown
                    isDesktop={false}
                    thread={thread}
                    dropdownRef={dropdownRef}
                />
            </div>
        </div>
    );
};
