import React from 'react';
import { trans } from '@pepita-i18n/babelfish';
import { TableActions, TableConfigs } from 'gtx-react/components/GxTable/types';
import { Thread, ThreadReadingStatus, FiltersState } from '../types';
import {
    formatThreadsListProjectPrice,
    getThreadById,
    getUnreadMessagesLabel,
    goToDetail,
    isThreadPreferred,
} from '../utils';
import { useDispatch, useSelector } from 'react-redux';
import {
    archiveSuccess, 
    archiveError,
    multipleSelectThreads,
    archiveBulkSuccess,
    restoreSuccess,
    restoreBulkSuccess,
    readSuccess,
    readBulkSuccess,
    preferredSuccess,
} from '../actions/list';
import { 
    getThreadsApi,
    readBulkThreadsApi,
    setThreadActive<PERSON>pi, 
    setThreadArchivedApi, 
    setThreadBulkArchivedApi, 
    setThreadBulkRestore<PERSON><PERSON>,
    setThreadRead<PERSON><PERSON>,
    setThreadAsPreferredApi,
} from '../web-api';
import { useTrackMixpanelEvents } from './useTrackMixpanelEvents';
import { ThreadRowStatusIcon } from '../containers/list/ThreadRowStatusIcon';
import {
    contentSelector,
    filtersSelector,
    getAgencyPageImagePlaceholder,
    getAgencyPageImageRetinaPlaceholder,
    getContactFullName,
    getContactPhone,
    getProject,
    getProjectPrices,
    getProperty,
    getPropertyPrice,
    getThreadUpdateDate,
} from '../selectors/list';
import { Tooltip } from '@gx-design/tooltip';
import { NotificationBadge } from '@gx-design/notification-badge';
import { ThreadRowPreview } from '../containers/list/ThreadRowPreview';
import { RealEstateCol } from '../containers/list/RealEstateCol';
import { Image } from 'gtx-react/components';
import { paginationSelector } from '../../../../commons/gtx-react/selectors/pagination';
import { useThreadsContext } from './useThreadsContext';
import { useNotifyContext } from '@gx-design/snackbar';
import { loadingStart, loadingEnd, paginationEnd } from 'gtx-react/actions/list';

const TABLE_LABELS = {
    singleRowSelected: trans('label.selected'),
    moreRowsSelected: trans('label.selected_plural_2'),
};

export const useTableConfigs = (threads: Thread[]) => {
    const dispatch = useDispatch();
    const { trackThreadMarkedAsRead } = useTrackMixpanelEvents();
    const filters = useSelector(filtersSelector);
    const pagination = useSelector(paginationSelector);
    const content = useSelector(contentSelector);
    const { setDeleteModal } = useThreadsContext();
    const { showNotification } = useNotifyContext();

    const setThreadArchived = (threadId: string) => {
        dispatch(loadingStart());
        //non blocking patch for optimistic update
        setThreadArchivedApi(threadId)
            .then(() => {
                dispatch(loadingEnd());
                showNotification({
                    type: 'success',
                    message: trans('label.messaging.archived.success'),
                });
                dispatch(archiveSuccess(threadId));
            })
            .catch(() => {
                dispatch(loadingEnd());
                showNotification({
                    type: 'error',
                    message: trans('label.messaging.archived.error'),
                });
                dispatch(archiveError());
            });
    };

    const setThreadArchivedBulk = ({
        selectedIdThreads,
        allSelected,
        filters,
        pagination,
    }: {
        selectedIdThreads: Thread['id'][];
        allSelected: boolean;
        filters: FiltersState;
        pagination: any;
    }) => {
        dispatch(loadingStart());

        setThreadBulkArchivedApi(selectedIdThreads)
            .then(() => {
                if (allSelected) {
                    const listParams = {
                        results: pagination.results,
                        page: pagination.page > 1 ? pagination.page - 1 : pagination.page,
                        ...filters,
                    };

                    getThreadsApi(listParams)
                        .then((result) => {
                            dispatch(archiveBulkSuccess(selectedIdThreads));
                            showNotification({
                                type: 'success',
                                message: selectedIdThreads.length > 1 ? trans('label.messaging.archived.success.plural') : trans('label.messaging.archived.success'),
                            });
                            dispatch(paginationEnd(result));
                            dispatch(loadingEnd());
                        })
                        .catch(() => dispatch(loadingEnd()));
                } else {
                    dispatch(archiveBulkSuccess(selectedIdThreads));
                    showNotification({
                        type: 'success',
                        message: selectedIdThreads.length > 1 ? trans('label.messaging.archived.success.plural') : trans('label.messaging.archived.success'),
                    });
                    dispatch(loadingEnd());
                }
                dispatch(multipleSelectThreads([]));
            })
            .catch(() => {
                showNotification({
                    type: 'error',
                    message: selectedIdThreads.length > 1 ? trans('label.messaging.archived.error.plural') : trans('label.messaging.archived.error'),
                });
                dispatch(loadingEnd());
            });
    };

    const setThreadActive = (threadId: string) => {
        dispatch(loadingStart());
        //non blocking patch for optimistic update
        setThreadActiveApi(threadId)
            .then(() => {
                dispatch(loadingEnd());
                dispatch(restoreSuccess(threadId));
                showNotification({
                    type: 'success',
                    message: trans('label.messaging.restored.success'),
                });
            })
            .catch(() => {
                dispatch(loadingEnd());
                showNotification({
                    type: 'error',
                    message: trans('label.messaging.restored.error'),
                });
            });
    };

    const setThreadRestoreBulk = ({
        selectedIdThreads,
        allSelected,
        filters,
        pagination,
    }: {
        selectedIdThreads: Thread['id'][];
        allSelected: boolean;
        filters: FiltersState;
        pagination: any;
    }) => {
        dispatch(loadingStart());

        setThreadBulkRestoreApi(selectedIdThreads)
            .then(() => {
                if (allSelected) {
                    const listParams = {
                        results: pagination.results,
                        page: pagination.page > 1 ? pagination.page - 1 : pagination.page,
                        ...filters,
                    };

                    getThreadsApi(listParams)
                        .then((result) => {
                            dispatch(restoreBulkSuccess(selectedIdThreads));
                            showNotification({
                                type: 'success',
                                message: selectedIdThreads.length > 1 ? trans('label.messaging.restored.success.plural') : trans('label.messaging.restored.success'),
                            });
                            dispatch(paginationEnd(result));
                            dispatch(loadingEnd());
                        })
                        .catch(() => dispatch(loadingEnd()));
                } else {
                    dispatch(restoreBulkSuccess(selectedIdThreads));
                    dispatch(loadingEnd());
                }
                dispatch(multipleSelectThreads([]));
            })
            .catch(() => {
                showNotification({
                    type: 'error',
                    message: selectedIdThreads.length > 1 ? trans('label.messaging.restored.error.plural') : trans('label.messaging.restored.error'),
                });
                dispatch(loadingEnd());
            });
    };

    const setSingleThreadRead = (threadId: string) => {
        dispatch(loadingStart());
        //non blocking patch for optimistic update
        setThreadReadApi(threadId)
            .then(() => {
                dispatch(loadingEnd());
                dispatch(readSuccess(threadId));
                showNotification({
                    type: 'success',
                    message: trans('label.mark_already_read_success'),
                });
                window.dispatchEvent(new CustomEvent('gtx_menu_counter_manager_update_request'));
            })
            .catch(() => {
                dispatch(loadingEnd());
                showNotification({
                    type: 'error',
                    message: trans('label.agency.error_during_operation'),
                });
            });
    };

    const setBulkThreadRead = ({
        selectedIdThreads,
        allSelected,
        filters,
        pagination,
    }: {
        selectedIdThreads: Thread['id'][];
        allSelected: boolean;
        filters: FiltersState;
        pagination: any;
    }) => {
        dispatch(loadingStart());
        //non blocking patch for optimistic update
        readBulkThreadsApi(selectedIdThreads)
            .then(() => {
                if (allSelected) {
                    const listParams = {
                        results: pagination.results,
                        page: pagination.page > 1 ? pagination.page - 1 : pagination.page,
                        ...filters,
                    };

                    getThreadsApi(listParams)
                        .then((result) => {
                            dispatch(readBulkSuccess(selectedIdThreads));
                            showNotification({
                                type: 'success',
                                message: trans('label.mark_already_read_success_plural'),
                            });
                            dispatch(paginationEnd(result));
                            dispatch(loadingEnd());
                        })
                        .catch(() => dispatch(loadingEnd()));
                } else {
                    dispatch(readBulkSuccess(selectedIdThreads));
                    showNotification({
                        type: 'success',
                        message: trans('label.mark_already_read_success_plural'),
                    });
                    dispatch(loadingEnd());
                }
                dispatch(multipleSelectThreads([]));
                window.dispatchEvent(new CustomEvent('gtx_menu_counter_manager_update_request'));
            })
            .catch(() => {
                showNotification({
                    type: 'error',
                    message: trans('label.agency.error_during_operation'),
                });
                dispatch(loadingEnd());
            });
    };

    const setPreferredThread = (threadId: string, isPreferred: boolean) => {
        //non blocking patch for optimistic update
        setThreadAsPreferredApi(threadId, isPreferred);
        dispatch(preferredSuccess(threadId, isPreferred));
    };

    const tableDataCreator = threads.map((thread) => {
        const id = thread.id;
        const favourite = thread.preferred;
        const data = thread;
        let customRowClassName = '';

        if (thread.readStatus === 'unread') {
            customRowClassName = 'messaging-listMessage--unread';
        }

        return {
            id,
            favourite,
            data,
            customRowClassName,
        };
    });

    const renderMapping = {
        status: ({ data }) => {
            return <ThreadRowStatusIcon thread={data} />;
        },
        contact: ({ data }) => {
            return (
                <div className="messaging-listMessage__contact">
                    <div className="messaging-listMessage__contactName">
                        <div>{getContactFullName(data)}</div>
                        {getContactPhone(data) ? (
                            <div>{getContactPhone(data)}</div>
                        ) : null}
                    </div>
                    {data.readStatus === ThreadReadingStatus.UNREAD && (
                        <Tooltip
                            position="top"
                            text={getUnreadMessagesLabel(data.unread)}
                        >
                            <NotificationBadge number={data.unread} />
                        </Tooltip>
                    )}
                </div>
            );
        },
        preview: ({ data }) => {
            return <ThreadRowPreview thread={data} />;
        },
        reference: ({ data }) => {
            return (
                <div className="messaging-listMessage__ref">
                    {getProperty(data) ? (
                        <RealEstateCol
                            thread={data}
                            price={getPropertyPrice(data)}
                        />
                    ) : getProject(data) ? (
                        <RealEstateCol
                            thread={data}
                            price={formatThreadsListProjectPrice(
                                getProjectPrices(data)
                            )}
                        />
                    ) : (
                        <>
                            <div className="list__field__thumb">
                                <div className="property-block__photo">
                                    <Image
                                        src={getAgencyPageImagePlaceholder(
                                            content
                                        )}
                                        srcSet={getAgencyPageImageRetinaPlaceholder(
                                            content
                                        )}
                                        loading="lazy"
                                    />
                                </div>
                            </div>
                            <div className="messaging-listMessage__refInfo">
                                {trans('label.messaging.from_agency_page')}
                            </div>
                        </>
                    )}
                </div>
            );
        },
        date: ({ data }) => {
            return getThreadUpdateDate(data);
        },
    };

    const actionsHandler = (
        id: Thread['id'],
        actionsData: TableActions,
        actionType: keyof TableActions
    ) => {
        if (!actionsData || !actionType || !id || !actionsData[actionType]) {
            return null;
        }

        const thread = getThreadById(threads, id);

        if (!thread) {
            return null;
        }

        if (actionType === 'main') {
            return actionsData[actionType];
        }

        return (actionsData[actionType] as any).filter((action) => {
            if (!action.validFor) {
                return true;
            }

            if (action.validFor?.status) {
                return action.validFor.status.includes(thread.status);
            }

            if (action.validFor?.readStatus) {
                return action.validFor.readStatus.includes(thread.readStatus);
            }

            return true;
        });
    };

    const listConfigs: TableConfigs = {
        itemSelection: true,
        labels: TABLE_LABELS,
        itemActionsHelper: actionsHandler,
        emptyState: {
            text: trans('label.no_results_found'),
            image: '/bundles/base/getrix/common/img/empty-state/empty-state.png',
        },
    };

    const columns = React.useMemo(
        () => [
            {
                key: 'status',
                header: trans('label.status'),
                renderCell: renderMapping['status'],
                sortable: false,
            },
            {
                key: 'contact',
                header: trans('label.contact'),
                renderCell: renderMapping['contact'],
                sortable: false,
                main: true,
            },
            {
                key: 'preview',
                header: trans('label.preview'),
                renderCell: renderMapping['preview'],
                sortable: false,
                className: 'messaging-preview-col',
            },
            {
                key: 'reference',
                header: trans('label.reference'),
                renderCell: renderMapping['reference'],
                sortable: false,
            },
            {
                key: 'date',
                header: trans('label.date'),
                renderCell: renderMapping['date'],
                sortable: false,
            },
        ],
        []
    );

    const listActions: TableActions = {
        firstCell: [
            {
                iconFlag: 'star--active',
                iconUnflag: 'star',
                itemKey: 'favourite',
                action: (id: Thread['id']) =>
                    setPreferredThread(id, !isThreadPreferred(threads, id))
            },
        ],
        main: {
            action: (id: Thread['id']) => goToDetail(id),
        },
        menu: [
            {
                label: trans('label.archive'),
                icon: 'inbox',
                action: (id: Thread['id']) => setThreadArchived(id),
                validFor: {
                    status: ['active'],
                },
            },
            {
                label: trans('label.restore'),
                icon: 'check',
                action: (id: Thread['id']) => setThreadActive(id),
                validFor: {
                    status: ['archived'],
                },
            },
            {
                label: trans('label.remove'),
                icon: 'bin',
                action: (id: Thread['id']) => {
                    setDeleteModal({
                        open: true,
                        threadIds: [id],
                    });
                },
            },
        ],
        quick: [
            {
                label: trans('label.see_details'),
                icon: 'eye',
                action: (id: Thread['id']) => {
                    goToDetail(id);
                },
            },
            {
                label: trans('label.mark_already_read'),
                icon: 'chat-check',
                action: (id: Thread['id']) => {
                    trackThreadMarkedAsRead([id], false);
                    setSingleThreadRead(id);
                },
                validFor: {
                    readStatus: ['unread'],
                },
            },
        ],
        bulk: [
            {
                label: trans('label.mark_already_read'),
                icon: 'chat-check',
                action: (ids: Thread['id'][]) => {
                    trackThreadMarkedAsRead(ids, true);
                    setBulkThreadRead({
                        selectedIdThreads: ids,
                        allSelected: threads.length === ids.length,
                        filters,
                        pagination,
                    });
                },
                validFor: {
                    thereAreSomeMessagesUnread: true,
                },
            },
            {
                label: trans('label.archive'),
                icon: 'inbox',
                action: (ids: Thread['id'][]) => {
                    setThreadArchivedBulk({
                        selectedIdThreads: ids,
                        allSelected: threads.length === ids.length,
                        filters,
                        pagination,
                    });
                },
                validFor: {
                    status: ['active'],
                },
            },
            {
                label: trans('label.restore'),
                icon: 'check',
                action: (ids: Thread['id'][]) => {
                    setThreadRestoreBulk({
                        selectedIdThreads: ids,
                        allSelected: threads.length === ids.length,
                        filters,
                        pagination,
                    });
                },
                validFor: {
                    status: ['archived'],
                },
            },
            {
                label: trans('label.remove'),
                icon: 'bin',
                action: (ids: Thread['id'][]) => {
                    setDeleteModal({
                        open: true,
                        threadIds: ids,
                    });
                },
            },
        ],
        sorting: null,
    };

    const tableData = {
        items: tableDataCreator,
        extraItemFields: ['id', 'status', 'favourite'],
    };

    return { tableData, listConfigs, columns, listActions };
};
