import { Message, ParticipantType, Thread } from "../../types"

/**
 * This func will create a compatible message with a data.phoneReply attribute 
 * used to inject in the messages list a phone reply CTA or REPLIED box
 * 
 * @param thread 
 * @param type 
 * @returns 
 */
export const createPhoneReplyMockMessage = (thread: Thread, type: 'cta' | 'replied'): Message => {
    const agentUuids = thread.participants.filter(itm => itm.type === ParticipantType.agency).map(itm => itm.uuid)

    return {
        authorUuid: agentUuids.length > 0 ? agentUuids[0] : null,
        createdAt: thread.additionalInfo?.answeredByPhone || thread.messages[thread.messages.length - 1].createdAt,
        data: {
            phoneReply: type,
            property: null,
            visitPlan: null,
            visitRequest: null
        },
        id: -1,
        status: null,
        textHtml: null,
        textPlain: null
    }
}
