import { Thread } from "../types";

export const BASE_PATH = '/messaggi';
export const LIST_PATH = `${BASE_PATH}/lista`;
export const THREAD_PATH = `${BASE_PATH}/:id`;
export const API_BASE_PATH = '/api/messaging';

// export const endpointsOld = {
//     DELETE_THREAD: `${BASE_PATH}/:id/thread`,
//     BULK_DELETE_THREADS: `${BASE_PATH}/threads`,
//     SET_THREAD_PREFERRED: `${BASE_PATH}/:id/thread/:preferred-status`,
//     SET_THREAD_ARCHIVED: `${BASE_PATH}/threads/:id/archive`,
//     BULK_THREAD_ARCHIVED: `${BASE_PATH}/threads/archive`,
//     GET_AGENCY_STATS: `${BASE_PATH}/stats`,
//     GET_CONTENT: `${BASE_PATH}/content`,
//     GET_THREAD: `${BASE_PATH}/:id/thread`,
//     GET_THREADS: `${BASE_PATH}/threads`,
//     GET_THREADS_COUNT: `${BASE_PATH}/threads/count`,
//     GET_THREAD_STATS: `${BASE_PATH}/:id/stats`,
//     POST_THREAD: `${BASE_PATH}/:id/thread`,
//     SET_THREAD_READ: `${BASE_PATH}/:id/thread/read`,
//     VISITED: `${BASE_PATH}/visited`,
//     SET_THREAD_ACTIVE:      `${API_BASE_PATH}/threads/:id/activate`,
// };

export const endpoints = {
    setAnsweredByPhone: (threadId: Thread['id']) => `${API_BASE_PATH}/threads/${threadId}/answered-by-phone`,
    SET_THREAD_PREFERRED: `${API_BASE_PATH}/threads/:id/:preferred-status`,
    SET_THREAD_ARCHIVED: `${API_BASE_PATH}/threads/:id/archive`,
    BULK_THREAD_ARCHIVED: `${API_BASE_PATH}/threads/archive`,
    SET_THREAD_ACTIVE: `${API_BASE_PATH}/threads/:id/activate`,
    BULK_SET_THREAD_ACTIVE: `${API_BASE_PATH}/threads/activate`,
    GET_THREADS_COUNT: `${API_BASE_PATH}/threads/count`,
    SET_THREAD_READ: `${API_BASE_PATH}/threads/:id/read`,
    BULK_THREAD_READ: `${API_BASE_PATH}/threads/read`,
    GET_THREAD_STATS: `${API_BASE_PATH}/threads/:id/stats`,
    SET_ALL_MESSAGES_READ: `${API_BASE_PATH}/threads/read-all`,
    //------------------------------------------------------
    BULK_DELETE_THREADS: `${BASE_PATH}/threads`,
    GET_THREADS: `${BASE_PATH}/threads`,
    GET_CONTENT: `${BASE_PATH}/content`,
    GET_THREAD: `${BASE_PATH}/:id/thread`,
    DELETE_THREAD: `${BASE_PATH}/:id/thread`,
    POST_THREAD: `${BASE_PATH}/:id/thread`,
    VISITED: `${BASE_PATH}/visited`,
    GET_AGENCY_STATS: `${BASE_PATH}/stats`,
};
