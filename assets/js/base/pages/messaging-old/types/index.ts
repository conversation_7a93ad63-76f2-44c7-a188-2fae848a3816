import { IRealEstateObj } from '../../../../commons/gtx-react/containers/ImmovisitaScheduleModal/types';
import { ImmovisitaScheduleModalFormFields } from '../../../../commons/gtx-react/containers/ImmovisitaScheduleModal/types/form';

import { VisitType } from '../../../../commons/gtx-react/containers/ImmovisitaScheduleModal/types';
import { EventPayload, SourceType } from '../../../../commons/lib/mixpanel';
export { VisitType } from '../../../../commons/gtx-react/containers/ImmovisitaScheduleModal/types';


export type ThreadAdditionalInfo = {
    answeredByPhone: null | string
}

export type BcMessagingMessage<T, P> = {
    type: T
    payload: P
}

export type BroadcastMsgUpdate = BcMessagingMessage<'update', { thread: Thread }>
export type BroadcastMsgRemove = BcMessagingMessage<'remove', { thread: Thread }>

export type BcMessage = BroadcastMsgUpdate | BroadcastMsgRemove


export type Thread = {
    id: string;
    contactedPropertyId?: number;
    updateDate: string;
    authorUuid: string;
    mode: ThreadMode;
    unread: number;
    total: number;
    participants: Participant[];
    messages: Message[];
    property?: Property;
    project?: Project;
    lastMessage: Message;
    preferred: boolean;
    readStatus: ThreadReadingStatus;
    status: ThreadLogicalStatus;
    additionalInfo?: ThreadAdditionalInfo
};

type IdName = {
    id: number;
    name: string;
};

type GeographyInformation = {
    city: {
        id: number;
        name: string;
        province: IdName;
        cityMacroZoneType?: any;
    };
    macrozone?: any;
    address: {
        street: string;
        number: string;
    };
    coordinates?: any;
    showAddress: boolean;
    printableCity?: string;
    printableAddress?: string;
    printableZone?: string;
};

export type Agent = {
    id: number;
    firstName?: string;
    lastName?: string;
    email?: string;
    profileImageId?: number;
    profileImageUrl: string;
};

export type Property = {
    id: number;
    reference: string;
    typology: any;
    geographyInformation: GeographyInformation;
    agent?: Agent;
    prices: any;
    status: IdName;
    contract: IdName;
    surface: string;
    rooms?: string;
    imageId: number;
    portalLink: string;
    previewLink: string;
    mainThumbUrl: string;
};

export type Project = {
    id: number;
    reference: string;
    typology: any;
    geographyInformation: GeographyInformation;
    agent?: Agent;
    prices: any;
    status: IdName;
    surfaces?: any;
    rooms?: any;
    imageId: number;
    portalLink: string;
    previewLink: string;
    mainThumbUrl: string;
};

export type Details = {
    isSidebarOpen: boolean;
};

export type DetailsState = {
    details: Details;
};

export enum RealEstateStatus {
    DELETED = 'Eliminato',
}

export enum ThreadMode {
    ACTIVE = 'active',
    LOCKED = 'locked',
    PHONE_ONLY = 'phone-only',
}

export enum MessageType {
    TEXT = 'text',
    ATTACHMENT = 'attachment',
    VISIT_REQUEST_IN_PERSON = 'visitRequestInPerson',
    VISIT_REQUEST_VIRTUAL = 'visitRequestVirtual',
    VISIT_PLAN_IN_PERSON = 'visitPlanInPerson',
    VISIT_PLAN_VIRTUAL = 'visitPlanVirtual',
}

export type Attachment = {
    id: string;
    fileName: string;
    url: string;
    size: string;
    mimeType: string;
};

export enum ThreadReadingStatus {
    READ = 'read',
    UNREAD = 'unread',
    REPLIED = 'replied',
}

export enum MessageStatus {
    READ = 'read',
    UNREAD = 'unread',
    SENT = 'sent',
    PENDING = 'pending',
    FAILED = 'failed',
}

export enum ParticipantType {
    private = 'private',
    agency = 'agency',
}

export type Participant = {
    uuid: string;
    type: ParticipantType;
    fullName: string;
    lastRead: string;
    phone?: string;
    email?: string;
};

export type Message = {
    id: number;
    authorUuid: string;
    textPlain: string;
    textHtml: string;
    createdAt: string;
    status: MessageStatus;
    attachments?: Attachment[];
    data: null | MessageData
};

export type MessageData = {
    property: null | MessageRealEstateDataContent,
    visitRequest: null | MessageVisitReqDataContent,
    visitPlan: null | MessageVirtualVisitPlanDataContent | MessageRealVisitPlanDataContent
    /** 
     * dato aggiunto nel frontend per includere nella lista messaggi 
     * una notifica di avvenuta risposta tramite telefono
     * oppure per inserire una CTA alla prima interazione
     * */
    phoneReply?: 'cta' | 'replied'
}

export type MessageVisitReq = MessageWithPosition & {
    data: {
        property: null | MessageRealEstateDataContent,
        visitRequest: MessageVisitReqDataContent,
        visitPlan: null
    }
};

export type MessageRealVisitPlan = MessageWithPosition & {
    data: {
        property: null | MessageRealEstateDataContent,
        visitRequest: null,
        visitPlan: MessageRealVisitPlanDataContent
    }
};

export type MessageVirtualVisitPlan = MessageWithPosition & {
    data: {
        property: null | MessageRealEstateDataContent,
        visitRequest: null,
        visitPlan: MessageVirtualVisitPlanDataContent
    }
};

export type MessageRealEstate = MessageWithPosition & {
    data: {
        property: MessageRealEstateDataContent,
        visitRequest: null | MessageVisitReqDataContent,
        visitPlan: null | MessageVirtualVisitPlanDataContent | MessageRealVisitPlanDataContent
    }
};

export type MessageVisitReqData = {
    visitRequest: MessageVisitReqDataContent;
};

export type MessageVisitReqDataContent = {
    asSoonAsPossible: boolean;
    visitDays: string[];
    atAnyTime: boolean;
    visitTimetables: string[];
    visitType: VisitType;
};

export type MessageRealVisitPlanData = {
    visitPlan: MessageRealVisitPlanDataContent;
};

export type MessageRealVisitPlanDataContent = {
    visitDateTime: string;
    note: string;
    visitType: VisitType.REAL;
};

export type MessageRealEstateData = {
    property: MessageRealEstateDataContent;
};

export type MessageRealEstateDataContent = {
    id: string;
    reference?: string;
    url?: string;
};

export type MessageVirtualVisitPlanData = {
    visitPlan: MessageVirtualVisitPlanDataContent;
};

export type MessageVirtualVisitPlanDataContent = {
    visitDateTime: string;
    guestLink?: string;
    agentLink?: string;
    note: string;
    visitType: VisitType.VIRTUAL;
}

export type MessageWithPosition = Message & { position?: string };

export type ThreadQs = {
    source?: SourceType;
};

export type ListProps = {
    threads: Thread[];
};

export type ListRealEstateProps = {
    thread: Thread;
    price: string;
};

export type FiltersState = {
    code: string;
    fullName: string;
    email: string;
    unread: boolean;
    preferred: boolean;
    status: ThreadLogicalStatus;
};

export type PaginationState = {
    moreResults: number;
    results: number;
    page: number;
    total?: number;
    loading?: boolean;
};

export type ContentState = {
    propertyImagePlaceholder: string;
    propertyImagePlaceholderNoBorder: string;
    agencyPageImagePlaceholder: string;
    agencyPageImageRetinaPlaceholder: string;
    hasScheduledVisitImmovisita: boolean;
};

export type LoaderState = {
    isLoading: boolean;
};

export type ThreadsApiResponse = {
    filters: FiltersState,
    pagination: PaginationState,
    threads: Thread[]
}

export type ListState = {
    loader: LoaderState;
    listLoader: LoaderState;
    notify: any;
    content: ContentState;
    threads: Thread[];
    filters: FiltersState;
    pagination: PaginationState;
    visited: boolean;
    selectedThreads: [string];
    allReadModalOpen: boolean;
};

export type ThreadApiResponse = {
    thread: Thread;
}
export type ThreadState = {
    thread: Thread;
    content: ContentState;
};

export type ThreadsAPIPayload = {
    code?: string;
    fullName?: string;
    unread?: boolean;
    email?: string;
    page?: number;
    status?: ThreadLogicalStatus;
    ids?: string[]
};

export type ThreadsCountAPIPayload = Pick<
    ThreadsAPIPayload,
    'fullName' | 'email'
>;

export type ThreadMessageSendPayload = {
    id: string;
    messageId: number;
    authorUuid: Participant['uuid'];
    textPlain?: string;
    attachments?: File[];
    data?:
    | MessageVisitReqData
    | MessageRealVisitPlanData
    | MessageVirtualVisitPlanData;
};

export type ThreadsListEventPayload = EventPayload & {
    threads_count: number;
    unread_threads_count: number;
    read_threads_count: number;
    replied_threads_count: number;
    ads_related_threads_count: number;
    agency_related_threads_count: number;
    last_received_message_date: string;
    last_replied_message_date: string;
    first_received_message_date: string;
    first_replied_message_date: string;
};

export type ThreadsPaginatePayload = EventPayload &
    Pick<
        ThreadsListEventPayload,
        'threads_count' & 'unread_threads_count' & 'read_threads_count'
    >;

export type ThreadsSearchEventPayload = EventPayload & {
    searched_name?: string;
    searched_email?: string;
    only_favorite?: boolean;
    only_unread?: boolean;
};

export type ThreadOpenedEventPayload = EventPayload & {
    thread_id: string;
    messages_count: number;
    unread_messages_count: number;
    last_received_message_date: string;
    last_replied_message_date: string;
    first_received_message_date: string;
    first_replied_message_date: string;
    agency_attachments_count: number;
    user_attachments_count: number;
};

export type ThreadDetailOpenedEventPayload = EventPayload &
    Pick<
        ThreadOpenedEventPayload,
        'messages_count' &
        'last_received_message_date' &
        'last_replied_message_date' &
        'first_received_message_date' &
        'first_replied_message_date'
    >;

export type ThreadSendEventPayload = EventPayload &
    Pick<ThreadOpenedEventPayload, 'last_received_message_date'> & {
        thread_id: number;
        message_id: number;
        answer_method: string;
        message_attachments_count: number;
        message_documents_count: number;
        message_videos_count: number;
        message_images_count: number;
        message_length: number;
    };

export type ScheduleModalState = {
    open: boolean;
    scheduledVisit: Partial<ImmovisitaScheduleModalFormFields> & {
        realEstateObj?: IRealEstateObj;
    };
    applicant_data?: {
        fullName: string;
        asSoonAsPossible?: boolean;
        time_precerences: string[];
        date_preferences: string[];
    };
    type: VisitType;
};

export type VisitPlannedEventPayload = EventPayload & {
    thread_id: string;
    visit_type: 'in-person visit' | 'remote visit';
    visit_date: string;
    visit_time: string;
    visit_id_listing: number;
    visit_id_agent: number;
    visit_guests_length: number;
    visit_notes_length: number;
};

export interface ListItemProps {
    thread: Thread;
}

export type FiltersProps = {
    isVisible: boolean;
};

export type IListFilterFormState = {
    fullName: string;
    email: string;
    unread: boolean;
    preferred: boolean;
    status: ThreadLogicalStatus;
};

export interface IPostMessageResponse {
    data: {
        message: Message;
        threadStatus: ThreadLogicalStatus;
    };
}

export type ThreadLogicalStatus = 'active' | 'archived' | 'deleted';

export interface IPayloadedAction<T> {
    type: string;
    payload: T;
}
