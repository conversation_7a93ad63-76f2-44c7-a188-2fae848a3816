import { render, screen } from '#tests/react/testing-library-enhanced';
import { describe, expect, it, vi } from 'vitest';
import { MsgScheduleModalPropertyBoxDatePreference } from './MsgScheduleModalPropertyBox';

describe('MsgScheduleModalPropertyBoxDatePreference', () => {
    it('renders the date in the expected format', () => {
        render(
            <MsgScheduleModalPropertyBoxDatePreference rawDate="2024-03-20 16:24:43" />
        );

        expect(screen.getByText('20/03/2024')).toBeInTheDocument();
    });

    it('renders the wrong date passed as argument', () => {
        render(
            <MsgScheduleModalPropertyBoxDatePreference rawDate="invalid date" />
        );

        expect(screen.getByText('invalid date')).toBeInTheDocument();
    });

    /**
     * Should the component be improved to handle the case where the date is undefined?
     */
    it('renders the default date if date is undefined', () => {
        render(
            // @ts-expect-error rawDate is missing on purpose
            <MsgScheduleModalPropertyBoxDatePreference rawDate={undefined} />
        );

        expect(screen.getByTestId('date-preference')).toHaveTextContent('');
    });
});
