import classNames from 'classnames';
import {
    ContentType,
    PropertyMatchesTypes,
    PropertyType,
} from 'lib/REST/types/acquisition-privates';
import { Image } from 'gtx-react/components';
import {
    getPropertyCategory,
    getPropertyCity,
    getPropertyContract,
    getPropertyEditDate,
    getPropertyId,
    getPropertyImage,
    getPropertyImgPlaceholder,
    getPropertyPreviewProxedUrl,
    getPropertyTypology,
    getPropertyZone,
    propertyHasZone,
} from '../../utils/selectors';
import { trans } from '@pepita-i18n/babelfish';
import {
    ActionsItem,
    FeaturesItem,
    InfoItem,
    OutcomeItem,
} from '../TableItems';

export const PropertyCard = ({
    property,
    content,
    matches,
}: {
    property: PropertyType;
    content: ContentType;
    matches: PropertyMatchesTypes;
}) => {
    return (
        <div
            className={classNames([
                'gx-card gx-card--col2',
                property.urlExists && 'disabled',
            ])}
            data-testid="property-card"
        >
            <div className="gx-card__content">
                <div className="property-item">
                    <div className="property-item__photo">
                        <Image
                            src={getPropertyImage(property)}
                            fallbackSrc={getPropertyImgPlaceholder(content)}
                        />
                    </div>
                    <div className="property-item__desc">
                        <div className="property-item__desc__features">
                            <FeaturesItem property={property} />
                        </div>
                        <div className="property-item__desc__geography">
                            <span className="city">
                                {getPropertyCity(property)}
                            </span>
                            {propertyHasZone(property) ? (
                                <>
                                    ,
                                    <span className="district">
                                        {getPropertyZone(property)}
                                    </span>
                                </>
                            ) : null}
                        </div>
                        <div className="property-item__desc__category">
                            <span>{getPropertyContract(property)}</span>,
                            <span>{getPropertyCategory(property)}</span>,
                            <span>{getPropertyTypology(property)}</span>
                        </div>
                    </div>
                </div>
                <div className="property-card__info">
                    <div className="property-card__info__item property-card__info__item--id">
                        <span className="title">
                            {trans('label.reference')}
                        </span>
                        <div className="content content--fixed-height">
                            {!property.urlExists ? (
                                <span>---</span>
                            ) : (
                                <a
                                    className="pointer"
                                    target="_blank"
                                    rel="noreferrer"
                                    onClick={() => {
                                        const propertyProxedUrl =
                                            getPropertyPreviewProxedUrl(
                                                property
                                            );

                                        if (!propertyProxedUrl) {
                                            return;
                                        }

                                        window.open(propertyProxedUrl);
                                    }}
                                >
                                    {getPropertyId(property)}
                                </a>
                            )}
                        </div>
                    </div>
                    <div className="property-card__info__item">
                        <span className="title">{trans('label.date')}</span>
                        <div className="content content--fixed-height">
                            {!property.urlExists ? (
                                <span>---</span>
                            ) : (
                                <>{getPropertyEditDate(property)}</>
                            )}
                        </div>
                    </div>
                    <div className="property-card__info__item property-card__info__item--information">
                        <span className="title">{trans('label.infos')}</span>
                        <InfoItem property={property} />
                    </div>
                    <div className="property-card__info__item">
                        <span className="title">{trans('label.outcome')}</span>
                        <OutcomeItem property={property} content={content} />
                    </div>
                </div>
            </div>
            <div className="gx-card__footer">
                <ActionsItem
                    property={property}
                    content={content}
                    matches={matches}
                />
            </div>
        </div>
    );
};
