import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { importProperty } from 'lib/REST/requests/acquisizione/annunci-privati/properties';
import { createPropertiesQueryOptions } from 'lib/REST/requests/acquisizione/annunci-privati/properties/query-factory';
import { useAppSearchParams } from '../contexts/useAppSearchParams';

export const useImportPropertyMutation = () => {
    const queryClient = useQueryClient();

    const searchParams = useAppSearchParams();

    const { queryKey } = createPropertiesQueryOptions({ query: searchParams });

    const { showNotification } = useNotifyContext();

    return useMutation({
        mutationFn: (propertyId: string) => importProperty({ propertyId }),
        onSuccess: ({ payload }) => {
            queryClient.setQueryData(queryKey, (old) => {
                if (!old) {
                    return undefined;
                }

                return {
                    ...old,
                    properties: old.properties.map((p) =>
                        p.property.id === payload?.propertyId ? { ...p, importedPropertyUrl: payload.detailUrl } : p
                    ),
                };
            });

            if (payload?.editUrl) {
                location.assign(payload.editUrl);
            }
        },
        onError: () => {
            showNotification({
                type: 'error',
                message: trans('label.error_durin_operation'),
            });
        },
    });
};
