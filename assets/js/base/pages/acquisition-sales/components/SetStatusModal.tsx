import { useMemo, useState } from 'react';
import { But<PERSON> } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { ActionList, ActionListItem } from '@gx-design/action-list';
import { IconInput } from '@gx-design/icon-input';
import { Modal } from '@gx-design/modal';
import { trans } from '@pepita-i18n/babelfish';
import { Formik, useFormikContext } from 'formik';
import * as Yup from 'yup';
import { Loader } from '@gx-design/loader';
import { format } from 'date-fns';
import {
    MutationStatusType,
    SaleRequest,
} from 'lib/REST/types/acquisition-sales';
import { currencyFormatter } from 'lib/currencyFormatter';
import { getOutcomeIcon } from '../utils/outcomeIcon';
import { useSuspenseQuery } from '@tanstack/react-query';
import { createOutcomesQueryOptions } from 'lib/REST/requests/api/lookup/sales-requests/query-factory';
import { GxFkTextarea } from 'gtx-react/components/gx-formik';

const setStatusSchema = Yup.object().shape({
    salesRequestId: Yup.string().required(),
    id: Yup.number(),
    outcome: Yup.object()
        .shape({
            value: Yup.number().required(),
            label: Yup.string().required(),
        })
        .required(),
    note: Yup.string().optional().default(''),
});

export type SetStatusSchemaType = Yup.InferType<typeof setStatusSchema>;

const StatusModalFooter = ({
    isDisabled,
    onClose,
}: {
    isDisabled: boolean;
    onClose: () => void;
}) => {
    const { submitForm } = useFormikContext<SetStatusSchemaType>();

    return (
        <div className="gx-form-section">
            <div className="pull-right">
                <Button onClick={onClose} variant="ghost">
                    {trans('label.cancel')}
                </Button>

                <Button
                    variant="accent"
                    disabled={isDisabled}
                    onClick={submitForm}
                >
                    {trans('label.insert')}
                </Button>
            </div>
        </div>
    );
};

const SetStatusModal = ({
    saleRequest,
    isOpen,
    close,
    isPending,
}: {
    saleRequest: SaleRequest;
    isOpen: boolean;
    close: () => void;
    isPending: boolean;
}) => {
    const { values, setFieldValue, resetForm } =
        useFormikContext<SetStatusSchemaType>();

    const { data: outcomesOptions } = useSuspenseQuery({
        ...createOutcomesQueryOptions(),
        select: (data) => data.map((e) => ({ ...e, value: parseInt(e.value) })),
    });

    const currentOutcomeIconData = useMemo(
        () => getOutcomeIcon(values.outcome?.value),
        [values.outcome?.value]
    );

    const [isOutcomeDropdownOpen, setIsOutcomeDropdownOpen] =
        useState<boolean>(false);

    const onClose = () => {
        resetForm();
        close();
    };

    return (
        <Modal
            bodyHeight="maxHeight"
            isOpen={isOpen}
            title={trans('label.sales_requests.status_modal.title', {
                DATE: format(new Date(saleRequest.date), 'dd/MM/yyyy'),
            })}
            onClose={onClose}
            footer={
                <StatusModalFooter
                    isDisabled={isPending || !values?.outcome?.value}
                    onClose={onClose}
                />
            }
        >
            {isPending && <Loader />}
            <div className="request-modal-info">
                <div className="request-modal-info__address">
                    {saleRequest?.geographyInformation?.city?.name} (
                    {saleRequest?.geographyInformation?.city?.province?.id}){' '}
                    {saleRequest?.geographyInformation?.macroZone?.name &&
                        `- ${saleRequest?.geographyInformation?.macroZone?.name?.split(
                            '-'
                        )[1]}`}{' '}
                    - {saleRequest?.geographyInformation?.address}
                </div>
                <div className="request-modal-info__property">
                    {saleRequest?.propertyInfo?.typology?.name}{' '}
                    {saleRequest?.propertyInfo?.surface &&
                        '| ' + saleRequest?.propertyInfo?.surface}{' '}
                    {saleRequest?.prices &&
                        '| ' + currencyFormatter(saleRequest?.prices?.desired)}
                </div>
                <div className="request-modal-info__contact">
                    {saleRequest?.contactInformation?.firstname}{' '}
                    {saleRequest?.contactInformation?.lastname} |{' '}
                    {saleRequest?.contactInformation?.email && (
                        <a
                            href={`mailto: ${saleRequest?.contactInformation?.email}`}
                        >
                            {saleRequest?.contactInformation?.email}
                        </a>
                    )}
                </div>
            </div>
            <div className="row-with-tag">
                <IconInput
                    isVerticalCentered
                    icon={currentOutcomeIconData.icon}
                    variant={currentOutcomeIconData.style}
                >
                    <Button
                        onClick={() =>
                            setIsOutcomeDropdownOpen(!isOutcomeDropdownOpen)
                        }
                    >
                        <span>
                            {values.outcome?.label
                                ? values.outcome?.label
                                : trans(
                                      'label.sales_requests.status_modal.status_empty'
                                  )}
                        </span>
                        <Icon name="arrow-down" />
                    </Button>
                    <div
                        className="gx-dropdown gx-dropdown--bottomLeft"
                        style={{
                            display: isOutcomeDropdownOpen ? 'block' : 'none',
                        }}
                    >
                        <ActionList>
                            {Object.values(outcomesOptions).map(
                                (outcomeItem, index) => {
                                    return (
                                        <ActionListItem
                                            key={`${outcomeItem.label}_key${index}`}
                                            onClick={() => {
                                                setFieldValue(
                                                    'outcome',
                                                    outcomeItem
                                                );
                                                setIsOutcomeDropdownOpen(false);
                                            }}
                                            text={trans(
                                                `db_sales_requests_outcome.id_${outcomeItem.value}`
                                            )}
                                            startElement={
                                                <Icon
                                                    className={`gx-text-${
                                                        getOutcomeIcon(
                                                            outcomeItem.value
                                                        ).style
                                                    }`}
                                                    name={
                                                        getOutcomeIcon(
                                                            outcomeItem.value
                                                        ).icon!
                                                    }
                                                />
                                            }
                                        />
                                    );
                                }
                            )}
                        </ActionList>
                    </div>
                </IconInput>
            </div>
            <div className="row-with-tag row-with-tag--textarea">
                <IconInput isVerticalCentered={false} icon="note">
                    <GxFkTextarea
                        label={trans('label.insert_note')}
                        isLabelVisible={false}
                        id="note"
                        name="note"
                        rows={8}
                    />
                </IconInput>
            </div>
        </Modal>
    );
};

export default ({
    saleRequest,
    isOpen,
    close,
    onSave,
    isPending,
}: {
    saleRequest: SaleRequest;
    isOpen: boolean;
    close: () => void;
    onSave: (data: MutationStatusType) => void;
    isPending: boolean;
}) => {
    return (
        <Formik
            initialValues={{
                salesRequestId: saleRequest.id,
                id: saleRequest.status?.id,
                note: saleRequest?.status?.note || '',
                outcome: {
                    value: saleRequest?.status?.outcome?.id || 0,
                    label: saleRequest?.status?.outcome?.name || '',
                },
            }}
            onSubmit={(data) => onSave(data)}
            enableReinitialize
            validationSchema={setStatusSchema}
        >
            <SetStatusModal
                saleRequest={saleRequest}
                isOpen={isOpen}
                close={close}
                isPending={isPending}
            />
        </Formik>
    );
};
