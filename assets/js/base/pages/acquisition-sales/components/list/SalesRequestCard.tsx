import { useMemo, useState } from 'react';
import { Item } from 'gtx-react/components';
import { trans } from '@pepita-i18n/babelfish';
import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { format } from 'date-fns';
import { SaleRequest } from 'lib/REST/types/acquisition-sales';
import { getMacroZone } from '../../utils/getMacroZone';
import { currencyFormatter } from 'lib/currencyFormatter';
import SetStatusModal from '../SetStatusModal';
import { PreviewButton, StatusItem } from './TableItems';
import { useSaveStatusListMutation } from '../../hooks/list/useSaveStatusListMutation';

export const SalesRequestCard = ({
    saleRequest,
}: {
    saleRequest: SaleRequest;
}) => {
    const {
        id,
        geographyInformation,
        date,
        prices,
        additionalInformation,
        contactInformation,
        propertyInfo,
    } = saleRequest || {};

    const macroZoneName = useMemo(
        () => getMacroZone(geographyInformation),
        [geographyInformation]
    );
    const [isRequestModalOpen, setIsRequestModalOpen] =
        useState<boolean>(false);

    const { mutate, isPending } = useSaveStatusListMutation();

    return (
        <div className="gx-card gx-card--col2" data-testid="sales-request-card">
            <div className="gx-card__content">
                <div className="gx-summary-list">
                    <Item label={trans('label.date')}>
                        {format(new Date(date), 'dd/MM/yyyy')}
                    </Item>
                    <Item label={trans('label.status')}>
                        <StatusItem saleRequest={saleRequest} />
                    </Item>
                    <Item label={trans('label.place')}>
                        <div>
                            {geographyInformation?.city?.name} (
                            {geographyInformation?.city?.province?.id})
                        </div>
                        {macroZoneName && <div>{macroZoneName}</div>}
                        <div>{geographyInformation?.address}</div>
                    </Item>
                    <Item label={trans('label.property')}>
                        <div>{propertyInfo?.typology?.name}</div>
                        <div>{propertyInfo?.surface}</div>
                    </Item>
                    <Item label={trans('label.price')}>
                        <div>
                            <strong>
                                {prices?.desired ? (
                                    <div>
                                        {currencyFormatter(prices.desired)}
                                    </div>
                                ) : (
                                    <div>---</div>
                                )}
                            </strong>
                        </div>
                        <div>
                            {' '}
                            {prices?.lower ? (
                                <div>{currencyFormatter(prices?.lower)}</div>
                            ) : (
                                <div>---</div>
                            )}
                        </div>
                    </Item>
                    <Item label={trans('label.sales_requests.sales_info')}>
                        <div>
                            <strong>
                                {additionalInformation?.plannedSale?.label
                                    ? trans(
                                          additionalInformation.plannedSale
                                              .label
                                      )
                                    : '---'}
                            </strong>
                        </div>
                        <div>
                            {additionalInformation?.owner?.label
                                ? trans(additionalInformation.owner.label)
                                : '---'}
                        </div>
                    </Item>
                    <Item label={trans('label.contact')}>
                        <div>
                            {contactInformation?.firstname}{' '}
                            {contactInformation?.lastname}
                        </div>
                        {contactInformation?.email && (
                            <div>
                                <a
                                    href={`mailto: ${contactInformation?.email}`}
                                >
                                    {contactInformation?.email}
                                </a>
                            </div>
                        )}
                        <div>{contactInformation?.phone}</div>
                    </Item>
                </div>
            </div>
            <div className="gx-card__footer">
                <Button onClick={() => setIsRequestModalOpen(true)}>
                    <Icon name="pencil" />
                    <span>{trans('label.edit')}</span>
                </Button>
                <PreviewButton id={id} />
            </div>
            <SetStatusModal
                saleRequest={saleRequest}
                isOpen={isRequestModalOpen}
                close={() => setIsRequestModalOpen(false)}
                isPending={isPending}
                onSave={(data) => {
                    mutate(data, {
                        onSettled: () => {
                            setIsRequestModalOpen(false);
                        },
                    });
                }}
            />
        </div>
    );
};
