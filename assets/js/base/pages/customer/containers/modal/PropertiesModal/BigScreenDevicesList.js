import React from 'react';
import {
    getPropertyId,
    getPropertyImage,
    getPropertyAddress,
    getPropertyAddressNumber,
    getPropertyContractType,
    getPropertyCity,
    getPropertyProvince,
    getPropertyPrice,
    getPropertyType,
    getPropertyCategory,
    getPropertyDetailUrl,
} from '../../../selectors/customer';
import { StickyListHead, List, ListItem, ListField, ListBody } from 'gtx-react/components';
import { Icon } from '@gx-design/icon';
import { Tooltip } from '@gx-design/tooltip';
import { Alert } from '@gx-design/alert';
import { trans } from '@pepita-i18n/babelfish';
import { Button } from '@gx-design/button';

const NoResults = () => <Alert style="warning">{trans('label.no_associated_properties_to_customer')}</Alert>;
const emptyString = '---';

export const BigScreenDevicesList = ({ properties }) => {
    const propertyRif = (property) => `${trans('label.reference_short')}: ${getPropertyId(property)}`;

    const propertyLocation = (property) => `${getPropertyCity(property)} (${getPropertyProvince(property)})`;

    const propertyAddress = (property) => {
        if (!getPropertyAddress(property)) {
            return '';
        }

        if (getPropertyAddress(property) && !getPropertyAddressNumber(property)) {
            return getPropertyAddress(property);
        }

        return `${getPropertyAddress(property)}, ${getPropertyAddressNumber(property)}`;
    };

    return (
        <>
            {properties.length > 0 ? (
                <List>
                    <StickyListHead>
                        <ListItem>
                            <ListField key="photo" className="text-center">
                                <div className="th-title">{trans('label.photo')}</div>
                            </ListField>
                            <ListField key="info" className="text-center">
                                <div className="th-title">{trans('label.infos')}</div>
                            </ListField>
                            <ListField key="place" className="text-center">
                                <div className="th-title">{trans('label.place')}</div>
                            </ListField>
                            <ListField key="price" className="text-center">
                                <div className="th-title">{trans('label.price')}</div>
                            </ListField>
                            <ListField key="actions" />
                        </ListItem>
                    </StickyListHead>
                    <ListBody>
                        {properties.map((property) => (
                            <ListItem key={getPropertyId(property)}>
                                <ListField key="photo">
                                    <div className="property-block text-center">
                                        <img className="property-block__photo" src={getPropertyImage(property)} />
                                    </div>
                                </ListField>
                                <ListField key="info">
                                    <div className="generic-content-block text-center">
                                        <div>{propertyRif(property)}</div>
                                        <div className="fontw4">{getPropertyContractType(property)}</div>
                                        <div>{getPropertyCategory(property)}</div>
                                        <div>{getPropertyType(property)}</div>
                                    </div>
                                </ListField>
                                <ListField key="place">
                                    <div className="generic-content-block text-center">
                                        <div>{propertyLocation(property)}</div>
                                        <div>{propertyAddress(property)}</div>
                                    </div>
                                </ListField>
                                <ListField key="price">
                                    <div className="generic-content-block text-center">
                                        <div className="fontw4">
                                            {getPropertyPrice(property) ? `${getPropertyPrice(property)}` : emptyString}
                                        </div>
                                    </div>
                                </ListField>
                                <ListField key="actions" className="list__field--actionsGroup text-center">
                                    <Tooltip text={trans('label.displays')}>
                                        <Button iconOnly as="a" href={getPropertyDetailUrl(property)} target="_blank">
                                            <Icon name="eye" />
                                        </Button>
                                    </Tooltip>
                                </ListField>
                            </ListItem>
                        ))}
                    </ListBody>
                </List>
            ) : (
                <NoResults />
            )}
        </>
    );
};
