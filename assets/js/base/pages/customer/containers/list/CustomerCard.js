import React from 'react';
import { Item } from 'gtx-react/components';
import { Actions, Contact } from './CustomerItem';
import { Checkbox } from '@gx-design/checkbox';
import {
    getCustomerFirstName,
    getCustomerLastName,
    getCustomerEmail,
    getCustomerPhone,
    getCustomerSource,
    getCustomerCellPhone,
    getCustomerFax,
    getCustomerType,
    getCustomerId,
} from '../../selectors/customer/customer';
import { connect } from 'react-redux';
import { toggleCustomer } from '../../actions/list/customers';
import { trans } from '@pepita-i18n/babelfish';

const emptyString = '---';

const mapStateToProps = (state) => {
    return {
        types: state.content.types,
        source: state.content.source,
    };
};

const mapDispatchToProps = (dispatch) => ({
    toogleItem(id) {
        dispatch(toggleCustomer(id));
    },
});

export let CustomerCard = connect(
    mapStateToProps,
    mapDispatchToProps
)(({ customer, toogleItem, source, types }) => {
    const customerType = getCustomerType(customer, types);
    const customerSource = getCustomerSource(customer, source);
    const onCheck = () => toogleItem(getCustomerId(customer));

    return (
        <div className="gx-card gx-card--col2">
            <div className="gx-card__content">
                <div className="property-card__check">
                    <Checkbox checked={customer?.selected ?? false} onChange={onCheck} />
                </div>
                <div className="property-item">
                    <div className="property-item__desc">
                        <div>
                            <b>{`${getCustomerFirstName(customer)} ${getCustomerLastName(customer)}`}</b>
                        </div>
                        <div className="property-item__desc__features">
                            <span className="gx-text-ellipsis">
                                {getCustomerEmail(customer) ? getCustomerEmail(customer) : null}
                            </span>
                        </div>
                    </div>
                </div>
                <div className="gx-summary-list gx-summary-list--padding-top" style={{ display: 'inline-block' }}>
                    <Item label={trans('label.contact_details')} mobileFullWidth tabletFullWidth>
                        {getCustomerPhone(customer) || getCustomerCellPhone(customer) || getCustomerFax(customer) ? (
                            <>
                                <Contact
                                    content={getCustomerPhone(customer)}
                                    popoverContent={trans('label.phone')}
                                    isDesktop={false}
                                />
                                <Contact
                                    content={getCustomerCellPhone(customer)}
                                    popoverContent={trans('label.mobile_phone')}
                                    isDesktop={false}
                                />
                                <Contact
                                    content={getCustomerFax(customer)}
                                    popoverContent={trans('label.fax')}
                                    isDesktop={false}
                                />
                            </>
                        ) : (
                            emptyString
                        )}
                    </Item>
                    <Item label={trans('label.type')}>
                        <div>{customerType ? customerType : emptyString}</div>
                    </Item>
                    <Item label={trans('label.source')}>
                        <div>{customerSource ? customerSource : emptyString}</div>
                    </Item>
                </div>
            </div>
            <div className="gx-card__footer">
                <Actions customer={customer} />
            </div>
        </div>
    );
});
