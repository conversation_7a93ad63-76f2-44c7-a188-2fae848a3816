import React, { Fragment, useState } from 'react';
import { useDispatch } from 'react-redux';
import { loadingStart, loadingEnd } from 'gtx-react/actions/list';
import { useNotifyContext } from '@gx-design/snackbar';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PropertiesModal } from '../modal/PropertiesModal';
import { AppointmentsModal } from '../modal/AppointmentsModal';
import { Tooltip } from '@gx-design/tooltip';
import { Icon } from '@gx-design/icon';
import { Dropdown } from '@gx-design/dropdown';
import { ActionList, ActionListItem } from '@gx-design/action-list';
import { CustomerModal } from 'gtx-react/containers/CustomerModal';
import { updateSuccess, deleteEnd } from '../../actions/list/customers';
import { getCustomerId, getCustomerRequestUrl } from '../../selectors/list';
import { endpoints, lookupEndpoints } from '../../web-api/endpoints';
import { getCustomerApi, updateCustomerApi, deleteCustomersApi } from '../../web-api';
import { trans } from '@pepita-i18n/babelfish';
import { Button } from '@gx-design/button';
import { Modal } from '@gx-design/modal';
import gtxConstants from '@getrix/common/js/gtx-constants';
import gtxLoggedUser from '@getrix/common/js/gtx-logged-user';

const MODAL_TYPE = {
    DELETE_CUSTOMER: 'DELETE_CUSTOMER',
    APPOINTMENTS_LIST: 'APPOINTMENTS_LIST',
    PROPERTIES_LIST: 'PROPERTIES_LIST',
};

const queryClient = new QueryClient();

export const Contact = ({ content, isDesktop = true, popoverContent }) => (
    <div>
        <Tooltip text={popoverContent}>
            <div>{content}</div>
        </Tooltip>
    </div>
);

export const ContactForPrint = ({ content, type }) =>
    content ? (
        <div>
            <b>{type}:</b> {content}
        </div>
    ) : null;

const EditButton = ({ customer, isDesktop }) => {
    const dispatch = useDispatch();
    const [editing, setEditing] = useState(false);
    const [isEditCustomerModalOpen, setIsEditCustomerModalOpen] = useState(false);
    const { showNotification } = useNotifyContext();

    const handleEdit = (data, closeModal) => {
        setEditing(true);

        return updateCustomerApi(data)
            .then((result) => {
                dispatch(updateSuccess(result.data));
                closeModal();
                showNotification({
                    type: 'success',
                    message: trans('label.searches.customer.update.success'),
                });
            })
            .catch(() =>
                showNotification({
                    type: 'error',
                    message: trans('label.error_durin_operation'),
                })
            )
            .finally(() => setEditing(false));
    };

    return (
        <>
            <Tooltip text={trans('label.edit')}>
                <Button iconOnly={isDesktop} onClick={() => setIsEditCustomerModalOpen(true)}>
                    <Icon name="pencil" />
                    <span hidden={isDesktop}>{trans('label.edit')}</span>
                </Button>
            </Tooltip>
            <QueryClientProvider client={queryClient}>
                <CustomerModal
                    id={getCustomerId(customer)}
                    getCustomerApi={getCustomerApi}
                    isOpen={isEditCustomerModalOpen}
                    loading={editing}
                    onCloseHandler={() => setIsEditCustomerModalOpen(false)}
                    onSubmitHandler={(data) => handleEdit(data, () => setIsEditCustomerModalOpen(false))}
                    lookupEndpoints={lookupEndpoints}
                />
            </QueryClientProvider>
        </>
    );
};

const ActionsDropdown = ({ customer }) => {
    const dispatch = useDispatch();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [openedModalType, setOpenedModalType] = useState(null);
    const { showNotification } = useNotifyContext();

    const deleteItem = (customer) => {
        const params = getCustomerId(customer);

        dispatch(loadingStart());

        deleteCustomersApi(params)
            .then(() => {
                dispatch(deleteEnd(customer));
                dispatch(loadingEnd());
                showNotification({
                    type: 'success',
                    message: trans('alert.delete_customer_success'),
                });
            })
            .catch(() => {
                dispatch(loadingEnd());
                showNotification({
                    type: 'error',
                    message: trans('label.error_durin_operation'),
                });
            });
    };

    const toggleModal =
        (value, type = null) =>
        () => {
            setIsModalOpen(value);
            setOpenedModalType(type);
        };

    const actionsList = [
        {
            text: trans('label.remove'),
            onClick: toggleModal(true, MODAL_TYPE.DELETE_CUSTOMER),
        },
        {
            text: trans('label.appointments_list'),
            onClick: toggleModal(true, MODAL_TYPE.APPOINTMENTS_LIST),
        },
        {
            text: trans('label.associated_properties_list'),
            onClick: toggleModal(true, MODAL_TYPE.PROPERTIES_LIST),
        },
    ];

    if (getCustomerRequestUrl(customer)) {
        if (gtxLoggedUser('getrixVersion') >= gtxConstants('SEARCHES_GTX_VERSION')) {
            if (customer.email) {
                actionsList.push({
                    text: trans('label.customers.active_searches_list'),
                    href: endpoints.CUSTOMER_ACTIVE_SEARCHES(customer.email),
                    target: '_blank',
                });
            }
        } else {
            actionsList.push({
                text: trans('label.requests_list'),
                href: getCustomerRequestUrl(customer),
                target: '_blank',
            });
        }
    }

    const onConfirmDelete = () => {
        toggleModal(false)();
        deleteItem(customer);
    };

    return (
        <Fragment>
            <Dropdown position="bottomRight" buttonContent={<Icon name="ellipsis" />} buttonIsIconOnly>
                <ActionList>
                    {actionsList.map(({ text, onClick, href, target }, idx) => (
                        <ActionListItem
                            key={`customer-action-${idx}`}
                            text={text}
                            onClick={onClick}
                            href={href}
                            target={target}
                        />
                    ))}
                </ActionList>
            </Dropdown>
            {isModalOpen && openedModalType === MODAL_TYPE.DELETE_CUSTOMER && (
                <Modal
                    isOpen={isModalOpen && openedModalType === MODAL_TYPE.DELETE_CUSTOMER}
                    size="small"
                    title={<div>{trans('label.delete_customer')}</div>}
                    footer="default"
                    labels={{
                        closeLabel: trans('label.cancel'),
                        confirmLabel: trans('label.confirm'),
                    }}
                    onClose={toggleModal(false)}
                    onConfirm={onConfirmDelete}
                >
                    <div>{trans('label.delete_customer_confirm')}</div>
                </Modal>
            )}
            {isModalOpen && openedModalType === MODAL_TYPE.APPOINTMENTS_LIST && (
                <AppointmentsModal
                    isOpen={isModalOpen && openedModalType === MODAL_TYPE.APPOINTMENTS_LIST}
                    customer={customer}
                    onClose={toggleModal(false)}
                />
            )}
            {isModalOpen && openedModalType === MODAL_TYPE.PROPERTIES_LIST && (
                <PropertiesModal
                    isOpen={isModalOpen && openedModalType === MODAL_TYPE.PROPERTIES_LIST}
                    customer={customer}
                    onClose={toggleModal(false)}
                />
            )}
        </Fragment>
    );
};

export const Actions = ({ isDesktop, customer }) => (
    <div className="gx-multiButton gx-multiButton--separeted-xs">
        <EditButton isDesktop={isDesktop} customer={customer} />
        <ActionsDropdown customer={customer} />
    </div>
);
