import React from 'react';
import { CustomerCard } from './CustomerCard';
import { getCustomerId } from '../../selectors/list';
import { Alert } from '@gx-design/alert';
import { trans } from '@pepita-i18n/babelfish';

const NoResults = () => <Alert style="warning">{trans('label.no_customers_to_show')}</Alert>;

export const SmallScreenDevicesList = ({ customers }) => (
    <>
        {customers.length > 0 ? (
            <div className="gx-card-list">
                {customers.map((customer) => (
                    <CustomerCard key={getCustomerId(customer)} customer={customer} />
                ))}
            </div>
        ) : (
            <NoResults />
        )}
    </>
);
