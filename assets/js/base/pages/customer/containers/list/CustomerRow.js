import React from 'react';
import { ListItem, ListField } from 'gtx-react/components';
import {
    getCustomerFirstName,
    getCustomerLastName,
    getCustomerType,
    getCustomerSource,
    getCustomerEmail,
    getCustomerPhone,
    getCustomerCellPhone,
    getCustomerFax,
    getCustomerId,
} from '../../selectors/customer/customer';
import { connect } from 'react-redux';
import { toggleCustomer } from '../../actions/list/customers';
import { Actions, Contact } from './CustomerItem';
import { trans } from '@pepita-i18n/babelfish';
import { Checkbox } from '@gx-design/checkbox';

const emptyString = '---';

const mapDispatchToProps = (dispatch) => ({
    toogleItem(id) {
        dispatch(toggleCustomer(id));
    },
});

const mapStateToProps = (state) => {
    return {
        types: state.content.types,
        source: state.content.source,
    };
};

export const CustomerRow = connect(
    mapStateToProps,
    mapDispatchToProps
)(({ customer, toogleItem, types, source }) => {
    const customerType = getCustomerType(customer, types);
    const customerSource = getCustomerSource(customer, source);
    const onCheck = () => toogleItem(getCustomerId(customer));

    return (
        <ListItem className={customer.selected && 'selected'}>
            <ListField key="selection">
                <Checkbox checked={customer?.selected ?? false} onChange={onCheck} />
            </ListField>
            <ListField key="name">
                <div className="generic-content-block">
                    <div>{`${getCustomerFirstName(customer)} ${getCustomerLastName(customer)}`}</div>
                </div>
            </ListField>
            <ListField key="type">
                <div className="generic-content-block">
                    <div>{customerType ? customerType : emptyString}</div>
                </div>
            </ListField>
            <ListField key="source">
                <div className="generic-content-block">
                    <div>{customerSource ? customerSource : emptyString}</div>
                </div>
            </ListField>
            <ListField key="email">
                <div className="generic-content-block customer-email truncate">
                    {getCustomerEmail(customer) ? (
                        <a href={`mailto:${getCustomerEmail(customer)}`}>{getCustomerEmail(customer)}</a>
                    ) : (
                        emptyString
                    )}
                </div>
            </ListField>
            <ListField key="contacts">
                <div className="generic-content-block" style={{ display: 'inline-block' }}>
                    {getCustomerPhone(customer) || getCustomerCellPhone(customer) || getCustomerFax(customer) ? (
                        <>
                            <Contact content={getCustomerPhone(customer)} popoverContent={trans('label.phone')} />
                            <Contact
                                content={getCustomerCellPhone(customer)}
                                popoverContent={trans('label.mobile_phone')}
                            />
                            <Contact content={getCustomerFax(customer)} popoverContent={trans('label.fax')} />
                        </>
                    ) : (
                        emptyString
                    )}
                </div>
            </ListField>
            <ListField key="actions" className="list__field--actionsGroup">
                <Actions customer={customer} isDesktop />
            </ListField>
        </ListItem>
    );
});
