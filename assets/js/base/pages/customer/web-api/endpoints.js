export const BASE_PATH = '/clienti';
export const ADD_PATH = '/clienti/aggiungi';
const LOOKUP_BASE_PATH = `${BASE_PATH}/lookup`;
const REST_PATH = `/rest`;

export const endpoints = {
    CREATE_CUSTOMER: `${BASE_PATH}/create`,
    DELETE_APPOINTMENT: `${BASE_PATH}/:customerId/appointments/:appointmentId`,
    EDIT_APPOINTMENT: '/v2/agenda/appuntamento?id=:id',
    GET_CUSTOMERS: `${BASE_PATH}/list`,
    GET_CUSTOMER: `${BASE_PATH}/:id`,
    GET_CUSTOMER_APPOINTMENTS: `${BASE_PATH}/:id/appointments`,
    GET_CUSTOMER_PROPERTIES: `${BASE_PATH}/:id/properties`,
    GET_CONTENT: `${BASE_PATH}/content`,
    PDF_CUSTOMERS_LIST: `${REST_PATH}${BASE_PATH}/pdf`,
    UPDATE_CUSTOMER: `${BASE_PATH}/:id/update`,
    CUSTOMER_ACTIVE_SEARCHES: email =>
        `/ricerche/manuali/lista?email=${email}&status=active`,
};

export const lookupEndpoints = {
    GET_TYPES_LOOKUP: `${LOOKUP_BASE_PATH}/types`,
    GET_DOCUMENT_TYPES_LOOKUP: `${LOOKUP_BASE_PATH}/document-types`,
    GET_SOURCES_LOOKUP: `${LOOKUP_BASE_PATH}/sources`,
    GET_CUSTOMER_GEO_NATION_LOOKUP: `${LOOKUP_BASE_PATH}/geography/nation`,
    GET_CUSTOMER_GEO_REGION_LOOKUP: `${LOOKUP_BASE_PATH}/geography/region`,
    GET_CUSTOMER_GEO_PROVINCE_LOOKUP: `${LOOKUP_BASE_PATH}/geography/province`,
    GET_CUSTOMER_GEO_CITY_LOOKUP: `${LOOKUP_BASE_PATH}/geography/city`,
};
