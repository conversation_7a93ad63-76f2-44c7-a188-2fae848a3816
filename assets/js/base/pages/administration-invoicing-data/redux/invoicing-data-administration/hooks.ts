import { useDispatch, useSelector } from 'react-redux';
import { TypedUseSelectorHook } from 'react-redux';
import { InvoicingDataState, InvoicingDataDispatch } from '../store';

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAgencyDispatch: () => InvoicingDataDispatch = useDispatch;
export const useAgencySelector: TypedUseSelectorHook<InvoicingDataState> = useSelector;
