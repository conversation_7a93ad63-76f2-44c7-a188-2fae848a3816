import { useState, useEffect } from 'react';
import {
    getInitialData,
    getEditorWebApi,
    getFloors,
    getFloorLookup,
    getOrientationsLookup,
    getImages,
} from '../web-api';

export function useEditorStartupData(
    idAnnuncio,
    idImmobile = null,
    imgHost = null
) {
    const [data, setData] = useState();

    useEffect(() => {
        Promise.all([
            getInitialData(imgHost, idAnnuncio, idImmobile),
            getFloors(idAnnuncio),
            getFloorLookup(),
            getOrientationsLookup(),
            getImages(imgHost, idAnnuncio, idImmobile),
            getEditorWebApi(imgHost, idAnnuncio, idImmobile),
        ]).then(
            ([
                initialData,
                floors,
                floorLookup,
                orientationLookup,
                images,
                webApi,
                assets,
            ]) => {
                initialData.floors = floors || null;
                initialData.images = images || null;
                setData({
                    initialData,
                    webApi,
                    floorLookup,
                    orientationLookup,
                    assets,
                });
            }
        );
    }, []);

    return data;
}
