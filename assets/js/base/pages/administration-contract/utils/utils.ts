import gtxConstants from '@getrix/common/js/gtx-constants';
import { trans } from '@pepita-i18n/babelfish';
import { FeaturesConfig } from '../redux/contract-admin/slices/featuresConfig';
import {
    SECRET_VISIBILITY_NAME,
    SHOWCASE_VISIBILITY_KEY,
    SHOWCASE_VISIBILITY_NAME,
    STAR_VISIBILITY_KEY,
    STAR_VISIBILITY_NAME,
    TOP_VISIBILITY_KEY,
    TOP_VISIBILITY_NAME,
} from 'constants/propertyVisibilities';

export function extractData(): Record<string, any> {
    try {
        const data = JSON.parse(document.getElementById('data').innerHTML);
        if (data) {
            return { data };
        } else {
            throw new Error('No data found');
        }
    } catch (error) {
        throw new Error(error?.message);
    }
}

export function extractFeaturesConfig(): FeaturesConfig {
    try {
        const unparsed = document.getElementById('feature_toggle.configs').innerText;
        if (!unparsed) {
            throw new Error('Element not found, feature_toggle.configs');
        }
        const parsed = JSON.parse(atob(unparsed));
        return parsed;
    } catch (error) {
        throw new Error(error?.message);
    }
}

// copy-pasted from the old implementation from @getrix/common
export function generateRequestServiceModalTitle({ service, type }: { service: string; type: string }): string {
    let value: string = '';

    switch (type) {
        case gtxConstants('SERVICE_REQUEST_TYPE_ACTIVATION'):
            value =
                service === gtxConstants('SERVICE_PORTAL')
                    ? trans('service_request.export')
                    : trans('service_request.service');
            break;
        case gtxConstants('SERVICE_REQUEST_TYPE_DEACTIVATION'):
            value =
                service === gtxConstants('SERVICE_PORTAL')
                    ? trans('service_request.export.deactivate')
                    : trans('service_request.service.deactivate');
            break;
        case gtxConstants('SERVICE_REQUEST_TYPE_INCREASE_ADV_SPACES'):
            value = trans('service_request.increase_spaces');
            break;
        case gtxConstants('SERVICE_REQUEST_TYPE_INCREASE_VISIBILITY'):
            value = trans('service_request.increase_visibility');
            break;
        case gtxConstants('SERVICE_REQUEST_TYPE_MORE_INFO'):
            value = trans('service_request.more_info');
            break;
    }

    return value;
}

// copy-pasted from the old implementation from @getrix/common
export function generateRequestServiceModalSubject({
    service,
    type,
    extraInfo = null,
}: {
    service: string;
    type: string;
    extraInfo?: Record<string, any>;
}): string {
    const activationString = trans('service_request.activation');
    const activationPortalString = trans('service_request.export');
    const deactivationPortalString = trans('service_request.export.deactivate');
    const increaseAdvSpacesString = trans('service_request.increase_spaces_2');
    const increaseGenericString = trans('service_request.increase');
    const infoString = trans('service_request.more_info');
    let value = '';

    switch (service) {
        case gtxConstants('SERVICE_SITO_AGENZIA'):
            value = `${activationString} "${trans('label.website')}"`;
            break;
        case gtxConstants('SERVICE_PLANIMETRIE_INTERATTIVE'):
            value = `${activationString} "${trans('label.interactive_plans')}"`;
            break;
        case gtxConstants('SERVICE_VT360'):
            value = `${activationString} "${trans('label.virtual_tour_3d')}"`;
            break;
        case gtxConstants('SERVICE_IMMOBILE_GARANTITO'):
            value = `${activationString} "${trans('label.guaranteed_property')}"`;
            break;
        case gtxConstants('SERVICE_ADV_SPACES_ANNUNCI_NUOVE_COSTRUZIONI'):
            value = `${activationString} "${trans('label.service_request.adv_spaces_nc')}"`;
            break;
        case gtxConstants('SERVICE_TELEFONATE_SMART'):
            value = `${activationString} "${gtxConstants('SERVICE_SMART_PHONE')}"`;
            break;
        case gtxConstants('SERVICE_LUXURY_PUBLICATION'):
            value = `${infoString} "${trans('service_request.luxury_publication')}"`;
            break;
        case gtxConstants('SERVICE_ANNUNCI_PRESTIGIO'):
            value = `${
                type === gtxConstants('SERVICE_REQUEST_TYPE_ACTIVATION') ? activationString : increaseAdvSpacesString
            } "${trans('label.prestigious_ads')}"`;
            break;
        case gtxConstants('SERVICE_ANNUNCI_ESTERO'):
            value = `${
                type === gtxConstants('SERVICE_REQUEST_TYPE_ACTIVATION') ? activationString : increaseAdvSpacesString
            } "${trans('label.foreign_channel_ads')}"`;
            break;
        case gtxConstants('SERVICE_IMMOBILIARE'):
            value = `${increaseAdvSpacesString} "${gtxConstants('AD_PORTAL')}"`;
            break;
        case gtxConstants('SERVICE_PORTAL'):
            value = `${
                type === gtxConstants('SERVICE_REQUEST_TYPE_ACTIVATION')
                    ? activationPortalString
                    : deactivationPortalString
            } "${extraInfo?.portalName || ''}"`;
            break;
        case gtxConstants('SERVICE_WEB_MARKETING'):
            value = `${infoString} ${trans('label.on')} "${trans('label.web_marketing_services')}"`;
            break;
        case gtxConstants('SERVICE_AGENCY_VISIBILITY'):
            value = `${increaseGenericString} "${trans('label.agency_visibility')}"`;
            break;
        case SHOWCASE_VISIBILITY_KEY:
            value = `${activationString} "${trans('label.extra_visibility')} ${SHOWCASE_VISIBILITY_NAME}"`;
            break;
        case STAR_VISIBILITY_KEY:
            value = `${activationString} "${trans('label.extra_visibility')} ${STAR_VISIBILITY_NAME}"`;
            break;
        case TOP_VISIBILITY_KEY:
            value = `${activationString} "${trans('label.extra_visibility')} ${TOP_VISIBILITY_NAME}"`;
            break;
        case gtxConstants('SERVICE_POSITION_SEARCH_AD'):
            value = `${infoString} "${trans('label.position_search_ad')}"`;
            break;
        case gtxConstants('SERVICE_AGENDA'):
            value = `${activationString} "${trans('label.agenda')}"`;
            break;
        case gtxConstants('SERVICE_REPORT'):
            value = `${activationString} "${trans('label.real_estate_report')}"`;
            break;
        case gtxConstants('SERVICE_RICHIESTE_VALUTAZIONE'):
            value = `${activationString} "${trans('label.property_valuations')}"`;
            break;
        case gtxConstants('SERVICE_ACQUISIZIONE_PRIVATI'):
            value = `${activationString} "${trans('label.private_properties')}"`;
            break;
        case gtxConstants('SERVICE_MULTINVIO'):
            value = `${activationString} "${trans('label.multisend')}"`;
            break;
        case gtxConstants('SERVICE_YOUDOMUS'):
            value = `${activationString} "${trans('label.service_youdomus')}"`;
            break;
        case gtxConstants('SECRET_PROPERTY_VISIBILITY_KEY'): {
            const actionString =
                type === gtxConstants('SERVICE_REQUEST_TYPE_ACTIVATION') ? activationString : increaseGenericString;
            value = `${actionString} "${SECRET_VISIBILITY_NAME}"`;
            break;
        }
    }

    return value;
}
