import { FC, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { trans } from '@pepita-i18n/babelfish';
import { useFormik } from 'formik';
import { Button } from '@gx-design/button';
import { Input, InputProps } from '@gx-design/input';
import { Textarea } from '@gx-design/textarea';
import { Modal } from '@gx-design/modal';
import gtxConstants from '@getrix/common/js/gtx-constants';
import * as Yup from 'yup';

import { showNotify } from 'gtx-react/rtk/slices/notificationsSlice';
import { Loader } from 'gtx-react/components';
import { sendCustomerServiceRequest } from '../web-api/api';
import {
    generateRequestServiceModalSubject,
    generateRequestServiceModalTitle,
} from '../utils/utils';
import { CustomerServiceRequestDataType } from '../types';
import { GtxLoggedUser } from 'types/gtxLoggedUser';

const GTX_LOGGED_USER: GtxLoggedUser = window?.['gtxLoggedUser'];

const ValidationSchema = Yup.object().shape({
    name: Yup.string().required(trans('label.required_value')),

    telephone: Yup.string()
        .matches(
            new RegExp(gtxConstants('REGEX_PHONE')),
            trans('phone_verification.valid_number.error')
        )
        .required(trans('label.required_value')),

    email: Yup.string()
        .matches(
            new RegExp(gtxConstants('REGEX_EMAIL')),
            trans('register.form.error.mail')
        )
        .required(trans('label.required_value')),
});

// TODO: replace these hooks / inputs with extracted version from commons/gtx-react
const useFormikValidateFieldOnChangeIfError = (
    fieldName: string,
    formikOptions: ReturnType<typeof useFormik>
) => {
    const { validateField, getFieldMeta, getFieldProps } = formikOptions;

    useEffect(() => {
        if (getFieldMeta(fieldName).error) {
            validateField(fieldName);
        }
    }, [getFieldProps(fieldName).value]);
};

type FormikInputProps = InputProps & {
    formikOptions: ReturnType<typeof useFormik>;
};

const FormikInput: FC<FormikInputProps> = ({
    formikOptions,
    ...props
}: FormikInputProps) => {
    useFormikValidateFieldOnChangeIfError(props.name, formikOptions);

    return <Input {...props} />;
};

type RequestServiceButtonModalProps = {
    /**
     * defaults to false. if true, the button will look like a link
     */
    showAsLink?: boolean;
    /**
     * label of the button which opens the modal
     */
    buttonLabel: string;
    /**
     * form hidden field ('activate', 'increase', etc.)
     */
    type: string;
    /**
     * form hidden field, defaults to 'amministrazione'
     */
    section?: string;
    /**
     * form hidden field, took from gtxConstants
     */
    service: string;
    /**
     * form hidden field
     */
    extraInfo?: any;
};

type ServiceRequestFormValuesType = {
    subject: string;
    name: string;
    telephone: string;
    email: string;
    message?: string;
};

// TODO: converts for fomik context usage, replace inputs with their exctracted version
// from commons/gtx-react and extract it to be used in other sections too
export const RequestServiceButtonModal: FC<RequestServiceButtonModalProps> = ({
    showAsLink = false,
    buttonLabel,
    section = gtxConstants('SECTION_ADMINISTRATION'),
    type,
    service,
    extraInfo,
}: RequestServiceButtonModalProps) => {
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const dispatch = useDispatch();

    const subject = generateRequestServiceModalSubject({
        service,
        type,
        extraInfo,
    });

    const formTitle = generateRequestServiceModalTitle({ service, type });

    const formikReturnValues = useFormik({
        initialValues: {
            subject,
            name: `${
                Boolean(GTX_LOGGED_USER?.name) ? GTX_LOGGED_USER.name : ''
            } ${
                Boolean(GTX_LOGGED_USER.surname) ? GTX_LOGGED_USER.surname : ''
            }`,
            telephone: `${GTX_LOGGED_USER?.telephone || ''}`,
            email: `${GTX_LOGGED_USER?.email || ''}`,
            message: '',
        },
        onSubmit: (values: ServiceRequestFormValuesType) => {
            setIsLoading(true);
            const valuesToSend: CustomerServiceRequestDataType = {
                servizio: service,
                sezione: section,
                tipo: type,
                oggetto: values.subject,
                nome: values.name,
                telefono: values.telephone,
                email: values.email,
                messaggio: values?.message,
                extraInfo: JSON.stringify(extraInfo),
            };

            sendCustomerServiceRequest(valuesToSend)
                .then(() => {
                    dispatch(
                        showNotify({
                            type: 'success',
                            message: trans('help.request_sent'),
                        })
                    );
                    setIsModalOpen(false);
                })
                .catch(() =>
                    dispatch(
                        showNotify({
                            type: 'error',
                            message: trans('service_request.modal.errorMsg'),
                        })
                    )
                )
                .finally(() => setIsLoading(false));
        },
        validationSchema: ValidationSchema,
        validateOnChange: false,
        validateOnBlur: false,
        validateOnMount: false,
    });

    const { getFieldProps, errors, handleSubmit, handleReset } =
        formikReturnValues;

    const toggleModal =
        (open: boolean) =>
        (e: any): void => {
            setIsModalOpen(open);
            if (!open) {
                // resetting form when closing..
                handleReset(e);
            }
        };

    const onSubmit = (e: any): void => handleSubmit(e);

    return (
        <>
            {showAsLink ? (
                <a className="text-primary" onClick={toggleModal(true)}>
                    {buttonLabel.toUpperCase()}
                </a>
            ) : (
                <Button onClick={toggleModal(true)}>{buttonLabel}</Button>
            )}
            <Modal
                isOpen={isModalOpen}
                size="large"
                title={formTitle}
                footer={
                    <>
                        <Button
                            onClick={toggleModal(false)}
                            disabled={isLoading}
                            variant="ghost"
                        >
                            {trans('label.cancel')}
                        </Button>
                        <Button
                            variant="accent"
                            onClick={onSubmit}
                            disabled={isLoading}
                            type="submit"
                        >
                            {trans('btn.label.send')}
                        </Button>
                    </>
                }
                onClose={isLoading ? () => null : toggleModal(false)}
            >
                <>
                    <Loader
                        loading={isLoading}
                        fixedOverlay={false}
                        centered={false}
                    />
                    <form onSubmit={handleSubmit}>
                        <div className="gx-row">
                            <div className="gx-col-xs-12">
                                <div className="gx-box-row">
                                    <Input
                                        label={trans('label.subject')}
                                        id="subject"
                                        type="text"
                                        required
                                        disabled
                                        {...getFieldProps('subject')}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="gx-row">
                            <div className="gx-col-xs-12">
                                <div className="gx-box-row">
                                    <FormikInput
                                        label={trans('label.referent')}
                                        id="name"
                                        type="text"
                                        required
                                        {...getFieldProps('name')}
                                        error={errors.name as string}
                                        formikOptions={formikReturnValues}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="gx-row">
                            <div className="gx-col-xs-6">
                                <div className="gx-box-row">
                                    <FormikInput
                                        label={trans('label.phone')}
                                        id="telephone"
                                        type="text"
                                        required
                                        {...getFieldProps('telephone')}
                                        error={errors.telephone as string}
                                        formikOptions={formikReturnValues}
                                    />
                                </div>
                            </div>
                            <div className="gx-col-xs-6">
                                <div className="gx-box-row">
                                    <FormikInput
                                        label={trans('label.mail')}
                                        id="email"
                                        type="email"
                                        required
                                        {...getFieldProps('email')}
                                        error={errors.email as string}
                                        formikOptions={formikReturnValues}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="gx-row">
                            <div className="gx-col-xs-12">
                                <div className="gx-box-row">
                                    <Textarea
                                        label={`${trans(
                                            'label.message'
                                        )} (${trans('label.optional')})`}
                                        id="message"
                                        rows={3}
                                        {...getFieldProps('message')}
                                    />
                                </div>
                            </div>
                        </div>
                    </form>
                </>
            </Modal>
        </>
    );
};
