import { Button } from '@gx-design/button';
import { User<PERSON><PERSON>s, UserForm, UserSubmitButton } from '../components/UserForm';
import { useUserActivationQuery } from '../hooks/queries';
import { trans } from '@pepita-i18n/babelfish';
import { BASE_PATH } from '../web-api/endpoints';
import { Alert } from '@gx-design/alert';
import { ErrorWithCode, goTo, hasTransictionalStatus } from '../utils';
import { useActivateUserMutation } from '../hooks/mutations';
import { useNotifyContext } from '@gx-design/snackbar';

export const SettingsUserActivateView = () => {
    const userActivationQuery = useUserActivationQuery();
    const activateUserMutation = useActivateUserMutation();
    const { showNotification } = useNotifyContext();

    if (
        userActivationQuery.isLoading ||
        userActivationQuery.isError ||
        !userActivationQuery.data
    ) {
        return null;
    }

    return (
        <div className="gx-container gx-container--maxWidth">
            <UserForm
                initialValues={{
                    name: userActivationQuery.data.name,
                    surname: userActivationQuery.data.surname,
                    email: userActivationQuery.data.email,
                    emailConfirmation: userActivationQuery.data.email,
                    role: userActivationQuery.data.role.toString(),
                }}
                onSubmit={(values) => {
                    activateUserMutation.mutate(
                        {
                            cognome: values.surname,
                            email: values.email,
                            nome: values.name,
                            ruolo: Number(values.role),
                            confemail: values.emailConfirmation,
                            id: userActivationQuery.data.id,
                        },
                        {
                            onSuccess: () => {
                                showNotification({
                                    type: 'success',
                                    message: trans(
                                        'userProfile.activateSuccess'
                                    ),
                                });

                                goTo(BASE_PATH);
                            },
                            onError: (err) => {

                                if (err instanceof ErrorWithCode) {
                                    return showNotification({
                                        type: 'error',
                                        message: err.message,
                                    });
                                }

                                showNotification({
                                    type: 'error',
                                    message: trans('userProfile.activateError'),
                                });
                            },
                        }
                    );
                }}
            >
                <>
                    <div className="gx-section gtx-sts-user-form-section">
                        <UserFields />
                    </div>
                    {hasTransictionalStatus(
                        userActivationQuery.data.status
                    ) ? null : (
                        <div className="gx-row">
                            <div className="gx-col-xs-12">
                                <div className="gx-box-row">
                                    <Alert style="info">
                                        {trans('userProfile.activationInfo')}
                                    </Alert>
                                </div>
                            </div>
                        </div>
                    )}
                    <div className="gx-section gx-end-xs">
                        <div className="gx-buttonGroup">
                            <Button as="a" href={BASE_PATH} variant="ghost">
                                {trans('label.cancel')}
                            </Button>
                            <UserSubmitButton
                                isLoading={false}
                                label={trans('label.activate')}
                            />
                        </div>
                    </div>
                </>
            </UserForm>
        </div>
    );
};
