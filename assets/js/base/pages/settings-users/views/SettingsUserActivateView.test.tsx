import { render, screen, waitFor } from '#tests/react/testing-library-enhanced';
import { server } from '#tests/vitest/setup';
import { NotifyProvider } from '@gx-design/snackbar';
import { QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from 'lib/queryClient';
import { HttpResponse, http } from 'msw';
import { describe, expect, it } from 'vitest';
import { UserStatus } from '../constants';
import htmlData from '../dummies/user-activation-html.json';
import { createActivateUserPath } from '../web-api/endpoints';
import { SettingsUserActivateView } from './SettingsUserActivateView';

const formValues = {
    name: htmlData.user.nome,
    surname: htmlData.user.cognome,
    email: htmlData.user.email,
};

const testForm = it.extend<{
    form: {
        getFields(): Promise<{
            role: HTMLElement;
            name: <PERSON><PERSON><PERSON><PERSON>;
            surname: <PERSON><PERSON><PERSON><PERSON>;
            email: HTM<PERSON><PERSON>;
            emailConfirmation: HTMLElement;
            submit: HTMLElement;
        }>;
    };
    // eslint-disable-next-line vitest/valid-title
}>({
    form: async ({ task }, use) => {
        await use({
            async getFields() {
                const role = await screen.findByLabelText(/label.role/); // await is needed because the select is async

                // expect(role).toBeInstanceOf(HTMLSelectElement);

                const name = screen.getByLabelText(/label.name/);
                const surname = screen.getByLabelText(/label.surname/);
                const email = screen.getByLabelText(/label.mail/);
                const emailConfirmation =
                    screen.getByLabelText(/label.confirm_mail/);
                const submit = screen.getByRole('button', {
                    name: 'label.activate',
                });

                return {
                    role,
                    name,
                    surname,
                    email,
                    emailConfirmation,
                    submit,
                };
            },
        });
    },
});

const renderUserActivationView = (data?: Partial<typeof htmlData>) => {
    return render(<SettingsUserActivateView />, {
        wrapper(props) {
            const queryClient = createQueryClient();

            return (
                <NotifyProvider>
                    <QueryClientProvider client={queryClient}>
                        {props.children}
                        <script id="activate-user-data" type="application/json">
                            {JSON.stringify({ ...htmlData, ...data })}
                        </script>
                    </QueryClientProvider>
                </NotifyProvider>
            );
        },
    });
};

describe('SettingsUserActivate', () => {
    testForm('show a prefilled form', async ({ form }) => {
        renderUserActivationView();
        const fields = await form.getFields();

        expect(fields.name).toHaveValue(formValues.name);
        expect(fields.surname).toHaveValue(formValues.surname);
        expect(fields.email).toHaveValue(formValues.email);
        expect(fields.emailConfirmation).toHaveValue(formValues.email);
    });

    testForm(
        'show an info alert when the user has not a transictional status',
        async ({ form }) => {
            renderUserActivationView({
                ...htmlData,
                user: { ...htmlData.user, status: UserStatus.ACTIVE },
            });

            const activationInfo = await screen.findByText(
                /userProfile.activationInfo/
            );

            return expect(activationInfo).toBeInTheDocument();
        }
    );

    it('show an info alert when the user has a transictional status (USER_STATUS_TO_VERIFY)', async () => {
        renderUserActivationView({
            ...htmlData,
            user: { ...htmlData.user, status: UserStatus.TO_VERIFY },
        });

        const activationInfo = screen.queryByText(/userProfile.activationInfo/);

        await waitFor(() => {
            return expect(activationInfo).not.toBeInTheDocument();
        });
    });

    it('show an info alert when the user has a transictional status (USER_STATUS_VERIFYING)', async () => {
        renderUserActivationView({
            ...htmlData,
            user: { ...htmlData.user, status: UserStatus.VERIFYING },
        });

        const activationInfo = screen.queryByText(/userProfile.activationInfo/);

        await waitFor(() => {
            return expect(activationInfo).not.toBeInTheDocument();
        });
    });

    testForm(
        'show an error feedback when the something goes wrong during the activation',
        async ({ form }) => {
            server.use(
                http.put(
                    createActivateUserPath(htmlData.user.idAgente),
                    ({ request }) => {
                        return HttpResponse.error();
                    }
                )
            );

            const { user } = renderUserActivationView();

            const fields = await form.getFields();

            await user.click(fields.submit);

            const errorFeedback = await screen.findByText(
                /userProfile.activateError/
            );

            return expect(errorFeedback).toBeInTheDocument();
        }
    );

    testForm(
        'show an error feedback when the something goes wrong during the activation giving specific error',
        async ({ form }) => {
            const expectedError = 'Some specific error message';
            server.use(
                http.put(
                    createActivateUserPath(htmlData.user.idAgente),
                    ({ request }) => {
                        return HttpResponse.json(
                            {
                                error: expectedError,
                                code: 400,
                            },
                            { status: 400 }
                        );
                    }
                )
            );

            const { user } = renderUserActivationView();

            const fields = await form.getFields();

            await user.click(fields.submit);

            const errorFeedback = await screen.findByText(
                new RegExp(expectedError)
            );

            return expect(errorFeedback).toBeInTheDocument();
        }
    );
    testForm(
        'show a success feedback when the activation is completed successfully',
        async ({ form }) => {
            server.use(
                http.put(
                    createActivateUserPath(htmlData.user.idAgente),
                    ({ request }) => {
                        return HttpResponse.json('ok'); //TODO: check the response
                    }
                )
            );

            const { user } = renderUserActivationView();

            const fields = await form.getFields();

            await user.click(fields.submit);

            const successFeedback = await screen.findByText(
                /userProfile.activateSuccess/
            );

            return expect(successFeedback).toBeInTheDocument();
        }
    );
});
