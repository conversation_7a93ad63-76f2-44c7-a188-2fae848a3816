import { Dropdown } from '@gx-design/dropdown';
import { UserStatus } from '../constants';
import { Icon } from '@gx-design/icon';
import { ActionList, ActionListItem } from '@gx-design/action-list';
import { trans } from '@pepita-i18n/babelfish';
import { useResendActivationEmailMutation } from '../hooks/mutations';
import { User } from 'gtx-react/utils/extractProfileData';
import { useNotifyContext } from '@gx-design/snackbar';

type HeaderActionsDropdownProps = {
    user: User;
    isHolder: boolean;
    isChangeRoleDisabled: boolean;
    onDelete: () => void;
    onDeactivation: () => void;
    onActivation: (status: UserStatus) => void;
};

export const UserHeaderActionsDropdown = (
    props: HeaderActionsDropdownProps
) => {
    const { showNotification } = useNotifyContext();
    const resendActivationEmailMutation = useResendActivationEmailMutation();

    const changeStatusEnabled =
        props.isChangeRoleDisabled ||
        props.user.status === UserStatus.VERIFYING;

    const deleteEnabled =
        props.user.status === UserStatus.VERIFYING || !props.isHolder;

    const resendEnabled = props.user.status === UserStatus.VERIFYING;

    if (changeStatusEnabled && !deleteEnabled && !resendEnabled) {
        return null;
    }

    return (
        <Dropdown
            buttonContent={<Icon name="ellipsis" />}
            buttonIsIconOnly
            position="bottomRight"
        >
            <ActionList>
                {changeStatusEnabled ? (
                    <></>
                ) : (
                    <ActionListItem
                        startElement={
                            <Icon
                                name={
                                    props.user.status !== UserStatus.ACTIVE
                                        ? 'check-circle'
                                        : 'cross-circle'
                                }
                            />
                        }
                        onClick={
                            props.user.status !== UserStatus.ACTIVE
                                ? () => props.onActivation(UserStatus.ACTIVE)
                                : props.onDeactivation
                        }
                        text={
                            props.user.status !== UserStatus.ACTIVE
                                ? trans('label.activate')
                                : trans('label.deactivate')
                        }
                    />
                )}
                {deleteEnabled ? (
                    <ActionListItem
                        onClick={props.onDelete}
                        startElement={<Icon name="bin" />}
                        text={trans('label.remove')}
                    />
                ) : (
                    <></>
                )}
                {resendEnabled ? (
                    <ActionListItem
                        onClick={() => {
                            if (!props.user.idAgente) {
                                return console.warn('User ID is null');
                            }

                            resendActivationEmailMutation.mutate(
                                {
                                    id: props.user.idAgente,
                                },
                                {
                                    onSuccess: () => {
                                        showNotification({
                                            message: trans(
                                                'common.generic_success'
                                            ),
                                            type: 'success',
                                        });
                                    },
                                    onError: () => {
                                        showNotification({
                                            message: trans(
                                                'common.generic_error'
                                            ),
                                            type: 'error',
                                        });
                                    },
                                }
                            );
                        }}
                        startElement={<Icon name="mail" />}
                        text={trans('label.resend_activation_mail')}
                    />
                ) : (
                    <></>
                )}
            </ActionList>
        </Dropdown>
    );
};
