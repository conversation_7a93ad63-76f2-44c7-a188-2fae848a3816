import { trans } from '@pepita-i18n/babelfish';

type UsersCounterProps = {
    activeUsers: number;
    total: number;
};

export const UsersCounter = (props: UsersCounterProps) => {
    return (
        <div className="panel-heading panel-heading--new">
            <div className="row">
                <span id="gtx-sts-active-users-counters" className="title">
                    <span data-testid="active-users">{props.activeUsers}</span>{' '}
                    {trans('label.out_of_2')}{' '}
                    <span data-testid="total-users">{props.total}</span>{' '}
                    {trans('label.active_users')}
                </span>
            </div>
        </div>
    );
};
