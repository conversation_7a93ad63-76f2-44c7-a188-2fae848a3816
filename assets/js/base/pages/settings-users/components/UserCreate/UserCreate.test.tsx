import { render, screen } from '#tests/react/testing-library-enhanced';
import { describe, expect, it } from 'vitest';
import { UserCreate } from './UserCreate';
import { QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from 'lib/queryClient';
import htmlData from '../../dummies/users-list-html.json';
import { NotifyProvider } from '@gx-design/snackbar';
import { GxNavigationBus } from 'lib/gx-navigation-bus';
import { GX_NAVIGATION_ADD_USER } from '../../constants';
import { server } from '#tests/vitest/setup';
import { HttpResponse, http } from 'msw';
import { CREATE_USER_PATH } from '../../web-api/endpoints';

const renderForm = () => {
    return render(<UserCreate />, {
        wrapper({ children }) {
            const queryClient = createQueryClient();

            return (
                <QueryClientProvider client={queryClient}>
                    <NotifyProvider>
                        {children}
                        <script id="users-list-data" type="application/json">
                            {JSON.stringify(htmlData)}
                        </script>
                    </NotifyProvider>
                </QueryClientProvider>
            );
        },
    });
};

const stubUser = {
    surname: 'Doe',
    email: '<EMAIL>',
    name: 'John',
    role: 'db_agent_role.id_3', // 3 is the value of the option
};

describe('UserCreate', () => {
    it('render a form inside a modal', async () => {
        renderForm();

        const notVisibleForm = screen.queryByTestId('user-create-form');

        expect(notVisibleForm).not.toBeInTheDocument();

        // open the modal
        GxNavigationBus.dispatchEvent({ type: GX_NAVIGATION_ADD_USER });

        const visibleForm = await screen.findByTestId('user-create-form');
        expect(screen.getByText(/label.add_user/)).toBeInTheDocument();
        expect(visibleForm).toBeInTheDocument();
    });
    it('should create a user when the form is valid and submitted resetting the form', async () => {
        const { user } = renderForm();

        GxNavigationBus.dispatchEvent({ type: GX_NAVIGATION_ADD_USER });

        const form = await screen.findByTestId('user-create-form');

        const role = await screen.findByLabelText(/label.role/); // await is needed because the select is async
        const name = screen.getByLabelText(/label.name/);
        const surname = screen.getByLabelText(/label.surname/);
        const email = screen.getByLabelText(/label.mail/);
        const emailConfirmation = screen.getByLabelText(/label.confirm_mail/);
        const submit = screen.getByRole('button', { name: 'label.add' });

        await user.selectOptions(role, stubUser.role); // value is 3
        await user.type(name, stubUser.name);
        await user.type(surname, stubUser.surname);
        await user.type(email, stubUser.email);
        await user.type(emailConfirmation, stubUser.email);

        await user.click(submit);

        const successText = await screen.findByText(/userProfile.addSuccess/);

        expect(successText).toBeInTheDocument();
        expect(form).not.toBeInTheDocument();
    });
    it('shoud not create a user and get a specific error', async () => {
        const error = {
            message: 'L\u0027indirizzo email indicato non \u00e8 disponibile.',
            code: 400,
        };

        server.use(
            http.post(CREATE_USER_PATH, () =>
                HttpResponse.json(error, { status: error.code })
            )
        );

        const { user } = renderForm();

        GxNavigationBus.dispatchEvent({ type: GX_NAVIGATION_ADD_USER });

        const form = await screen.findByTestId('user-create-form');

        const role = await screen.findByLabelText(/label.role/); // await is needed because the select is async
        const name = screen.getByLabelText(/label.name/);
        const surname = screen.getByLabelText(/label.surname/);
        const email = screen.getByLabelText(/label.mail/);
        const emailConfirmation = screen.getByLabelText(/label.confirm_mail/);
        const submit = screen.getByRole('button', { name: 'label.add' });

        await user.selectOptions(role, stubUser.role); // value is 3
        await user.type(name, stubUser.name);
        await user.type(surname, stubUser.surname);
        await user.type(email, stubUser.email);
        await user.type(emailConfirmation, stubUser.email);

        await user.click(submit);

        const errorText = await screen.findByText(new RegExp(error.message));

        expect(errorText).toBeInTheDocument();
        expect(form).toBeInTheDocument();
    });
    it('shoud not create a user and get a generic error', async () => {
        server.use(http.post(CREATE_USER_PATH, () => HttpResponse.error()));

        const { user } = renderForm();

        GxNavigationBus.dispatchEvent({ type: GX_NAVIGATION_ADD_USER });

        const form = await screen.findByTestId('user-create-form');

        const role = await screen.findByLabelText(/label.role/); // await is needed because the select is async
        const name = screen.getByLabelText(/label.name/);
        const surname = screen.getByLabelText(/label.surname/);
        const email = screen.getByLabelText(/label.mail/);
        const emailConfirmation = screen.getByLabelText(/label.confirm_mail/);
        const submit = screen.getByRole('button', { name: 'label.add' });

        await user.selectOptions(role, stubUser.role); // value is 3
        await user.type(name, stubUser.name);
        await user.type(surname, stubUser.surname);
        await user.type(email, stubUser.email);
        await user.type(emailConfirmation, stubUser.email);

        await user.click(submit);

        const errorText = await screen.findByText(/messageConfig.genericError/);

        expect(errorText).toBeInTheDocument();
        expect(form).toBeInTheDocument();
    });
});
