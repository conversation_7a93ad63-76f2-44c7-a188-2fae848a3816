import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { EmptyState } from '@gx-design/empty-state';
import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { ContentDropdown } from 'gtx-react/components/ContentDropdown/ContentDropdown';
import { ContentDropdownProvider } from 'gtx-react/components/ContentDropdown/ContentDropdownContext';
import { ContentDropdownContext } from 'gtx-react/components/ContentDropdown/useContentDropdown';
import { ActionList, ActionListItem } from '@gx-design/action-list';
import {
    ColumnPinningState,
    createColumnHelper,
    getCoreRowModel,
    getSortedRowModel,
    SortingState,
    PaginationState,
    OnChangeFn,
} from '@tanstack/react-table';
import {
    CrmTable,
    useReactTableV1,
} from 'gtx-react/components/tanstack-table/v1';
import {
    useColumnOrderState,
    useColumnSizingState,
    useColumnVisibilityState,
    useDefaultOrderingColumns,
    usePaginationSearchParams,
} from 'gtx-react/components/tanstack-table/hooks';
import { getDefaultColumnOrder } from 'gtx-react/components/tanstack-table/helpers';
import { CrmSummary } from 'gtx-react/components/CrmSummary';
import { PaginationBar } from '@gx-design/pagination-bar';
import { ErrorBoundary } from 'react-error-boundary';
import React, { useMemo, useState, useRef } from 'react';
import { USER_STATUS_VERIFYING, UserStatus } from '../../constants';
import { useResendActivationEmailMutation } from '../../hooks/mutations';
import {
    createActivationPath,
    createEditUserPath,
    createSettingsPath,
} from '../../web-api/endpoints';
import { AgencyCell, RoleCell, StatusCell, UserCell } from './cells';
import { UserBasicInfo } from './types';
import { ErrorWithCode, goTo } from '../../utils';
import { usePrivilegeContext } from '../PrivilegeProvider';

type UsersListBigScreenProps = {
    list: UserBasicInfo[];
    onDelete: (id: number) => void;
    pagination: PaginationState;
    onPageChange: OnChangeFn<PaginationState>;
    totalResults: number;
};

// Permission helper function (equivalent to the original itemActionsHelper)
const useItemActionsHelper = (
    isAdmin: boolean,
    isSuperAdmin: boolean,
    usersList: UserBasicInfo[]
) => {
    return (
        userId: number,
        actionType: 'main' | 'quick' | 'menu'
    ): boolean | string[] | null => {
        const user = usersList.find((u) => u.id === userId);

        if (!user) {
            return null;
        }

        switch (actionType) {
            case 'main':
                // Main action not available for super admin or admin with holder
                if (isSuperAdmin || (isAdmin && user.isHolder)) {
                    return null;
                }
                return true;

            case 'quick':
                // Quick actions always available (show preview)
                return true;

            case 'menu': {
                // Menu actions filtering based on user conditions
                const actions: string[] = [];

                // Edit user action - always available
                actions.push('edit');

                // Resend activation email - only for verifying users or unverified email
                if (
                    user.status === UserStatus.VERIFYING ||
                    !user.emailVerified
                ) {
                    actions.push('resend');
                }

                // Delete action - only for non-holders
                if (!user.isHolder) {
                    actions.push('delete');
                }

                return actions.length > 0 ? actions : null;
            }

            default:
                return null;
        }
    };
};

// Action components
const QuickActionsCell = ({
    user,
    itemActionsHelper,
}: {
    user: UserBasicInfo;
    itemActionsHelper: (
        userId: number,
        actionType: 'main' | 'quick' | 'menu'
    ) => any;
}) => {
    const canShowQuickActions = itemActionsHelper(user.id, 'quick');

    if (!canShowQuickActions) {
        return null;
    }

    return (
        <div className="flex gap-1 justify-end">
            <Button
                variant="ghost"
                size="small"
                onClick={(e) => {
                    e.stopPropagation(); // Prevent row click
                    goTo(createSettingsPath(user.id.toString()));
                }}
                title={trans('label.show_preview')}
                className="hover:bg-gray-100"
            >
                <Icon name="eye" />
            </Button>
        </div>
    );
};

const MenuActionsCell = ({
    user,
    onDelete,
    resendActivationEmailMutation,
    showNotification,
    itemActionsHelper,
}: {
    user: UserBasicInfo;
    onDelete: (id: number) => void;
    resendActivationEmailMutation: any;
    showNotification: any;
    itemActionsHelper: (
        userId: number,
        actionType: 'main' | 'quick' | 'menu'
    ) => boolean | string[] | null;
}) => {
    const [isOpen, setIsOpen] = useState(false);

    const availableActions = itemActionsHelper(user.id, 'menu') as
        | string[]
        | null;

    if (!availableActions || availableActions.length === 0) {
        return null;
    }

    const menuActions: Array<{ label: string; action: (e: any) => void }> = [];

    // Edit user action
    if (availableActions.includes('edit')) {
        menuActions.push({
            label: trans('label.edit_user'),
            action: (e: any) => {
                e.stopPropagation(); // Prevent row click
                if (user.status === UserStatus.TO_VERIFY) {
                    goTo(createActivationPath(user.id.toString()));
                } else {
                    goTo(createEditUserPath(user.id.toString()));
                }
            },
        });
    }

    // Resend activation email action
    if (availableActions.includes('resend')) {
        menuActions.push({
            label: trans('label.resend_activation_mail'),
            action: (e: any) => {
                e.stopPropagation(); // Prevent row click
                resendActivationEmailMutation.mutate(
                    { id: user.id },
                    {
                        onSuccess: () => {
                            showNotification({
                                message: trans('common.generic_success'),
                                type: 'success',
                            });
                        },
                        onError: (err: any) => {
                            showNotification({
                                message:
                                    err instanceof ErrorWithCode
                                        ? err.message
                                        : trans('common.generic_error'),
                                type: 'error',
                            });
                        },
                    }
                );
            },
        });
    }

    // Delete action
    if (availableActions.includes('delete')) {
        menuActions.push({
            label: trans('label.remove'),
            action: (e: any) => {
                e.stopPropagation(); // Prevent row click
                onDelete(user.id);
            },
        });
    }

    return (
        <div className="flex justify-end">
            <ContentDropdownContext.Provider value={{ isOpen, setIsOpen }}>
                <ContentDropdown
                    renderAction={({ ref }) => (
                        <Button
                            ref={ref}
                            variant="ghost"
                            size="small"
                            onClick={(e) => {
                                e.stopPropagation(); // Prevent row click
                                setIsOpen(!isOpen);
                            }}
                            className="hover:bg-gray-100"
                            title="More actions"
                        >
                            <Icon name="ellipsis" />
                        </Button>
                    )}
                >
                    <ActionList>
                        {menuActions.map((action, index) => (
                            <ActionListItem
                                key={index}
                                text={action.label}
                                onClick={(e) => {
                                    action.action(e);
                                    setIsOpen(false); // Close dropdown after action
                                }}
                            />
                        ))}
                    </ActionList>
                </ContentDropdown>
            </ContentDropdownContext.Provider>
        </div>
    );
};

const useUserColumns = (props: {
    onDelete: (id: number) => void;
    resendActivationEmailMutation: any;
    showNotification: any;
    itemActionsHelper: (
        userId: number,
        actionType: 'main' | 'quick' | 'menu'
    ) => any;
}) => {
    const columnHelper = createColumnHelper<UserBasicInfo>();

    return [
        columnHelper.accessor('name', {
            id: 'user',
            header: trans('label.user'),
            enableSorting: false,
            size: 270,
            minSize: 200,
            maxSize: 400,
            cell: ({ row }) => (
                <UserCell
                    avatarUrl={row.original.avatarUrl}
                    name={row.original.name}
                    email={row.original.email}
                    phone={row.original.phone}
                />
            ),
            meta: {
                label: trans('label.user'),
            },
        }),
        columnHelper.accessor('role', {
            id: 'role',
            header: trans('label.role'),
            enableSorting: false,
            size: 230,
            minSize: 120,
            maxSize: 230,
            cell: ({ row }) => (
                <RoleCell
                    id={row.original.id}
                    disabled={
                        row.original.isCurrentUser || row.original.isHolder
                    }
                    role={row.original.role}
                />
            ),
            meta: {
                label: trans('label.role'),
            },
        }),
        columnHelper.accessor('status', {
            id: 'status',
            header: trans('label.status'),
            enableSorting: false,
            size: 130,
            minSize: 100,
            maxSize: 180,
            cell: ({ row }) => (
                <StatusCell
                    id={row.original.id}
                    status={row.original.status}
                    disabled={
                        row.original.isCurrentUser ||
                        row.original.isHolder ||
                        row.original.status === USER_STATUS_VERIFYING
                    }
                />
            ),
            meta: {
                label: trans('label.status'),
            },
        }),
        columnHelper.accessor('phone', {
            id: 'phone',
            header: trans('label.phone'),
            enableSorting: false,
            size: 140,
            minSize: 120,
            maxSize: 200,
            cell: ({ row }) => <span>{row.original.phone}</span>,
            meta: {
                label: trans('label.phone'),
            },
        }),
        columnHelper.accessor('agencyPageVisibility', {
            id: 'agencyPage',
            header: trans('label.agency_page'),
            enableSorting: false,
            size: 160,
            minSize: 120,
            maxSize: 200,
            cell: ({ row }) => (
                <AgencyCell
                    id={row.original.id}
                    userStatus={row.original.status}
                    isVisible={row.original.agencyPageVisibility}
                />
            ),
            meta: {
                label: trans('label.agency_page'),
            },
        }),
        // Quick actions column
        columnHelper.display({
            id: 'quickActions',
            header: '',
            enableSorting: false,
            size: 80,
            minSize: 60,
            maxSize: 100,
            cell: ({ row }) => (
                <QuickActionsCell
                    user={row.original}
                    itemActionsHelper={props.itemActionsHelper}
                />
            ),
            meta: {
                label: 'Quick Actions',
            },
        }),
        // Menu actions column
        columnHelper.display({
            id: 'menuActions',
            header: '',
            enableSorting: false,
            size: 80,
            minSize: 60,
            maxSize: 100,
            cell: ({ row }) => (
                <MenuActionsCell
                    user={row.original}
                    onDelete={props.onDelete}
                    resendActivationEmailMutation={
                        props.resendActivationEmailMutation
                    }
                    showNotification={props.showNotification}
                    itemActionsHelper={props.itemActionsHelper}
                />
            ),
            meta: {
                label: 'Menu Actions',
            },
        }),
    ];
};

const useUserListTable = (props: {
    list: UserBasicInfo[];
    onDelete: (id: number) => void;
    pagination: PaginationState;
    onPageChange: OnChangeFn<PaginationState>;
    totalResults: number;
}) => {
    const resendActivationEmailMutation = useResendActivationEmailMutation();
    const { showNotification } = useNotifyContext();
    const { isAdmin, isSuperAdmin } = usePrivilegeContext();

    // Calculate paginated data for client-side pagination
    const startIndex = props.pagination.pageIndex * props.pagination.pageSize;
    const endIndex = startIndex + props.pagination.pageSize;
    const paginatedList = props.list.slice(startIndex, endIndex);

    // Table state management
    const [columnOrder, setColumnOrder] = useColumnOrderState(
        getDefaultColumnOrder([], [])
    );
    const [columnPinningState, setColumnPinningState] =
        useState<ColumnPinningState>({
            left: ['user'],
            right: ['quickActions', 'menuActions'],
        });
    const [columnSizing, setColumnSizing] = useColumnSizingState({});
    const [columnVisibility, setColumnVisibility] = useColumnVisibilityState(
        {}
    );
    const [sorting, setSorting] = useState<SortingState>([]);

    // Create the itemActionsHelper
    const itemActionsHelper = useItemActionsHelper(
        isAdmin,
        isSuperAdmin,
        props.list // Use full list for permission checks
    );

    const columns = useUserColumns({
        onDelete: props.onDelete,
        resendActivationEmailMutation,
        showNotification,
        itemActionsHelper,
    });
    const defaultColumns = useDefaultOrderingColumns(columns);

    // Empty state configuration
    const emptyStateConfig = useMemo(
        () => ({
            text: trans('label.no_results_found'),
            image: '/bundles/base/getrix/common/img/empty-state/empty-state.png',
        }),
        []
    );

    // Create a TanStack table instance
    const table = useReactTableV1({
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        data: paginatedList, // Use paginated data
        columns,
        enableMultiSort: true,
        columnResizeMode: 'onChange',
        enableColumnResizing: true,
        enableDnd: true,
        manualSorting: false,
        manualPagination: true,
        manualFiltering: true,
        pageCount: Math.ceil(props.totalResults / props.pagination.pageSize),
        defaultColumn: {
            size: 150, // Default column size
            minSize: 50, // Minimum column size
            maxSize: 500, // Maximum column size
        },
        state: {
            columnSizing,
            columnOrder: columnOrder,
            columnVisibility: columnVisibility,
            columnPinning: columnPinningState,
            sorting,
            pagination: props.pagination,
        },
        onColumnSizingChange: setColumnSizing,
        onColumnPinningChange: setColumnPinningState,
        onColumnVisibilityChange: setColumnVisibility,
        onColumnOrderChange: setColumnOrder,
        onSortingChange: setSorting,
        onPaginationChange: props.onPageChange,
    });

    return {
        table,
        emptyStateConfig,
        pagination: props.pagination,
        onPageChange: props.onPageChange,
        totalResults: props.totalResults,
    };
};

export const UsersListBigScreen = (props: UsersListBigScreenProps) => {
    const { table, emptyStateConfig, pagination, onPageChange, totalResults } =
        useUserListTable({
            list: props.list,
            onDelete: props.onDelete,
            pagination: props.pagination,
            onPageChange: props.onPageChange,
            totalResults: props.totalResults,
        });

    const tableContainerRef = useRef<HTMLElement>(null);

    return (
        <ContentDropdownProvider
            value={{
                dropdownAnchorElement: tableContainerRef as React.RefObject<HTMLElement>,
                onDropdownChange: undefined,
            }}
        >
            <div ref={tableContainerRef as React.RefObject<HTMLDivElement>}>
                {table.getRowModel().rows.length > 0 && totalResults && (
                    <CrmSummary>
                        <CrmSummary.Results
                            itemsLength={table.getRowModel().rows.length}
                            pageSize={pagination.pageSize}
                            pageIndex={pagination.pageIndex}
                            pageCount={Math.ceil(totalResults / pagination.pageSize)}
                        />
                    </CrmSummary>
                )}
                <CrmTable table={table}>
                <CrmTable.Thead>
                    {table.getHeaderGroups().map(({ id, headers }) => (
                        <CrmTable.Tr key={id}>
                            {headers.map((header) => (
                                <CrmTable.HorizontalDropping
                                    key={header.id}
                                    items={table.getState().columnOrder}
                                >
                                    <CrmTable.Th header={header}>
                                        <CrmTable.Th.PinningControls />
                                        <CrmTable.Th.ResizebleControls />
                                        <CrmTable.Th.DraggableButton />
                                    </CrmTable.Th>
                                </CrmTable.HorizontalDropping>
                            ))}
                        </CrmTable.Tr>
                    ))}
                </CrmTable.Thead>
                <CrmTable.TableBody>
                    {table.getRowModel().rows.map((row) => (
                        <CrmTable.BodyRow row={row} key={row.id}>
                            {row.getVisibleCells().map((cell) => (
                                <CrmTable.HorizontalDropping
                                    key={cell.id}
                                    items={table.getState().columnOrder}
                                >
                                    <CrmTable.Td key={cell.id} cell={cell}>
                                        <CrmTable.Td.ResizebleControls
                                            column={cell.column}
                                        />
                                    </CrmTable.Td>
                                </CrmTable.HorizontalDropping>
                            ))}
                        </CrmTable.BodyRow>
                    ))}
                </CrmTable.TableBody>
                </CrmTable>
                {table.getRowModel().rows.length === 0 && (
                    <EmptyState
                        title={emptyStateConfig.text}
                        img={emptyStateConfig.image}
                    />
                )}
                <ErrorBoundary fallback={null}>
                    <PaginationBar
                        separatorString={trans('label.out_of_2')}
                        resultString={trans('label.results')}
                        currentResults={pagination.pageSize}
                        totalResults={totalResults || 0}
                    >
                        <PaginationBar.DropDown
                            onResultsChange={(value) => {
                                table.resetRowSelection();
                                onPageChange({
                                    pageIndex: 0,
                                    pageSize: value,
                                });
                            }}
                            options={[10, 30, 50]}
                            value={pagination.pageSize}
                            resultString={trans('label.results')}
                        />
                        <PaginationBar.Pager
                            activePage={pagination.pageIndex + 1}
                            maxPagesToShow={3}
                            onPageClick={(page) => {
                                table.resetRowSelection();
                                onPageChange({
                                    pageIndex: page - 1,
                                    pageSize: pagination.pageSize,
                                });
                            }}
                            totalPages={Math.ceil(
                                (totalResults || 0) / pagination.pageSize
                            )}
                        />
                    </PaginationBar>
                </ErrorBoundary>
            </div>
        </ContentDropdownProvider>
    );
};
