import { useQuery } from '@tanstack/react-query';
import { QUERY } from '../constants';
import { getMultiSendPortals } from '../web-api/real';

type UseMultiSendPortalsQueryParams = Parameters<typeof getMultiSendPortals>[0];

export const getMultiSendPortalsQueryKey = (props?: UseMultiSendPortalsQueryParams) => [
    QUERY.EXPORT_PORTALS_LIST,
    props || null,
];

export const useMultiSendPortalsQuery = (props?: UseMultiSendPortalsQueryParams) => {
    return useQuery({
        queryKey: getMultiSendPortalsQueryKey(props),

        queryFn: () =>
            getMultiSendPortals(props).then((res) => {
                if (res.status === 'success') {
                    return res.data;
                }
                throw new Error(res.data.error);
            }),
        placeholderData: (previousData) => previousData,
    });
};

export type UseMultiSendPortalsQueryData = ReturnType<typeof useMultiSendPortalsQuery>['data'];
