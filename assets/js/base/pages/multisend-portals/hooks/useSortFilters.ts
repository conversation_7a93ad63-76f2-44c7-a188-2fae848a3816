import { useCallback, useMemo, useState } from 'react';
import { getQueryString, setQueryString } from 'lib/utility';

const getNonEmptyFilters = (filters: { [key: string]: string }) => {
    const nonEmptyFilters: { [key: string]: string } = {};

    Object.keys(filters).forEach(key => {
        if (filters[key]) {
            nonEmptyFilters[key] = filters[key];
        }
    });

    return nonEmptyFilters;
};

const getFiltersFromQueryString = (sortFilters: string[]) => {
    const { results, ...queryParams } = getQueryString();
    const filters: { [key: string]: string; results: string } = {
        results,
    };

    Object.keys(queryParams).forEach(key => {
        if (sortFilters.includes(key)) {
            filters[key] = queryParams[key];
        }
    });

    return filters;
};

export const usePaginationFilters = (sortFilters: string[]) => {
    const [filters, setFilters] = useState(
        getFiltersFromQueryString(sortFilters)
    );

    const hasFilters = useMemo(() => {
        return (
            Object.keys(filters).filter(f => f !== '' && f !== 'results' && f !== 'page' && f !== 'sort')
                .length > 0
        );
    }, [filters]);

    const onFilter = useCallback(sort => {
        setFilters(({ results }) => {
            const data = {
                results,
                ...getNonEmptyFilters(sort),
            };
            setQueryString(window.location.pathname, data);

            return data;
        });
    }, []);

    const onFilterClear = useCallback(() => {
        setFilters(({ results }) => {
            setQueryString(window.location.pathname, { results });

            return {
                results,
            };
        });
    }, []);

    return {
        filters,
        hasFilters,
        onFilter,
        onFilterClear,
    };
};
