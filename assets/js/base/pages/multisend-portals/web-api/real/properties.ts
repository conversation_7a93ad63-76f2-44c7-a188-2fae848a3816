import { http } from '@pepita/http';
import {
    BacklinkIsValidResponse,
    CategoriesResponse,
    IsSearchablePropertyResponse,
    PortalZonesResponse,
    PropertiesResponse,
    PublishAllPropertyPortalResponse,
    PublishPortalsByPropertiesResponse,
    PublishedPortalsByPropertiesResponse,
    RemainingPropertiesPortalsResponse,
    SetZoneResponse,
    TogglePropertyPortalResponse,
    ZonesByPortalsResponse,
} from '../../types';
import {
    MULTISEND_PROPERTIES_ACTIVATE_ALL_PORTAL_PATH,
    MULTISEND_PROPERTIES_ACTIVATION_PORTAL_PATH,
    MULTISEND_PROPERTIES_BACKLINK_IS_VALID_PATH,
    MULTISEND_PROPERTIES_CATEGORIES_PATH,
    MULTISEND_PROPERTIES_IS_SEARCHABLE_PATH,
    MULTISEND_PROPERTIES_PATH,
    MULTISEND_PROPERTIES_PUBLISHED_PATH,
    MULTISEND_PROPERTIES_REMAINING_SPACE_PATH,
    MULTISEND_PROPERTIES_SET_ZONE_PATH,
    MULTISEND_PROPERTIES_WITHOUT_ZONES_PATH,
    MULTISEND_PROPERTIES_ZONES_BY_PORTALS_PATH,
    MULTISEND_PROPERTIES_PUBLISH_PORTALS_PATH,
} from '../endpoints';

type KeyParams =
    | 'region'
    | 'province'
    | 'city'
    | 'zone'
    | 'code'
    | 'publishStatus'
    | 'sort'
    | 'category'
    | 'contract'
    | 'price_from'
    | 'price_to'
    | 'surface_from'
    | 'surface_to'
    | 'portal'
    | 'page';

type SearchParams = Partial<{ [key in KeyParams]: number | string }>;

export const getProperties = (searchParams: Partial<SearchParams> = {}): Promise<PropertiesResponse> => {
    return http.get(MULTISEND_PROPERTIES_PATH, { searchParams }).json();
};

export const getIsPropertySearchable = ({
    portalId,
    propertyId,
}: {
    propertyId: number;
    portalId: number;
}): Promise<IsSearchablePropertyResponse> => {
    return http
        .get(MULTISEND_PROPERTIES_IS_SEARCHABLE_PATH, {
            searchParams: { propertyId, portalId },
        })
        .json();
};

export const getPropertiesRemainingSpaces = async ({
    portalId,
}: {
    portalId: number;
}): Promise<RemainingPropertiesPortalsResponse> => {
    return http
        .get(MULTISEND_PROPERTIES_REMAINING_SPACE_PATH, {
            searchParams: { portalId },
        })
        .json();
};

export const getPublishedPortalsByProperties = async ({
    portalId,
    properties,
}: {
    portalId: number;
    properties: number[];
}): Promise<PublishedPortalsByPropertiesResponse> => {
    const params = new URLSearchParams();
    params.append('portalId', portalId.toString());
    properties.forEach((propertyId, index) => {
        params.append(`properties[${index}]`, propertyId.toString());
    });

    return http.get(`${MULTISEND_PROPERTIES_PUBLISHED_PATH}?${params.toString()}`).json();
};

export const togglePortalForProperty = async ({
    portalId,
    propertyId,
    status,
}: {
    propertyId: number;
    portalId: number;
    status: boolean;
}): Promise<TogglePropertyPortalResponse> => {
    const response = await http
        .post(MULTISEND_PROPERTIES_ACTIVATION_PORTAL_PATH, {
            form: {
                propertyId,
                portalId,
                status,
            },
        })
        .json();
    return response;
};

export const toggleAllPortalForProperties = async ({
    portalId,
    status,
}: {
    portalId: number;
    status: boolean;
}): Promise<PublishAllPropertyPortalResponse> => {
    const response = await http
        .post(MULTISEND_PROPERTIES_ACTIVATE_ALL_PORTAL_PATH, {
            form: {
                portalId,
                status,
            },
        })
        .json();
    return response;
};

export const getCategories = async ({ context }: { context: 'properties' }): Promise<CategoriesResponse> => {
    return http
        .get(MULTISEND_PROPERTIES_CATEGORIES_PATH, {
            searchParams: { context },
        })
        .json();
};

export const getPropertiesWithoutZones = (
    searchParams: { portalId: number } & Partial<any>
): Promise<PortalZonesResponse> => {
    return http.get(MULTISEND_PROPERTIES_WITHOUT_ZONES_PATH, { searchParams }).json();
};

export const getZonesByPortals = ({
    portalIds,
    propertyId,
}: {
    portalIds: number[];
    propertyId: number;
}): Promise<ZonesByPortalsResponse> => {
    return http
        .get(MULTISEND_PROPERTIES_ZONES_BY_PORTALS_PATH, {
            searchParams: { propertyId, portalIds },
        })
        .json();
};

export const setZone = ({
    portalId,
    propertyId,
    zoneId,
}: {
    portalId: number;
    propertyId: number;
    zoneId: number;
}): Promise<SetZoneResponse> => {
    return http
        .post(MULTISEND_PROPERTIES_SET_ZONE_PATH, {
            form: { portalId, propertyId, zoneId },
        })
        .json();
};

export const getBacklinkIsValid = ({ backlink }: { backlink: string }): Promise<BacklinkIsValidResponse> => {
    return http
        .get(MULTISEND_PROPERTIES_BACKLINK_IS_VALID_PATH, {
            searchParams: { backlink },
        })
        .json();
};

export const publishPortalsByProperties = ({
    portalIds,
    propertyId,
}: {
    propertyId: number;
    portalIds: number[];
}): Promise<PublishPortalsByPropertiesResponse> => {
    return http
        .post(MULTISEND_PROPERTIES_PUBLISH_PORTALS_PATH, {
            form: {
                propertyId,
                portalIds,
            },
        })
        .json();
};
