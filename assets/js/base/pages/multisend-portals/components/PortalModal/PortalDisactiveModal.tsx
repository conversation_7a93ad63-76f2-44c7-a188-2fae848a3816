import { Button } from '@gx-design/button';
import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { Modal } from '@gx-design/modal';
import { useCallback } from 'react';
import useDisactivePortalMutation from '../../hooks/useDisactivePortalMutation';

type PortalDisactiveModalProps = {
    isOpen: boolean;
    onClose: () => void;
    id: number;
    portalName: string;
};

const PortalDisactiveModal = ({
    isOpen,
    onClose,
    portalName,
    id,
}: PortalDisactiveModalProps) => {
    const { mutate, isPending: isLoading } = useDisactivePortalMutation();
    const { showNotification } = useNotifyContext();

    const onDisactive = useCallback(() => {
        mutate(
            { id },
            {
                onSuccess: () => {
                    showNotification({
                        type: 'success',
                        message: trans('portals.exports_disable_with_success'),
                    });
                    onClose();
                },
                onError: () => {
                    showNotification({
                        type: 'error',
                        message: trans('portals.exports_disable_with_error'),
                    });
                },
            }
        );
    }, [mutate, id, onClose, showNotification]);

    return (
        <Modal
            size="small"
            title={trans('userProfile.deactivationConfirm.title')}
            onClose={onClose}
            isOpen={isOpen}
            footer={
                <>
                    <Button onClick={onClose} variant="ghost">
                        {trans('label.cancel')}
                    </Button>
                    <Button
                        disabled={isLoading}
                        onClick={onDisactive}
                        variant="accent"
                    >
                        {trans('label.deactivate')}
                    </Button>
                </>
            }
        >
            {trans('portals.exports_ensure_disactivate', { portalName })}
        </Modal>
    );
};

export default PortalDisactiveModal;
