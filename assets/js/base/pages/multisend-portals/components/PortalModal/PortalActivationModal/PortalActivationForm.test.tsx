import { render, screen } from '#tests/react/testing-library-enhanced';
import { addMonths, endOfMonth, startOfMonth } from 'date-fns';
import { formatDate } from 'lib/datefns';
import { describe, expect, test, vi } from 'vitest';
import PortalActivationForm from './PortalActivationForm';

describe('PortalActivationForm', () => {
    test('should render the correct date in the range date pickers', async () => {
        const onSubmitMock = vi.fn();

        const { user } = render(
            <PortalActivationForm
                onSubmit={onSubmitMock}
                detail={{
                    portalInfo: {
                        contractStartDate: '1/10/2018',
                        contractEndDate: '7/10/2018',
                        lastSendDate: '23 giugno 2023',
                        unlimitedSend: false,
                        propertyNumber: 1,
                        relationshipCode: '',
                        username: '<EMAIL>',
                        password: '',
                        pageUrl: '',
                        preselection: false,
                    },
                    configuration: {
                        contractStartDate: 3,
                        contractEndDate: 3,
                        propertyNumber: 3,
                        relationshipCode: 1,
                        username: 3,
                        password: 1,
                        pageUrl: 2,
                    },
                }}
            />
        );

        const contractDurationElement = screen.getByLabelText(
            'label.contract_start'
        );

        const contractDurationToElement =
            screen.getByLabelText('label.contract_end');

        expect(contractDurationElement).toHaveValue('01/10/2018');
        expect(contractDurationToElement).toHaveValue('07/10/2018');

        const submitButton = screen.getByTestId('portal-submit-button');

        await user.click(submitButton);

        expect(onSubmitMock).toHaveBeenCalled();
        expect(onSubmitMock).toHaveBeenCalledWith(
            {
                // eslint-disable-next-line camelcase
                contractDuration_from: '01/10/2018',
                // eslint-disable-next-line camelcase
                contractDuration_to: '07/10/2018',
                preselection: 'false',
                pageUrl: '',
                propertyNumber: 1,
                unlimitedSend: false,
                username: '<EMAIL>',
            },
            expect.anything()
        );
    });

    test('should show an error if the contract end is before or equal than contract start in the range date pickers', async () => {
        const onSubmitMock = vi.fn();

        const CURRENT_MONTH_START_VALUE = formatDate(
            startOfMonth(new Date()),
            'dd/MM/yyyy'
        );
        const CURRENT_MONTH_END_VALUE = formatDate(
            endOfMonth(new Date()),
            'dd/MM/yyyy'
        );
        const NEXT_MONTH_START = startOfMonth(addMonths(new Date(), 1));
        const NEXT_MONTH_END = endOfMonth(addMonths(new Date(), 1));

        const NEXT_MONTH_START_BUTTON = formatDate(
            NEXT_MONTH_START,
            'd MMMM yyyy'
        ).toLowerCase();
        const NEXT_MONTH_START_VALUE = formatDate(
            NEXT_MONTH_START,
            'dd/MM/yyyy'
        );
        const NEXT_MONTH_END_BUTTON = formatDate(
            NEXT_MONTH_END,
            'd MMMM yyyy'
        ).toLowerCase();
        const NEXT_MONTH_END_VALUE = formatDate(NEXT_MONTH_END, 'dd/MM/yyyy');

        const { user } = render(
            <PortalActivationForm
                onSubmit={onSubmitMock}
                detail={{
                    portalInfo: {
                        contractStartDate: CURRENT_MONTH_START_VALUE,
                        contractEndDate: CURRENT_MONTH_END_VALUE,
                        lastSendDate: '23 giugno 2023',
                        unlimitedSend: false,
                        propertyNumber: 1,
                        relationshipCode: '',
                        username: '<EMAIL>',
                        password: '',
                        pageUrl: '',
                        preselection: false,
                    },
                    configuration: {
                        contractStartDate: 3,
                        contractEndDate: 3,
                        propertyNumber: 3,
                        relationshipCode: 1,
                        username: 3,
                        password: 1,
                        pageUrl: 2,
                    },
                }}
            />
        );

        const contractDurationElement = screen.getByLabelText(
            'label.contract_start'
        );

        const contractDurationToElement =
            screen.getByLabelText('label.contract_end');

        expect(contractDurationElement).toHaveValue(CURRENT_MONTH_START_VALUE);
        expect(contractDurationToElement).toHaveValue(CURRENT_MONTH_END_VALUE);

        await user.click(contractDurationToElement);
        const next1 = screen.getByRole('button', {
            name: 'label.next_month',
        });
        expect(next1).toBeVisible();
        await user.click(next1);
        const startMonthButton = screen.getByRole('button', {
            name: NEXT_MONTH_START_BUTTON,
        });
        await user.click(startMonthButton);
        expect(contractDurationToElement).toHaveValue(NEXT_MONTH_START_VALUE);

        await user.click(contractDurationElement);
        const next2 = screen.getByRole('button', {
            name: 'label.next_month',
        });
        expect(next2).toBeVisible();
        await user.click(next2);
        const endMonthButton = screen.getByRole('button', {
            name: NEXT_MONTH_END_BUTTON,
        });
        await user.click(endMonthButton);
        expect(contractDurationElement).toHaveValue(NEXT_MONTH_END_VALUE);

        const submitButton = screen.getByTestId('portal-submit-button');

        await user.click(submitButton);

        expect(screen.getByText('label.date_is_not_valid')).toBeInTheDocument();
        expect(onSubmitMock).not.toHaveBeenCalled();
    });

    test('should render the correct date in the single date picker', async () => {
        const onSubmitMock = vi.fn();

        const { user } = render(
            <PortalActivationForm
                onSubmit={onSubmitMock}
                detail={
                    {
                        portalInfo: {
                            contractStartDate: '2/09/2011',
                            contractEndDate: null,
                            lastSendDate: '26 giugno 2023',
                            unlimitedSend: false,
                            propertyNumber: 10,
                            relationshipCode: null,
                            username: null,
                            password: null,
                            pageUrl: '',
                            preselection: false,
                        },
                        configuration: {
                            contractStartDate: 3,
                            contractEndDate: 1,
                            propertyNumber: 3,
                            relationshipCode: 1,
                            username: 1,
                            password: 1,
                            pageUrl: 2,
                        },
                    } as any
                }
            />
        );

        const contractDurationElement = screen.getByLabelText(
            'label.contract_start'
        );

        expect(contractDurationElement).toHaveValue('02/09/2011');

        const submitButton = screen.getByTestId('portal-submit-button');

        await user.click(submitButton);

        expect(onSubmitMock).toHaveBeenCalled();
        expect(onSubmitMock).toHaveBeenCalledWith(
            {
                // eslint-disable-next-line camelcase
                contractDuration_from: '02/09/2011',
                preselection: 'false',
                pageUrl: '',
                propertyNumber: 10,
                unlimitedSend: false,
            },
            expect.anything()
        );
    });
});
