import { Button } from '@gx-design/button';
import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { Modal } from '@gx-design/modal';
import React, { useCallback, useMemo, useState } from 'react';
import useOrderPortalsMutation from '../../hooks/useOrderPortalsMutation';
import { MultiSendPortal, MultiSendPortalsResponse } from '../../types';
import { getImageUrl } from '../../utils';
import { PortalSorting } from './types';
import { SortableList } from 'gtx-react/components/SortableList';

type PortalOrderingModalProps = {
    isOpen: boolean;
    onClose: () => void;
    portals: MultiSendPortal[];
};

/**
 * Get only the necessary data for the portal sorting
 * @param portals
 * @returns
 */
const getItems = (portals: MultiSendPortalsResponse['data']) => {
    return (portals as MultiSendPortal[]).map<PortalSorting>((p, index) => ({
        id: p.portal.id,
        image: p.portal.logoId,
        name: p.portal.name,
        index,
    }));
};

const PortalOrderingModal = ({
    isOpen,
    onClose,
    portals,
}: PortalOrderingModalProps) => {
    const items = useMemo(() => getItems(portals), [portals]);
    const { showNotification } = useNotifyContext();
    const { mutate, isPending: isLoading } = useOrderPortalsMutation();
    const [portalsState, setPortalsState] = useState(items);

    const onOrder = useCallback(() => {
        mutate(
            { ids: items.map((i) => i.id) },
            {
                onSuccess: () => {
                    showNotification({
                        type: 'success',
                        message: trans('portals.exports_ordered_with_success'),
                    });

                    onClose();
                },
                onError: () => {
                    showNotification({
                        type: 'error',
                        message: trans('portals.exports_ordered_with_error'),
                    });
                },
            }
        );
    }, [mutate, items, onClose, showNotification]);

    const handleClose = useCallback(() => {
        setPortalsState(items);
        onClose();
    }, [items, onClose]);

    return (
        <Modal
            title={trans('label.reorder')}
            size="small"
            bodyHeight="maxHeight"
            isOpen={isOpen}
            onClose={handleClose}
            footer={
                <>
                    <Button onClick={handleClose} variant="ghost">
                        {trans('label.cancel')}
                    </Button>
                    <Button
                        disabled={isLoading}
                        variant="accent"
                        onClick={onOrder}
                    >
                        {trans('label.save')}
                    </Button>
                </>
            }
        >
            {portalsState && (
                <div className="portal-order">
                    <SortableList
                        className="portals-order-list"
                        items={portalsState}
                        onChange={setPortalsState}
                        renderItem={(item) => (
                            <SortableList.Item
                                className="portal-order__item-wrap"
                                id={item.id}
                            >
                                <SortableList.DragHandler className="portal-order__item">
                                    <div className="logo">
                                        <img
                                            src={getImageUrl(item.image)}
                                            alt={item.name}
                                            draggable={false}
                                        />
                                    </div>
                                    <div className="drag">
                                        <div></div>
                                    </div>
                                </SortableList.DragHandler>
                            </SortableList.Item>
                        )}
                    />
                </div>
            )}
        </Modal>
    );
};

export default PortalOrderingModal;
