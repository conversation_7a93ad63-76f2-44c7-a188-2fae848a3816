import { useQuery } from '@tanstack/react-query';
import { getZonesByPortals } from '../../../web-api/real';
import { MULTISEND_PROPERTIES_ZONES_BY_PORTALS_PATH } from '../../../web-api/endpoints';

const getZonesQueryKey = ({ portalIds, propertyId }: { portalIds: number[]; propertyId: number }) =>
    [`GET ${MULTISEND_PROPERTIES_ZONES_BY_PORTALS_PATH}`, portalIds, propertyId] as const;

export const useZonesQuery = (
    {
        portalIds,
        propertyId,
    }: {
        portalIds: number[];
        propertyId: number;
    },
    { enabled }: { enabled: boolean }
) => {
    return useQuery({
        queryKey: getZonesQueryKey({ portalIds, propertyId }),

        queryFn: () =>
            getZonesByPortals({ portalIds, propertyId }).then((response) => {
                if (response.status === 'success') {
                    return response.data;
                }

                return Promise.reject(response);
            }),
        initialData: {},
        enabled,
    });
};
