import { useIsMutating } from '@tanstack/react-query';
import { getOtherPortalActivationMutationKey } from '../../hooks/useOtherPortalsActivationMutation';
import { getActivationMutationKey } from './hooks/useActivationMutation';
import { getIsSearchableMutationKey } from './hooks/useIsSearchableMutation';

import type { JSX } from "react";

type ActivationProgressProps = {
    children: (props: { isActivating: boolean }) => JSX.Element;
};

const ActivationProgress = ({ children }: ActivationProgressProps) => {
    const countIsSearchable = useIsMutating({
        mutationKey: getIsSearchableMutationKey(),
        exact: false,
    });
    const countIsActivation = useIsMutating({
        mutationKey: getActivationMutationKey(),
        exact: false,
    });

    const countIsOtherPortalActivation = useIsMutating({
        mutationKey: getOtherPortalActivationMutationKey(),
        exact: false,
    });

    return children({
        isActivating:
            countIsSearchable +
                countIsActivation +
                countIsOtherPortalActivation >
            0,
    });
};

export default ActivationProgress;
