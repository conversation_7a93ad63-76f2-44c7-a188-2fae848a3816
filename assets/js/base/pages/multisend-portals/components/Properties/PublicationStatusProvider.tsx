import React from 'react';
import { MultiSendPortal, PropertyPublicationStatus } from '../../types';

type PublicationStatusProviderProps = {
    publicationStatus: PropertyPublicationStatus;
    portal: MultiSendPortal;
};

const PublicationStatusContext = React.createContext<
    PublicationStatusProviderProps
>(undefined);

export const PublicationStatusProvider = ({
    children,
    ...value
}: React.PropsWithChildren<PublicationStatusProviderProps>) => {
    return (
        <PublicationStatusContext.Provider value={value}>
            {children}
        </PublicationStatusContext.Provider>
    );
};

export const usePublicationStatusContext = () => {
    const context = React.useContext(PublicationStatusContext);
    if (context === undefined) {
        throw new Error(
            'usePublicationStatusContext must be used within a PublicationStatusProvider'
        );
    }
    return context;
};
