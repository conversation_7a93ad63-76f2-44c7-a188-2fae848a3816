import { ActionList, ActionListItem } from '@gx-design/action-list';
import { Dropdown } from '@gx-design/dropdown';
import { Icon } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import React, { PropsWithChildren } from 'react';
import { FiltersBar } from '../FiltersBar';

type SortParameters = { sort: '+city' | '+code' | '-city' | '-code' };

type NewConstructionFiltersBarProps = {
    currentSort: { [key: string]: string };
    leftActions?: React.ReactNode;
    results: number | string;
    onClear?: () => void;
    onSort: (props: SortParameters) => void;
};

export const NewConstructionsFiltersBar = ({
    leftActions,
    currentSort,
    children,
    results,
    onClear,
    onSort,
}: PropsWithChildren<NewConstructionFiltersBarProps>) => {
    const handleSort = React.useCallback(
        (props: SortParameters) => {
            return () => onSort(props);
        },
        [onSort]
    );

    return (
        <>
            <FiltersBar
                leftActions={leftActions}
                onClear={onClear}
                results={results}
                actions={
                    <Dropdown
                        buttonIsIconOnly={false}
                        buttonContent={
                            <>
                                <Icon name="order" />
                                <span>{trans('label.order_imperative')}</span>
                            </>
                        }
                        position="bottomRight"
                    >
                        <ActionList title={`${trans('label.order_by')}:`}>
                            <ActionListItem
                                active={currentSort.sort === '+code'}
                                onClick={handleSort({ sort: '+code' })}
                                text={trans('label.sort_reference_asc')}
                            />
                            <ActionListItem
                                active={currentSort.sort === '+city'}
                                onClick={handleSort({ sort: '+city' })}
                                text={trans('label.sort_city_asc')}
                            />
                        </ActionList>
                        <ActionList>
                            <ActionListItem
                                active={currentSort.sort === '-code'}
                                onClick={handleSort({ sort: '-code' })}
                                text={trans('label.sort_reference_desc')}
                            />
                            <ActionListItem
                                active={currentSort.sort === '-city'}
                                onClick={handleSort({ sort: '-city' })}
                                text={trans('label.sort_city_desc')}
                            />
                        </ActionList>
                    </Dropdown>
                }
            >
                {children}
            </FiltersBar>
        </>
    );
};
