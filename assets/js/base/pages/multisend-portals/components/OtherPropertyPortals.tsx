import { Popover } from '@gx-design/popover';
import classNames from 'classnames';
import { Image, ViewportMediaQuery } from 'gtx-react/components';
import React from 'react';
import { PropertyCounter } from './PropertyCounter';
import { PROPERTY_IMAGE_PLACEHOLDER } from 'constants/property';
import { getImageUrl, getPropertyImageUrl } from '../utils';

type PortalConstructionLogoProps = {
    imageId: string;
    className?: string;
    title: string;
    publishMethod?: string;
};

type PortalRowResponsiveProps = Omit<
    PortalConstructionLogoProps,
    'className' | 'showPopover'
> & {
    viewport?: 'tablet' | 'desktop';
};

type PortalRowProps = Omit<PortalRowResponsiveProps, 'viewport'>;

const PortalLogo = ({
    imageId,
    className,
    title,
    publishMethod,
}: PortalConstructionLogoProps) => (
    <div className={classNames(['logo', className])}>
        <Popover
            position="bottomLeft"
            onEdge
            content={<>{title}</>}
            large={false}
            title={publishMethod || ''}
        >
            <img src={getImageUrl(imageId)} alt={title} />
        </Popover>
    </div>
);

export const PortalRowDesktop = ({
    imageId,
    title,
    publishMethod,
    children,
}: React.PropsWithChildren<PortalRowProps>) => (
    <div className="portal-activation-row">
        <PortalLogo
            imageId={imageId}
            title={title}
            publishMethod={publishMethod}
        />
        <div className="portal-activation-row__actions">
            {React.Children.map(children, (child) => (
                <div className="portal-activation-row__actions__item">
                    {child}
                </div>
            ))}
        </div>
    </div>
);

const PortalRowMobile = ({
    imageId,
    title,
    publishMethod,
    children,
}: React.PropsWithChildren<PortalRowProps>) => (
    <div className="portal-activation-row clearfix">
        <PortalLogo
            imageId={imageId}
            title={title}
            publishMethod={publishMethod}
        />
        {children}
    </div>
);

export const PortalRow = ({
    viewport = 'desktop',
    imageId,
    title,
    publishMethod,
    children,
}: React.PropsWithChildren<PortalRowResponsiveProps>) => {
    const Component =
        viewport === 'tablet' ? PortalRowMobile : PortalRowDesktop;

    return (
        <Component
            imageId={imageId}
            title={title}
            publishMethod={publishMethod}
        >
            {children}
        </Component>
    );
};

export function OtherPropertyPortals({
    children,
    onSendAll,
    constructionImageId,
    isLoading,
    constructionDescriptions,
}: React.PropsWithChildren<{
    onSendAll: () => void;
    constructionImageId: string;
    constructionDescriptions: string[];
    isLoading: boolean;
}>) {
    return (
        <div>
            <div className="portal-activation-row">
                <div className="property-block">
                    <Image
                        className="property-block__photo"
                        src={getPropertyImageUrl(constructionImageId)}
                        fallbackSrc={PROPERTY_IMAGE_PLACEHOLDER}
                    />
                    <div className="property-block__desc">
                        {constructionDescriptions.map((description) => (
                            <div
                                key={description}
                                className="property-block__desc__field"
                            >
                                {description}
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            <ViewportMediaQuery>
                {({ isDesktop, isTablet }) =>
                    React.Children.map(children, (child) => {
                        if (React.isValidElement(child)) {
                            return React.cloneElement(
                                child as React.ReactElement<{
                                    viewport: 'desktop' | 'tablet';
                                }>,
                                {
                                    viewport: isDesktop ? 'desktop' : 'tablet',
                                }
                            );
                        }

                        return null;
                    })
                }
            </ViewportMediaQuery>
        </div>
    );
}

OtherPropertyPortals.Row = PortalRow;
OtherPropertyPortals.RowCounter = PropertyCounter;
