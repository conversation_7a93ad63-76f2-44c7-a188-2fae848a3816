import { getSessionStorageKey } from '../../../../commons/lib/client-storage';
import { trans } from '@pepita-i18n/babelfish';

type ActivationError = {
    adId: string;
    extra: { [key: string]: string }[];
};

/**
 * Parses data from api mapping every error label with his own tranlation
 * @param data
 * @returns
 */
export const parseActivationErrors = (data: ActivationError[]) => {
    if (!data) {
        return null;
    }

    let result = [];
    const validationErrors = getSessionStorageKey('validationErrors');

    data.map((item) => {
        const errorData = { id: item['adId'], code: item['code'], errors: [] };
        const extraKeys = Object.keys(item.extra);

        extraKeys.forEach((extraKeyItem) => {
            const errorType = item.extra[extraKeyItem][0];
            const splittedKeyItems = extraKeyItem.split('.');

            let shouldSkip = false;
            splittedKeyItems.forEach((key) => {
                if (shouldSkip) {
                    return false;
                }

                if (validationErrors['errors'][key] && validationErrors['errors'][key][errorType]) {
                    errorData.errors.push(trans(validationErrors['errors'][key][errorType]));
                    shouldSkip = true;
                }
            });
        });

        result.push(errorData);
    });

    return result;
};
