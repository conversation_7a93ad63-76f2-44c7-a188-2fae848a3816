import React from 'react';
import { CardList } from 'gtx-react/components/CardList/CardList';
import { trans } from '@pepita-i18n/babelfish';
import { useSelector } from 'react-redux';
import {
    <PERSON>Query,
    Loader as ListLoader,
} from '../../../../commons/gtx-react/components';
import {
    CACHE_QUALITY_DETAILS_KEY_PREFIX,
    CARD_LIST_LABELS,
    PORTAL_NEW_CONSTRUCTIONS_VIEWPORT_MEDIA_QUERIES,
} from '../constants';
import { List, ListItem } from '@gx-design/list';
import { IReduxRootState } from '../types/redux';
import gtxConstants from '@getrix/common/js/gtx-constants';
import { isSearchActive } from '../utils/searchFilters';
import { usePortalNewConstructionsListContext } from '../hooks/usePortalPropertiesListContext';
import { PortalNewConstructionsListAlert } from './PortalNewConstructionsListAlert';
import { IPortalNewConstructionsListItem } from '../types/list';
import { getNewConstructionQualityDetailsApi } from '../web-api/api';
import { useQueryClient } from '@tanstack/react-query';

function renderImage(data: IPortalNewConstructionsListItem) {
    return <img src={data.mainImageUrl} loading="lazy" />;
}

function renderReference(data: IPortalNewConstructionsListItem) {
    if (!data) {
        return null;
    }

    const reference = data?.code ? data.code : data.id;

    return (
        <List>
            <ListItem content={reference} />
        </List>
    );
}

export const PortalNewConstructionsSmallScreenDevicesList: React.FC<{}> =
    () => {
        const { listConfigs, listActions, listItems, renderMapping } =
            usePortalNewConstructionsListContext();
        const queryClient = useQueryClient();
        const searchFilters = useSelector(
            (data: IReduxRootState) => data.searchFilters
        );
        const fields = React.useMemo(
            () => [
                {
                    key: 'image',
                    main: true,
                    renderContent: (data: IPortalNewConstructionsListItem) =>
                        renderImage(data),
                },
                {
                    key: 'reference',
                    label: trans('label.reference'),
                    renderContent: (data: IPortalNewConstructionsListItem) =>
                        renderReference(data),
                },
                {
                    key: 'place',
                    label: trans('label.place'),
                    renderContent: renderMapping['place'],
                },
                {
                    key: 'visibility',
                    label: trans('label.visibility'),
                    renderContent: renderMapping['visibility'],
                },
                {
                    key: 'quality',
                    label: trans('label.quality'),
                    asyncContentRender: (
                        data: IPortalNewConstructionsListItem
                    ) => {
                        if (!data?.id) {
                            return;
                        }

                        const cachedData = queryClient.getQueryData([
                            `${CACHE_QUALITY_DETAILS_KEY_PREFIX}${data.id}`,
                        ]);

                        if (cachedData) {
                            return new Promise((resolve) =>
                                resolve(
                                    renderMapping['quality'](data, cachedData)
                                )
                            );
                        }

                        return getNewConstructionQualityDetailsApi(
                            data.id
                        ).then((res) => {
                            if (!res?.data) return;

                            queryClient.setQueryData(
                                [
                                    `${CACHE_QUALITY_DETAILS_KEY_PREFIX}${data.id}`,
                                ],
                                res.data
                            );
                            return renderMapping['quality'](data, res.data);
                        });
                    },
                },
                {
                    key: 'statistics',
                    label: trans('label.statistics'),
                    renderContent: renderMapping['statistics'],
                },
                {
                    key: 'threads',
                    label: trans('label.messages'),
                    renderContent: renderMapping['threads'],
                    sortable: false,
                    visible: Boolean(
                        gtxConstants('PROPERTY_LIST_MATCH_COLUMN')
                    ),
                },
                {
                    key: 'match',
                    label: trans('label.matches.page_title'),
                    renderContent: renderMapping['match'],
                    sortable: false,
                    visible: Boolean(
                        gtxConstants('PROPERTY_LIST_MATCH_COLUMN')
                    ),
                },
                {
                    key: 'date',
                    label: trans('label.modified_date'),
                    renderContent: renderMapping['date'],
                },
            ],
            []
        );

        const data = {
            items: listItems,
            extraItemFields: ['id', 'status', 'favourite'],
        };

        const configs = {
            ...listConfigs,
            labels: CARD_LIST_LABELS,
        };

        const listLoader = useSelector(
            (data: IReduxRootState) => data.listLoader
        );

        return (
            <div style={{ position: 'relative' }}>
                <PortalNewConstructionsListAlert />
                <ListLoader
                    loading={listLoader.isLoading}
                    fixedOverlay={false}
                    centered={false}
                />
                <MediaQuery
                    queries={PORTAL_NEW_CONSTRUCTIONS_VIEWPORT_MEDIA_QUERIES}
                >
                    {(match) => (
                        <CardList
                            actions={listActions}
                            cardFields={fields}
                            cardsData={data}
                            configs={configs}
                            twoColumnsView={match.isTablet}
                            noInitialItems={
                                !data.items && !isSearchActive(searchFilters)
                            }
                        />
                    )}
                </MediaQuery>
            </div>
        );
    };
