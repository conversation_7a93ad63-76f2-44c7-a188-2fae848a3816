import { Button } from '@gx-design/button';
import { Modal } from '@gx-design/modal';
import { trans } from '@pepita-i18n/babelfish';
import { useSuspenseQuery } from '@tanstack/react-query';
import { Formik, useFormikContext } from 'formik';
import { GxFkCheckbox } from 'gtx-react/components/gx-formik';
import { createTosQueryOptions } from 'lib/REST/requests/immobili/aste/catalogo/query-factory';
import * as Yup from 'yup';
import { useTosMutation } from '../../hooks/list/useTosMutation';
import { Icon } from '@gx-design/icon';

const TosFormModal = ({
    shouldShowModal,
    isPending,
}: {
    shouldShowModal: boolean;
    isPending: boolean;
}) => {
    const { values, submitForm } = useFormikContext<TosSchemaType>();

    return (
        <Modal
            isOpen={shouldShowModal}
            size="large"
            title={trans('auctions.tos.title')}
            onClose={() => {}}
            closeAction={false}
            footer={
                <Button
                    disabled={!values?.tos}
                    variant="accent"
                    onClick={() => submitForm()}
                >
                    {isPending ? (
                        <Icon name="loader" className="gx-spin" />
                    ) : (
                        trans('label.close')
                    )}
                </Button>
            }
        >
            <div>
                <div>
                    {trans('auctions.tos.text_1')}
                    <br />
                    <br />
                    {trans('auctions.tos.text_2')}
                </div>
                <div style={{ marginTop: '22px', height: '22px' }}>
                    <form>
                        <GxFkCheckbox
                            name="tos"
                            label={trans('auctions.tos.checkbox')}
                        />
                    </form>
                </div>
            </div>
        </Modal>
    );
};

export const tosSchema = Yup.object().shape({
    tos: Yup.boolean(),
});

export type TosSchemaType = Yup.InferType<typeof tosSchema>;

export default () => {
    const { data: tos } = useSuspenseQuery({
        ...createTosQueryOptions(),
        select: (data) => data?.result,
    });

    const { mutate, isPending } = useTosMutation();

    return (
        <Formik
            initialValues={{ tos }}
            onSubmit={() => mutate()}
            validationSchema={tosSchema}
            enableReinitialize
        >
            <TosFormModal shouldShowModal={!tos} isPending={isPending} />
        </Formik>
    );
};
