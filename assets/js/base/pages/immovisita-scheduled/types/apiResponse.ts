export interface IPaginationOptionsResponse {
    paginationOptions: ILookupItem[];
}
export interface ILookupItem {
    value: any;
    label: string;
    extra?: { [key: string]: any };
}

export interface ISvListFormattedApiResponse {
    rooms: IRoomListItem[];
    pagination: IPagination;
}

export interface IRoomListItem {
    id: number;
    // agentId: null;
    agent: { id: number; firstname: string; lastname: string };
    startTime: null | string;
    endTime: null | string;
    scheduledTime: string;
    externalId: string;
    created: string;
    note: string;
    modified: string;
    guests: IGuests[];
    property: IProperty;
    link: string;
}

export interface IProperty {
    id: number;
    properties: IProperties[];
    prices: IPrices[];
    mainImage: number;
    created: string;
    modified: string;
    propertiesDetailUrl: string;
    printableSurface: string;
    printableTypology: string;
    printablePrice: string;
    mainThumbUrl: string;
    geographyInformations: IGeographyInformations;
}

export interface IProperties {
    id: number;
    reference: string;
    typologyV2: ITypologyV2;
    geographyInformation: IGeographyInformation;
    isMain: boolean;
    surface: number;
    rooms: number;
    bathrooms: number;
    floor: null;
}

export interface ITypologyV2 {
    id: number;
    name: string;
    parent: IParent;
}

export interface IParent {
    id: number;
    name: string;
    parent: IParent | null;
}

export interface IGeographyInformation {
    city: ICity;
    macrozone: IMacrozone;
    address: IAddress;
    coordinates: ICoordinates;
    showAddress: boolean;
}

export interface ICity {
    id: number;
    name: string;
    province: IProvince;
    cityMacroZoneType: null;
}

export interface IProvince {
    id: string;
    name: string;
}

export interface IMacrozone {
    id: number;
    name: string;
    nameSn: null;
    keyUrl: null;
    city: null;
}

export interface IAddress {
    street: string;
    number: null;
}

export interface ICoordinates {
    latitude: number;
    longitude: number;
}

export interface IPrices {
    price: string;
    isVisible: boolean;
    contractId: number;
    printablePrice: string;
}

export interface IGeographyInformations {
    city: ICity;
    macrozone: IMacrozone;
    address: IAddress;
    coordinates: ICoordinates;
    showAddress: boolean;
    printableCity: string;
    printableAddress: string;
}

export interface IGuests {
    id: number;
    roomId: number;
    firstname: string;
    lastname: string;
    phone: string;
    email: string;
}

export interface IPagination {
    start: number;
    results: number;
    total: number;
    page: number;
}
