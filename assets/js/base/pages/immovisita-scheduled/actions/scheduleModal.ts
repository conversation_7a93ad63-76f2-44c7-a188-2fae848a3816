import { createActions } from 'redux-actions';
import { IReduxRootState } from '../types/redux';

export const {
    toggleScheduleModalCreator,
    setScheduleItemModalCreator,
} = createActions({
    TOGGLE_SCHEDULE_MODAL_CREATOR: isOpen => isOpen,
    SET_SCHEDULE_ITEM_MODAL_CREATOR: ref => ref,
});

export const toggleScheduleModal = isOpen => dispatch =>
    dispatch(toggleScheduleModalCreator(isOpen));

export const setModalScheduledItem = (
    item: IReduxRootState['modify_modal']['scheduledVisit']
) => dispatch => dispatch(setScheduleItemModalCreator(item));
