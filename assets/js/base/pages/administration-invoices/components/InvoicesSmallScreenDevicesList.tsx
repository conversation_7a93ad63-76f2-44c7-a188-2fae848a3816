import { Badge } from '@gx-design/badge';
import { trans } from '@pepita-i18n/babelfish';
import { FC } from 'react';
import { CardList } from 'gtx-react/components/CardList/CardList';
import {
    CardField,
    CardListActions,
    CardListConfigs,
} from 'gtx-react/components/CardList/types';
import { TableData } from 'gtx-react/components/GxTable/types';
import { formatDate } from 'lib/datefns';
import useInvoicesConfig from '../hooks/useInvoicesConfig';
import { Invoice } from '../types/apiResponse';
import { renderStatusTagStyle } from './../utils/utils';
import { Button } from '@gx-design/button';
import useInvoiceActions from '../hooks/useInvoiceActions';
import { convertCase, ucFirst } from 'lib/strings-formatter';

const renderCardFields = (
    showPaymentButton: (data: Invoice) => boolean,
    paymentLinkByIdAction: (id: Invoice['id']) => string
): CardField[] => [
    {
        key: 'number',
        label: trans('label.document_number'),
        renderContent: (data: Invoice) => <span>{data.number}</span>,
    },
    {
        key: 'type',
        label: trans('label.type'),
        renderContent: (data: Invoice) => (
            <span>
                {data.type
                    ? ucFirst(
                          trans(
                              `invoice.type.${convertCase(data.type, 'snake')}`
                          )
                      )
                    : '--'}
            </span>
        ),
    },
    {
        key: 'date',
        label: trans('label.issue_date'),
        renderContent: (data: Invoice) => (
            <span>
                {formatDate(data.date, 'dd/MM/yy', { fallBackStr: '--' })}
            </span>
        ),
    },
    {
        key: 'expirationDate',
        label: trans('label.expiration_date'),
        renderContent: (data: Invoice) => (
            <span>
                {formatDate(data.expirationDate, 'dd/MM/yy', {
                    fallBackStr: '--',
                })}
            </span>
        ),
    },
    {
        key: 'status',
        label: trans('label.status'),
        renderContent: (data: Invoice) =>
            data.status ? (
                <div className="invoice-status">
                    <Badge
                        className="invoice-status__item"
                        style={renderStatusTagStyle(data.status)}
                        text={ucFirst(
                            trans(
                                `invoice.status.${convertCase(
                                    data.status,
                                    'snake'
                                )}`
                            )
                        )}
                    />
                    {showPaymentButton(data) && (
                        <Button
                            as="a"
                            href={paymentLinkByIdAction(data.id)}
                            target="_blank"
                            className="explore-cta-promotion invoice-status__item"
                        >
                            {trans('label.pay_invoice')}
                        </Button>
                    )}
                </div>
            ) : (
                '--'
            ),
    },
];

const listActions: CardListActions = {
    main: null,
    quick: [
        {
            label: trans('label.download'),
            icon: 'download',
            action: (id: Invoice['id']) =>
                window.open(`/download_fattura.php?id=${id}`),
            validFor: {
                attachment: true,
            },
        },
    ],
    bulk: null,
    menu: null,
};

const rawConfig: CardListConfigs = {
    itemSelection: false,
    emptyState: {
        text: trans('label.no_results_found'),
        image: '/bundles/base/getrix/common/img/empty-state/empty-state.png',
    },
};

type InvoicesSmallScreenDevicesListProps = {
    data: TableData;
    twoColumnsView: boolean;
};

export const InvoicesSmallScreenDevicesList: FC<
    InvoicesSmallScreenDevicesListProps
> = ({ data, twoColumnsView }) => {
    const config = useInvoicesConfig({ invoices: data.items, rawConfig });
    const { showPaymentButton, paymentLinkByIdAction } = useInvoiceActions();
    return (
        <CardList
            cardsData={data}
            cardFields={renderCardFields(
                showPaymentButton,
                paymentLinkByIdAction
            )}
            configs={config as CardListConfigs}
            actions={listActions}
            noInitialItems={!Boolean(data.items.length)}
            twoColumnsView={twoColumnsView}
            displayShowMoreButton={false}
        />
    );
};
