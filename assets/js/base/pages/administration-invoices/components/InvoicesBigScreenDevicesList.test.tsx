import { render, screen, within } from '#tests/react/testing-library-enhanced';
import { describe, expect, test, vi, beforeEach } from 'vitest';
import { InvoicesBigScreenDevicesList } from './InvoicesBigScreenDevicesList';
import { Invoice } from '../types/apiResponse';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { useSelector } from 'react-redux';

// Mock dependencies
vi.mock('react-redux', async () => {
    const actual = await vi.importActual<typeof import('react-redux')>('react-redux');
    return {
        ...actual,
        useSelector: vi.fn(),
    };
});

vi.mock('@growthbook/growthbook-react', async () => {
    const actual = await vi.importActual<typeof import('@growthbook/growthbook-react')>('@growthbook/growthbook-react');
    return {
        ...actual,
        useFeatureIsOn: vi.fn(),
    };
});

describe('InvoicesBigScreenDevicesList', () => {
    const mockInvoices: Invoice[] = [
        {
            id: 1,
            number: 'INV-001',
            type: 'invoice',
            date: '2024-01-15',
            expirationDate: '2024-02-15',
            status: 'paid',
            attachment: 'file1.pdf',
        },
        {
            id: 2,
            number: 'INV-002',
            type: 'creditNote',
            date: '2024-01-10',
            expirationDate: '2024-02-10',
            status: 'notPaid',
            paymentLink: 'https://example.com/pay',
        },
        {
            id: 3,
            number: 'INV-003',
            type: 'invoice',
            date: '2024-01-20',
            expirationDate: '2024-02-20',
            status: 'issued',
            attachment: 'file3.pdf',
            paymentLink: 'example.com/pay3',
        },
    ];

    const mockData = { items: mockInvoices };

    const mockInvoicesState = {
        invoices: {
            list: mockInvoices,
        },
    };

    beforeEach(() => {
        (useFeatureIsOn as any).mockReturnValue(true);
        (useSelector as any).mockReturnValue(mockInvoicesState);
    });

    const mockPagination = {
        pageIndex: 0,
        pageSize: 30,
    };

    const mockOnPageChange = vi.fn();

    const renderComponent = () => {
        return render(
            <InvoicesBigScreenDevicesList
                data={mockData}
                pagination={mockPagination}
                onPageChange={mockOnPageChange}
                totalResults={mockInvoices.length}
            />
        );
    };

    test('renders table with invoice data', () => {
        renderComponent();

        const table = screen.getByRole('table');
        expect(table).toBeInTheDocument();

        // Check that all invoice numbers are rendered
        expect(screen.getByText('INV-001')).toBeInTheDocument();
        expect(screen.getByText('INV-002')).toBeInTheDocument();
        expect(screen.getByText('INV-003')).toBeInTheDocument();
    });

    test('renders sortable column headers', () => {
        renderComponent();

        // Check that sortable headers are present
        expect(screen.getByText('label.document_number')).toBeInTheDocument();
        expect(screen.getByText('label.type')).toBeInTheDocument();
        expect(screen.getByText('label.issue_date')).toBeInTheDocument();
        expect(screen.getByText('label.expiration_date')).toBeInTheDocument();
        expect(screen.getByText('label.status')).toBeInTheDocument();
    });

    test('renders download buttons for invoices with attachments', () => {
        renderComponent();

        const downloadButtons = screen.getAllByTitle('label.download');
        // Should have 2 download buttons (for invoices with attachments)
        expect(downloadButtons).toHaveLength(2);
    });

    test('renders payment buttons for unpaid invoices with payment links', () => {
        renderComponent();

        const paymentButtons = screen.getAllByText('label.pay_invoice');
        // Should have 2 payment buttons (for invoices with payment links and not paid status)
        expect(paymentButtons).toHaveLength(2);
    });

    test('table supports sorting functionality', async () => {
        const { user } = renderComponent();

        const table = screen.getByRole('table');
        expect(table).toBeInTheDocument();

        // Get the first row data before sorting
        const rows = within(table).getAllByRole('row');
        const dataRows = rows.slice(1); // Skip header row

        // Verify initial order (should be in the order we provided)
        expect(within(dataRows[0]).getByText('INV-001')).toBeInTheDocument();
        expect(within(dataRows[1]).getByText('INV-002')).toBeInTheDocument();
        expect(within(dataRows[2]).getByText('INV-003')).toBeInTheDocument();

        // Find and click the document number column header to sort
        const documentNumberHeader = screen.getByText('label.document_number').closest('span');
        if (documentNumberHeader) {
            await user.click(documentNumberHeader);

            // After clicking, the table should be sorted
            // Note: The actual sorting behavior depends on the TanStack Table implementation
            // This test verifies that the sorting mechanism is in place
            const sortedRows = within(table).getAllByRole('row');
            const sortedDataRows = sortedRows.slice(1);

            // The rows should still be present (sorting doesn't remove data)
            expect(sortedDataRows).toHaveLength(3);
        }
    });

    test('displays formatted dates correctly', () => {
        renderComponent();

        // Check that dates are formatted as dd/MM/yy
        expect(screen.getByText('15/01/24')).toBeInTheDocument();
        expect(screen.getByText('10/01/24')).toBeInTheDocument();
        expect(screen.getByText('20/01/24')).toBeInTheDocument();
    });

    test('displays status badges correctly', () => {
        renderComponent();

        // Check that status badges are rendered
        expect(screen.getByText('invoice.status.paid')).toBeInTheDocument();
        expect(screen.getByText('invoice.status.not_paid')).toBeInTheDocument();
        expect(screen.getByText('invoice.status.issued')).toBeInTheDocument();
    });
});
