import { configureStore } from '@reduxjs/toolkit';
import basicSlices from 'gtx-react/rtk/slices';
import invoices from './invoices-administration/slices/invoices';
import agency from './invoices-administration/slices/agency';

export const invoicesStore = configureStore({
    reducer: {
        ...basicSlices,
        invoices,
        agency,
    },
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type InvoicesState = ReturnType<typeof invoicesStore.getState>;
export type InvoicesDispatch = typeof invoicesStore.dispatch;
