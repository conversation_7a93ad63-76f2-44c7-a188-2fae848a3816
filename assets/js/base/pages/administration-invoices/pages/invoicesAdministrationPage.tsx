import { INVOICES_ADMIN_PATH } from '../web-api/endpoints';

import { GtxPageWithStore } from 'gtx-react/components/GtxApp/GtxApp';
import { AdministrationInvoicesView } from '../views/AdministrationInvoicesView';
import { invoicesStore } from '../redux/store';
import { setInvoices } from '../redux/invoices-administration/slices/invoices';
import { getInvoices } from '../web-api/api';
import { HeaderActions } from '../components/HeaderActions';
import { setAgency } from '../redux/invoices-administration/slices/agency';
import { extractAgencyData } from '../utils/utils';

export const invoicesAdministrationPage: GtxPageWithStore = {
    store: invoicesStore,
    container: AdministrationInvoicesView,
    path: INVOICES_ADMIN_PATH,
    initFunc: async (dispatch) => {
        const [{ data }, agency] = await Promise.all([
            getInvoices(),
            extractAgencyData(),
        ]);

        dispatch(setInvoices(data));
        dispatch(setAgency(agency));
    },
    header: {
        actions: HeaderActions,
    },
};
