import { trans } from '@pepita-i18n/babelfish';
import { CardList } from 'gtx-react/components/CardList/CardList';
import {
    CardField,
    CardListActions,
    CardListConfigs,
} from 'gtx-react/components/CardList/types';
import { TableData } from 'gtx-react/components/GxTable/types';
import React, { useMemo } from 'react';
import { AgencyListQueryData } from '../../../hooks/queries';

type AgencyTableSmallScreenProps = {
    data: AgencyListQueryData;
    onChooseAgency: (agencyId: number) => void;
};

type AgencyRowProps = {
    name: string;
    address: string;
    id: string;
};

type AgencyCardItem = {
    data: AgencyRowProps;
};

const renderCardFields: CardField<AgencyCardItem>[] = [
    {
        key: 'name',
        label: trans('label.name'),
        renderContent: ({ data }) => <span>{data.name}</span>,
    },
    {
        key: 'address',
        label: trans('label.address'),
        renderContent: ({ data }) => <span>{data.address}</span>,
    },
];

const rawConfig: CardListConfigs = {
    itemSelection: false,
    emptyState: {
        text: trans('label.no_results_found'),
        image: '/bundles/base/getrix/common/img/empty-state/empty-state.png',
    },
};

export const AgencyTableSmallScreen = ({
    data,
    onChooseAgency,
}: AgencyTableSmallScreenProps) => {
    const tableData: TableData<AgencyCardItem> = useMemo(
        () => ({
            items: data.list.map((agency) => ({
                favourite: false,
                id: agency.id.toString(),
                data: agency,
                customRowClassName: '',
                selected: false,
            })),
            extraItemFields: ['id'],
        }),
        [data.list]
    );

    const listActions: CardListActions = useMemo(
        () => ({
            main: null,
            menu: null,
            quick: [
                {
                    label: trans('label.switch_agency'),
                    icon: 'circular-arrows',
                    action: (id) => {
                        onChooseAgency(Number(id));
                    },
                },
            ],
            bulk: null,
        }),
        [onChooseAgency]
    );

    return (
        <CardList<AgencyTableCardsData>
            cardsData={tableData}
            cardFields={renderCardFields}
            configs={rawConfig as CardListConfigs}
            actions={listActions}
            twoColumnsView={false}
            displayShowMoreButton={false}
        />
    );
};
