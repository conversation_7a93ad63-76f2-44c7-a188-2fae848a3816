import { useQuery } from '@tanstack/react-query';
import { queries } from '../utils';
import { getAgenciesList } from '../web-api';
import { Agency, AgencyListResponse } from '../types';

function getAgenctStreet(agency: Agency) {
    if (!agency?.address?.city?.name) {
        return '---';
    }

    const city = agency.address.city.name;
    const province = agency?.address?.city?.province?.name ? agency.address.city.province.name : null;
    const street = agency?.address?.address?.street ? agency.address.address.street : null;
    const streetNumber = agency?.address?.address?.number ? agency.address.address.number : null;

    if (!street) {
        return `${city} ${province ? `(${province})` : ''}`;
    }

    return `${street} ${streetNumber ? streetNumber : ''}, ${city} ${province ? `(${province})` : ''}`;
}

function agencyListSelect(response: AgencyListResponse) {
    if (response.status === 'error') {
        throw new Error(response.data.error);
    }

    return {
        ...response.data,
        list: response.data.list.map((agency) => ({
            name: agency.name,
            id: agency.id,
            address: getAgenctStreet(agency),
        })),
    };
}

export type AgencyListQueryData = ReturnType<typeof agencyListSelect>;

export const useAgenciesQuery = (params: { page: number }) => {
    return useQuery({
        queryKey: queries.listPaginated(params.page),
        queryFn: () =>
            getAgenciesList({
                page: params.page,
            }),
        select: agencyListSelect,
    });
};
