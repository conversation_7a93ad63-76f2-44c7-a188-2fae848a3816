import { ApiResponse, WithPagination } from 'types/api-response';

export type Address = {
    street: string;
    number: string;
};

export type Province = {
    id: string;
    name: string;
    region: Region;
};

export type City = {
    id: number;
    name: string;
    province: Province;
    cityMacroZoneType: null;
    chiefTown: null;
};

export type Country = {
    id: string;
    name: string;
};

export type Region = {
    id: string;
    name: string;
    country: Country;
};

export type Agency = {
    id: number;
    name: string;
    username: string;
    address: {
        address: Address;
        city: City;
    };
};

export type AgencyList = WithPagination<Agency>;

export type AgencyListResponse = ApiResponse<AgencyList, string>;
