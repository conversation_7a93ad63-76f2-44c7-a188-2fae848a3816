import { trans } from '@pepita-i18n/babelfish';
import { Formik, useFormikContext } from 'formik';
import { UserInputAutocompleteFormik } from 'gtx-react/containers/CustomerInputAutocompleteFormik/UserInputAutocompleteFormik';
import { GetMatchesListFilters } from '../types/matches';
import { FC, PropsWithChildren, useMemo } from 'react';
import { Button } from '@gx-design/button';
import { Modal } from '@gx-design/modal';
import { GxFkCheckbox, GxFkInput } from 'gtx-react/components/gx-formik';
import { MATCHESLIST_DEFAULT_FILTERS } from '../constants/MatchesListDefaultFilters';
import * as Yup from 'yup';
import { email } from 'constants/yupValidations';
import { useFiltersContext } from '../contexts/filters/useFiltersContext';
import { useListenCounters } from 'gtx-react/hooks/useListenCounters';
import { getSmartCounterString } from 'lib/getSmartCounterString';

const filterValidationSchema = Yup.object().shape({
    customerEmail: email(),
});

const FilterBox: FC = () => {
    const { filters } = useFiltersContext();
    const { counters } = useListenCounters();

    return (
        <div className="gx-row">
            <div className="gx-col-xs-12 gx-col-sm-12 gx-col-md-4 gx-box-row">
                <UserInputAutocompleteFormik
                    id={filters.customerId}
                    queryParams={{
                        hasMatches: true,
                    }}
                    hideEditBtn
                />
            </div>
            <div className="gx-col-xs-12 gx-col-sm-12 gx-col-md-4 gx-box-row">
                <GxFkInput
                    name={'customerEmail' as keyof GetMatchesListFilters}
                    label={trans('label.mail')}
                    placeholder={trans('label.insert_customer_mail')}
                />
            </div>
            <div className="gx-col-xs-12 gx-col-sm-12 gx-col-md-4 gx-pushtop-md gx-box-row">
                <GxFkCheckbox
                    name="isNew"
                    variant="button"
                    label={`${trans(
                        'label.only_new_ones'
                    )} ${getSmartCounterString(counters.newMatches)}`}
                />
            </div>
        </div>
    );
};

type MatchesFilterModalActionsProps = {
    setIsOpen: (isOpen: boolean) => void;
};

const MatchesFilterModalActions: FC<MatchesFilterModalActionsProps> = ({
    setIsOpen,
}) => {
    const { submitForm, resetForm } = useFormikContext();

    return (
        <>
            <Button
                onClick={() => {
                    setIsOpen(false);
                    resetForm();
                }}
            >
                {trans('label.close')}
            </Button>
            <Button
                variant="accent"
                className="btn--cerca"
                onClick={() => {
                    submitForm();
                }}
            >
                {trans('label.apply_filters')}
            </Button>
        </>
    );
};

type MatchesListFiltersModalProps = {
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
};

const FiltersModal: FC<PropsWithChildren<MatchesListFiltersModalProps>> = ({
    isOpen,
    setIsOpen,
}) => {
    const { resetForm } = useFormikContext();

    return (
        <Modal
            isOpen={isOpen}
            title={trans('label.filter')}
            onClose={() => {
                setIsOpen(false);
                resetForm();
            }}
            className="matches-filters-modal"
            footer={
                <MatchesFilterModalActions
                    setIsOpen={(isOpen: boolean) => {
                        setIsOpen(isOpen);
                    }}
                />
            }
        >
            <div className="matches-filters-modal__content">
                <FilterBox />
            </div>
        </Modal>
    );
};
export const MatchesListFiltersModal: FC<MatchesListFiltersModalProps> = ({
    isOpen,
    setIsOpen,
}) => {
    const { filters, setFilters } = useFiltersContext();

    const initialValues = useMemo<GetMatchesListFilters>(() => {
        return {
            ...MATCHESLIST_DEFAULT_FILTERS,
            ...filters,
        };
    }, [filters]);

    return (
        <>
            <Formik
                initialValues={initialValues}
                enableReinitialize
                onSubmit={(values: GetMatchesListFilters) => {
                    setFilters(values);
                    setIsOpen(false);
                }}
                validationSchema={filterValidationSchema}
                validateOnChange={false}
                validateOnBlur={false}
                validateOnMount={false}
            >
                <FiltersModal isOpen={isOpen} setIsOpen={setIsOpen} />
            </Formik>
        </>
    );
};
