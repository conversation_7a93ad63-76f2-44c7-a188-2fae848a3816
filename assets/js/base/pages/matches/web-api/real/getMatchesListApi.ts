import { http } from '@pepita/http';
import { GetMatchesListOption, MatchItem } from '../../types/matches';
import { endpoints } from '../endpoints';
import { ApiResponse } from 'types/api-response';

export type GetMatchesApiReturnType = { data: MatchItem[]; total: number };

type GetMatchesApiType = (params: GetMatchesListOption) => Promise<GetMatchesApiReturnType>;

export type MatchesApiResponse = ApiResponse<{
    list: MatchItem[];
    totalResults: number;
    pagination: {
        results: number;
        page: number;
    };
}>;

export const getMatchesListApi: GetMatchesApiType = ({ page, results, filters, search }) => {
    return http
        .get(endpoints.MATCHES_LIST, {
            searchParams: {
                ...filters,
                ...(search && search !== '' ? { propertyRef: search } : {}),
                page,
                results,
            },
        })
        .json()
        .then((resp: MatchesApiResponse) => {
            if (resp.status === 'success') {
                return {
                    data: resp.data.list,
                    total: resp.data.totalResults,
                };
            } else {
                throw new Error(`Problems fetching Matches list ${endpoints.MATCHES_LIST}`);
            }
        });
};
