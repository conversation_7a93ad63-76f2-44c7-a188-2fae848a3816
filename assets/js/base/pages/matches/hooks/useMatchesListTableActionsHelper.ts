import { useCallback } from 'react';
import { MatchItem } from '../types/matches';
import { ItemActionsHelper } from 'gtx-react/components/GxTable/types';

type MatchesListActionHelperOptions = {
    data: MatchItem[] | undefined;
};

/**
 * torna itemActionsHelper callback da passare a GxTable config.actionsHelper
 * rende disponibili le action in row solo per gli elementi nuovi
 */
export const useMatchesListTableActionsHelper = ({ data }: MatchesListActionHelperOptions) => {
    const itemActionsHelper = useCallback<ItemActionsHelper>(
        (id, actionsData, currentActionType) => {
            const actions = actionsData[currentActionType] || null;
            const item = data?.find((itm) => itm.id.toString() === id.toString());
            return item && item.lifeCycleState?.name === 'new' ? actions : null;
        },
        [data]
    );

    return { itemActionsHelper };
};
