import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { ucFirst } from 'lib/strings-formatter';
import { useEffect, useRef } from 'react';
import { PropertyStatus } from '../types';

export const useTrackPageFirstAccess = (status: PropertyStatus) => {
    const { trackEvent } = useMixpanelContext();
    const hasTrackedPageLoad = useRef(false);

    useEffect(() => {
        // Only trigger the event when the page is first loaded (not on subsequent navigations)
        if (!hasTrackedPageLoad.current) {
            hasTrackedPageLoad.current = true;

            trackEvent({
                event: 'properties_portal_area_access',
                extra: {
                    ['list_view']: ucFirst(status),
                },
            });
        }
    }, [status, trackEvent]);
};
