import { render, screen } from '#tests/react/testing-library-enhanced';
import { describe, it, expect, vi } from 'vitest';
import { VisibilityPopoverContent } from './VisibilityPopover';
import { ExtraVisibilities } from '../PropertyList/helpers/property-list-mapper';

// Mock the Icon component
vi.mock('@gx-design/icon', () => ({
    Icon: ({ name, className }: { name: string; className?: string }) => (
        <span data-testid={`icon-${name}`} className={className}>
            {name}
        </span>
    ),
}));

describe('VisibilityPopoverContent', () => {
    const defaultProps = {
        title: 'Property Visibility',
        extraVisibilities: {
            type: 'AVAILABLE',
            payload: {},
        } as ExtraVisibilities,
        adPortal: 'Immobiliare.it',
    };

    it('renders the title correctly', () => {
        render(<VisibilityPopoverContent {...defaultProps} />);

        expect(screen.getByText('Property Visibility')).toBeInTheDocument();
    });

    describe('when visibility type is AVAILABLE', () => {
        it('shows visible status for both agency page and portal results', async () => {
            render(<VisibilityPopoverContent {...defaultProps} />);

            expect(
                screen.getByText('label.is_visible_on_agency_page')
            ).toBeInTheDocument();
            expect(
                screen.getByText('label.is_visible_in_portal_results')
            ).toBeInTheDocument();

            const successIcons = await screen.findAllByTestId(
                'icon-check-circle--active'
            );

            expect(successIcons).toHaveLength(2);

            successIcons.forEach((icon) => {
                expect(icon).toHaveClass('gx-text-success');
            });
        });
    });

    describe('when visibility type is AGENCY_PAGE', () => {
        it('shows visible for agency page but not for portal results', () => {
            render(
                <VisibilityPopoverContent
                    {...defaultProps}
                    extraVisibilities={
                        {
                            type: 'AGENCY_PAGE',
                            payload: {},
                        } as ExtraVisibilities
                    }
                />
            );

            expect(
                screen.getByText('label.is_visible_on_agency_page')
            ).toBeInTheDocument();
            expect(
                screen.getByText('label.not_visible_in_portal_results')
            ).toBeInTheDocument();
        });

        it('displays mixed success and error icons', () => {
            render(
                <VisibilityPopoverContent
                    {...defaultProps}
                    extraVisibilities={
                        {
                            type: 'AGENCY_PAGE',
                            payload: {},
                        } as ExtraVisibilities
                    }
                />
            );

            expect(screen.getByTestId('icon-check-circle--active')).toHaveClass(
                'gx-text-success'
            );
            expect(screen.getByTestId('icon-cross-circle--active')).toHaveClass(
                'gx-text-error'
            );
        });
    });

    describe('when visibility type is SECRET', () => {
        it('shows not visible for both agency page and portal results', () => {
            render(
                <VisibilityPopoverContent
                    {...defaultProps}
                    extraVisibilities={
                        {
                            type: 'SECRET',
                            payload: {},
                        } as ExtraVisibilities
                    }
                />
            );

            expect(
                screen.getByText('label.not_visible_on_agency_page')
            ).toBeInTheDocument();
            expect(
                screen.getByText('label.not_visible_in_portal_results')
            ).toBeInTheDocument();
        });

        it('displays the secret but shareable message', () => {
            render(
                <VisibilityPopoverContent
                    {...defaultProps}
                    extraVisibilities={
                        {
                            type: 'SECRET',
                            payload: {},
                        } as ExtraVisibilities
                    }
                />
            );

            expect(
                screen.getByText('label.secret_but_shareable')
            ).toBeInTheDocument();
        });
    });

    describe('edge cases', () => {
        it('handles undefined extraVisibilities type gracefully', () => {
            render(
                <VisibilityPopoverContent
                    {...defaultProps}
                    extraVisibilities={{ type: undefined as any }}
                />
            );

            expect(
                screen.getByText('label.not_visible_on_agency_page')
            ).toBeInTheDocument();
            expect(
                screen.getByText('label.not_visible_in_portal_results')
            ).toBeInTheDocument();
        });

        it('handles empty title', () => {
            render(
                <VisibilityPopoverContent
                    {...defaultProps}
                    title=""
                    extraVisibilities={
                        {
                            type: 'AVAILABLE',
                            payload: {},
                        } as ExtraVisibilities
                    }
                />
            );

            expect(
                screen.getByText('label.is_visible_on_agency_page')
            ).toBeInTheDocument();
        });

        it('does not show secret section for non-SECRET types', () => {
            render(
                <VisibilityPopoverContent
                    {...defaultProps}
                    extraVisibilities={
                        {
                            type: 'AVAILABLE',
                            payload: {},
                        } as ExtraVisibilities
                    }
                />
            );

            expect(
                screen.queryByText('label.secret_but_shareable')
            ).not.toBeInTheDocument();
        });
    });
});
