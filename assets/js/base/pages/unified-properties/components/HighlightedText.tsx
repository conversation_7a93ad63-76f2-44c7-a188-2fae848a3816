import { getMatchingText } from 'lib/getMatchingText';
import { useSearchParams } from 'react-router-dom';
import { Fragment } from 'react';

export const HighlightedText = (props: { text: string }) => {
    const [filters] = useSearchParams();

    if (!filters.has('code') || !filters.has('term')) {
        return props.text;
    }

    const terms = filters.get('term')?.split(',') ?? [''];

    return getMatchingText(props.text, terms).map(({ isMatch, key, text }) => (
        <Fragment key={key}>
            {isMatch ? (
                <b
                    data-testid="highlighted-text"
                    style={{ backgroundColor: '#ffd498' }}
                >
                    {text}
                </b>
            ) : (
                text
            )}
        </Fragment>
    ));
};
