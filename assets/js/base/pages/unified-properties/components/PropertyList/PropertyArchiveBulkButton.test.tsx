import { render, screen } from '#tests/react/testing-library-enhanced';
import { server } from '#tests/vitest/setup';
import { NotifyProvider } from '@gx-design/snackbar';
import { QueryClientProvider } from '@tanstack/react-query';
import { MixpanelProvider } from 'gtx-react/contexts/MixpanelProvider';
import { createQueryClient } from 'lib/queryClient';
import { http, HttpResponse } from 'msw';
import { describe, expect, it, vi } from 'vitest';
import PropertyArchiveBulkButton from './PropertyArchiveBulkButton';
import { archiveProperties } from 'lib/REST/requests/api/portal-properties';

const renderWithProviders = (ui: React.ReactElement) => {
    const queryClient = createQueryClient();
    return render(
        <QueryClientProvider client={queryClient}>
            <NotifyProvider>
                <MixpanelProvider
                    mixpanelConfig={{
                        area: 'Unified Properties',
                        section: 'Property List',
                        type: 'Property Archive',
                        experimental: [],
                        trackEvents: true,
                    }}
                >
                    {ui}
                </MixpanelProvider>
            </NotifyProvider>
        </QueryClientProvider>
    );
};

describe('PropertyArchiveButton', () => {
    it('should show success notification on successful archive', async () => {
        const { user } = renderWithProviders(
            <PropertyArchiveBulkButton
                status="active"
                toggleAllRowsSelected={vi.fn()}
                ids={[1, 2, 3]}
            />
        );

        // Click the archive button
        const button = screen.getByRole('button');
        await user.click(button);

        // Wait for the mutation to complete
        expect(
            await screen.findByText('ad.archived_success_plural')
        ).toBeInTheDocument();
    });

    it('should show success notification on successful archive (on singular id)', async () => {
        const { user } = renderWithProviders(
            <PropertyArchiveBulkButton
                status="active"
                toggleAllRowsSelected={vi.fn()}
                ids={[1]}
            />
        );

        // Click the archive button
        const button = screen.getByRole('button');
        await user.click(button);

        // Wait for the mutation to complete
        expect(
            await screen.findByText('ad.archived_success')
        ).toBeInTheDocument();
    });

    it('should show error notification on failed archive', async () => {
        // Setup mock for failed archiving

        server.use(
            http.patch(archiveProperties.endpoint, () =>
                HttpResponse.json({}, { status: 500 })
            )
        );
        const { user } = renderWithProviders(
            <PropertyArchiveBulkButton
                status="active"
                toggleAllRowsSelected={vi.fn()}
                ids={[1, 2, 3]}
            />
        );

        // Click the archive button
        const button = screen.getByRole('button');
        await user.click(button);

        expect(
            await screen.findByText('label.error_durin_operation')
        ).toBeInTheDocument();
    });
});
