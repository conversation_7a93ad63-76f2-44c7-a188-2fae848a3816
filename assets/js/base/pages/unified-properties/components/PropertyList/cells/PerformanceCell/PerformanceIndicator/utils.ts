import { RankingVariationType } from 'gtx-react/components/PerformanceRankingIndicator/types';
import { ProcessedPerformanceData, PerformanceStats } from './types';
import { PropertyPerformance } from 'lib/REST/requests/api/properties/types/performance';

/**
 * Process performance data to calculate position difference and other metrics
 */
export function processPerformanceData(
    performanceItem: PropertyPerformance | undefined,
    statsItem: PerformanceStats | undefined
): ProcessedPerformanceData {
    const statsArray = statsItem?.stats?.performanceIndexes;

    let oldPosition: number | null | undefined = null;
    let newPosition: number | null | undefined = null;

    if (statsArray?.length) {
        oldPosition = statsArray[0]?.generals?.position;
        newPosition = performanceItem?.performance?.generals?.position;
    }

    const positionDiff = oldPosition && newPosition ? oldPosition - newPosition : undefined;

    return {
        zoneSimilarPropertiesPosition: performanceItem?.performance?.generals?.position,
        zoneSimilarProperties: performanceItem?.performance?.total,
        positionDiff,
        variationType: determineVariationType(positionDiff),
    };
}

/**
 * Determine variation type based on position difference
 */
export function determineVariationType(positionDiff: number | undefined): RankingVariationType | null {
    if (typeof positionDiff === 'number') {
        if (positionDiff > 0) {
            return 'positive';
        } else if (positionDiff < 0) {
            return 'negative';
        } else {
            return 'equal';
        }
    }
    return null;
}
