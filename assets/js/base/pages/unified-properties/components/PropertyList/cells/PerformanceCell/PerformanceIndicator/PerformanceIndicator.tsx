import { trans } from '@pepita-i18n/babelfish';
import { PerformanceBadge } from 'gtx-react/components/PerformanceBadge';
import { PERFORMANCE_TAGS } from 'constants/propertyPerformance';
import { FC, memo } from 'react';
import { PerformanceIndicatorProps } from './types';
import { usePerformanceData } from './usePerformanceData';
import { PerformanceVariation } from './PerformanceVariation';
import { Button } from '@gx-design/button';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { Icon } from '@gx-design/icon';
import { PropertiesTouchPoint } from 'types/mixpanel/properties';

/**
 * Performance Indicator component displaying property performance metrics
 */
export const PerformanceIndicator: FC<PerformanceIndicatorProps> = memo(
    (props) => {
        const { trackEvent } = useMixpanelContext();

        // Use custom hook for data fetching logic
        const { data, isLoading, isError } = usePerformanceData(props);

        // Error state handling
        if (isError) {
            return <>{trans('label.error')}</>;
        }

        //TODO: Loading state handling
        if (isLoading) {
            return <>...</>;
        }

        // No data handling
        if (!data?.zoneSimilarPropertiesPosition) {
            console.warn(
                'Performance data is unavailable. Please check the property ID or network connection.'
            );
            return <>{trans('label.property_performance.unavailable')}</>;
        }

        // Get performance score from property index
        const performanceTag = PERFORMANCE_TAGS.find(
            (tag) => tag.value === props.relativeIndexId
        );

        return (
            <>
                <div className="crm-cell-contentItem crm-cell-contentItem--xsGap">
                    <PerformanceBadge
                        performance={performanceTag?.score || 'medium'}
                        large={false}
                    >
                        <span>
                            <b>{`${data.zoneSimilarPropertiesPosition}°`}</b>
                            {` ${trans('label.out_of')} ${
                                data.zoneSimilarProperties
                            }`}
                        </span>
                    </PerformanceBadge>
                    {data.variationType && (
                        <PerformanceVariation
                            variationType={data.variationType}
                            positionDiff={data.positionDiff}
                        />
                    )}
                </div>
                <Button
                    as="a"
                    onClick={() => {
                        trackEvent({
                            event: 'properties_view_performance_listing',
                            extra: {
                                ['property_id']: props.currentPropertyId,
                                ['touch_point']:
                                    PropertiesTouchPoint.PERFORMANCE_COLUMN,
                                ['list_view']: props.listView
                            },
                        });
                    }}
                    size="small"
                    className="explore-cta-promotion"
                    target="_blank"
                    href={props.performanceUrl}
                >
                    <Icon name="podium" />
                    <span>{trans('label.explore')}</span>
                </Button>
            </>
        );
    }
);

PerformanceIndicator.displayName = 'PerformanceIndicator';
