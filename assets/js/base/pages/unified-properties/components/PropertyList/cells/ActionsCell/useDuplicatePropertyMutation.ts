import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UnexpectedRestError } from 'lib/REST';
import { duplicateProperty } from 'lib/REST/requests/rest/property';
import { propertiesOptions } from 'lib/REST/requests/api/properties/query-factory';

export function useDuplicatePropertyMutation() {
    const { showNotification } = useNotifyContext();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: duplicateProperty,
        onSuccess: (_, { query }) => {
            const sp = new URLSearchParams();
            sp.set('step', '2');
            sp.set('action', 'duplica');
            sp.set('idAnnuncio', String(query.id));
            sp.set('tipo', '');

            queryClient.invalidateQueries({ queryKey: propertiesOptions });

            window.open(`/inserimento_annuncio.php?${sp.toString()}`, '_blank');
        },
        onError: async (err, { query }) => {
            if (err instanceof UnexpectedRestError) {
                const data = await err.response.json();

                if (data && data.adId) {
                    const sp = new URLSearchParams();
                    sp.set('step', '2');
                    sp.set('action', 'duplica');
                    sp.set('idAnnuncio', String(data.adId));
                    sp.set('tipo', String(query.id));

                    if (data.type === 'images') {
                        sp.set('immagini', '0');
                    } else if (data.type === 'plans') {
                        sp.set('planimetrie', '0');
                    } else if (data.type === 'media') {
                        sp.set('media', '0');
                    }

                    return window.open(`/inserimento_annuncio.php?${sp.toString()}`, '_blank');
                }
            }

            showNotification({
                type: 'error',
                message: trans('label.error_durin_operation'),
            });
        },
    });
}
