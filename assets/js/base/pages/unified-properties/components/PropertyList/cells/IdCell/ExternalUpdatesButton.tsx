import { Icon } from '@gx-design/icon';
import { useNotifyContext } from '@gx-design/snackbar';
import { Tooltip } from '@gx-design/tooltip';
import { trans } from '@pepita-i18n/babelfish';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { changePropertyIntegrationFlag } from 'lib/REST/requests/api/properties/properties';
import { usePropertiesQueryKey } from '../../../PropertiesQueryKeyProvider';
import produce from 'immer';
import { ListProperties } from 'lib/REST/requests/api/properties/types/list';
import clsx from 'clsx';

function updateOldData(args: { propertyId: number; status: boolean }) {
    return (list: ListProperties | undefined) =>
        produce(list, (draft) => {
            const found = draft?.list.find(
                (property) => property.id === args.propertyId
            );

            if (found) {
                // This is weird, but the API returns a boolean for the status 🤷‍♂️
                found.integrationFlag = Number(args.status);
            }
        });
}

export default function ExternalUpdatesButton(props: {
    status: 'opened' | 'closed' | 'disabled';
    propertyId: number;
}) {
    const { showNotification } = useNotifyContext();
    const queryClient = useQueryClient();
    const { queryKey } = usePropertiesQueryKey();

    const externalUpdatesMutation = useMutation({
        mutationFn: changePropertyIntegrationFlag,
        onMutate: (variables) => {
            // optimistically update the cache
            queryClient.setQueryData(
                queryKey,
                updateOldData({
                    propertyId: props.propertyId,
                    status: variables.query.status,
                })
            );

            // return a context object with the previous data
            return {
                previousData: props.status === 'opened',
            };
        },
        onError: (_, __, context) => {
            // rollback the optimistic update if the mutation fails
            showNotification({
                type: 'error',
                message: trans('label.error_durin_operation'),
            });

            if (context === undefined) {
                return;
            }

            queryClient.setQueryData(
                queryKey,
                updateOldData({
                    propertyId: props.propertyId,
                    status: context.previousData,
                })
            );
        },
    });

    const label = trans(
        {
            opened: 'label.ad.block_ext_updates',
            closed: 'label.ad.enable_ext_updates',
            disabled: 'label.ad.no_ext_system',
        }[props.status] || 'label.ad.no_ext_system'
    );

    return (
        <Tooltip text={label}>
            <span>
                <button
                    className={clsx(
                        'crm-cell-id__toolsButton',
                        props.status === 'opened' && 'is-selected'
                    )}
                    disabled={props.status === 'disabled'}
                    onClick={() => {
                        externalUpdatesMutation.mutate({
                            params: { id: props.propertyId },
                            query: { status: props.status === 'opened' },
                        });
                    }}
                >
                    <Icon
                        name={
                            props.status === 'opened'
                                ? 'unlock--active'
                                : 'lock'
                        }
                    />
                    <span className="gx-sr-only">{label}</span>
                </button>
            </span>
        </Tooltip>
    );
}
