import { Button } from '@gx-design/button';
import { trans } from '@pepita-i18n/babelfish';
import { ENDPOINTS } from 'constants/property';
import { useOpenPropertyConvertToRentDialog } from './PropertyConvertToRentDialog';
import { Modal } from '@gx-design/modal';

export function PropertyConvertToRentErrorModal({
    id,
    isOpen,
    onClose,
}: {
    id: number;
    isOpen: boolean;
    onClose: () => void;
}) {
    const { close } = useOpenPropertyConvertToRentDialog();

    return (
        <Modal
            footer={
                <>
                    <Button variant="ghost" onClick={close}>
                        {trans('label.cancel')}
                    </Button>
                    <Button
                        variant="accent"
                        as="a"
                        href={`${ENDPOINTS.propertyAdd}?step=2&idAnnuncio=${id}`}
                    >
                        {trans('label.confirm')}
                    </Button>
                </>
            }
            title={trans('convert_to_sold_rented.error.title')}
            isOpen={isOpen}
            onClose={onClose}
        >
            {trans('convert_to_sold_rented.error.generic')}
        </Modal>
    );
}
