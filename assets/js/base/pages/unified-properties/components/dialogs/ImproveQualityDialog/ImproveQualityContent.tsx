import { useQuery, useSuspenseQuery } from '@tanstack/react-query';
import { ImprovePropertyQuality } from 'gtx-react/containers/ImprovePropertyQuality';
import useImprovePropertyQualityConfigs from 'gtx-react/hooks/useImprovePropertyQualityConfigs';
import { createGetQualityDetailsOptions } from 'lib/REST/requests/api/properties/query-factory';
import { useConfigContext } from '../../ConfigProvider';
import { trans } from '@pepita-i18n/babelfish';

import { usePropertiesQueryKey } from '../../PropertiesQueryKeyProvider';
import { createGetPropertiesOptions } from 'lib/REST/requests/api/properties/query-factory';
import { IconProps } from '@gx-design/icon';
import { ENDPOINTS } from 'constants/property';
import { getMeQueryOptions } from 'lib/REST/requests/rest/profile/query-factory';

/**
 * @description Define the key pair for different property types
 *
 * This maps the type of property to the corresponding configuration key
 * used in the ImprovePropertyQuality component. */
const keyPair = {
    auction: 'portalAuction',
    property: 'portalProperty',
    ['new_construction']: 'portalNewConstruction',
} as const;

export function ImproveQualityContent(props: {
    propertyId: number;
    type: 'auction' | 'property' | 'new_construction';
}) {
    const configs = useImprovePropertyQualityConfigs(
        {
            AUCTION_EDIT: `${ENDPOINTS.propertyAdd}?step={step}&idAnnuncio={id}&tipo=${window.gtxConstants.CATEGORIA_ASTE}`,
            AUCTION_MAIN_PROPERTY_EDIT: `${ENDPOINTS.propertyAdd}?step=3b&idAnnuncio={id}&categoria=${window.gtxConstants.CATEGORIA_ASTE}
            )}&tipo=${window.gtxConstants.CATEGORIA_ASTE}&idAstaImmobile={auctionPropertyId}`,
            NEW_CONSTRUCTION_EDIT: `${ENDPOINTS.newConstructionAdd}?idAnnuncio={id}&step={step}`,
            PROPERTY_EDIT: `${ENDPOINTS.propertyAdd}?step={step}&idAnnuncio={id}`,
        },
        keyPair[props.type]
    );

    const { data: qualityData, isLoading: isQualityLoading } = useQuery(
        createGetQualityDetailsOptions({
            params: { propertyId: String(props.propertyId) },
        })
    );

    const meQuery = useSuspenseQuery(getMeQueryOptions);

    const { queryArgs } = usePropertiesQueryKey();

    const propertyQuery = useQuery({
        ...createGetPropertiesOptions(queryArgs),
        select: (data) =>
            data.list.find((property) => property.id === props.propertyId),
    });

    const { adPortal } = useConfigContext();

    const getUIProps = () => {
        if (!propertyQuery.data || !qualityData) {
            return {
                propertyRanking: 0,
                propertyId: '',
                qualityItems: [],
                agencyContact: null,
                portalName: adPortal,
                isLoading: isQualityLoading,
            };
        }

        const qualityItems = configs.map((config) => {
            const isVisible = config.isVisible
                ? config.isVisible(propertyQuery.data)
                : true;
            const isCompleted = Boolean(qualityData[config.key]?.outcome);

            let description = '';
            if (!isCompleted) {
                const rawDescription = config.description(propertyQuery.data);
                if (
                    rawDescription &&
                    config.transPlaceholder &&
                    config.placeholderKey
                ) {
                    description = trans(rawDescription, {
                        [config.transPlaceholder]:
                            qualityData.description[config.placeholderKey],
                    });
                } else {
                    description = rawDescription;
                }
            }

            return {
                key: config.key,
                title: config.title,
                isCompleted,
                issueType: config.issueType(propertyQuery.data) as
                    | 'warning'
                    | 'positive'
                    | 'negative',
                description: description || undefined,
                actionUrl: isCompleted
                    ? undefined
                    : config.action.replace(
                          '{id}',
                          props.propertyId.toString()
                      ),
                actionLabel: config.actionLabel,
                actionIcon: config.actionIcon as IconProps['name'],
                isVisible,
            };
        });

        return {
            propertyRanking: propertyQuery.data.ranking,
            propertyId: String(props.propertyId),
            qualityItems,
            agencyContact: {
                name: meQuery.data?.responsible.nome ?? '',
                phone: meQuery.data?.responsible?.telefono ?? '',
                email: meQuery.data?.responsible?.email ?? '',
            },
            portalName: adPortal,
            isLoading: isQualityLoading,
        };
    };

    const uiProps = getUIProps();

    if (!uiProps) {
        return null;
    }

    return <ImprovePropertyQuality {...uiProps} />;
}
