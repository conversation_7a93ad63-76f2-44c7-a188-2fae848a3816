import { Button } from '@gx-design/button';
import { Modal } from '@gx-design/modal';
import { trans } from '@pepita-i18n/babelfish';
import { RankingDetailModal } from 'gtx-react/containers/RankingDetailModal/RankingDetailModal';
import {
    createContext,
    PropsWithChildren,
    useCallback,
    useContext,
    useMemo,
    useState,
} from 'react';

type PropertyRankingDetailDialogValues = string | null;

const PropertyRankingDetailDialogReaderContext = createContext<
    PropertyRankingDetailDialogValues | undefined
>(undefined);

export function usePropertyRankingDetailDialogReader() {
    const context = useContext(PropertyRankingDetailDialogReaderContext);
    if (context === undefined) {
        throw new Error(
            'usePropertyRankingDetailDialogReader must be used within a PropertyRankingDetailDialogProvider'
        );
    }
    return context;
}

const PropertyRankingDetailDialogWriterContext = createContext<
    ((args: PropertyRankingDetailDialogValues) => void) | undefined
>(undefined);

export function usePropertyRankingDetailDialogWriter() {
    const context = useContext(PropertyRankingDetailDialogWriterContext);
    if (context === undefined) {
        throw new Error(
            'usePropertyRankingDetailDialogWriter must be used within a PropertyRankingDetailDialogProvider'
        );
    }
    return context;
}

export function PropertyRankingDetailDialogProvider(props: PropsWithChildren) {
    const [id, setId] = useState<PropertyRankingDetailDialogValues>(null);

    /**
     * Callback to set the ids in the context
     * @param newIds
     */
    const setIdsCallback = useCallback(
        (id: PropertyRankingDetailDialogValues) => {
            setId(id);
        },
        []
    );

    return (
        <PropertyRankingDetailDialogWriterContext.Provider
            value={setIdsCallback}
        >
            <PropertyRankingDetailDialogReaderContext.Provider value={id}>
                {props.children}
            </PropertyRankingDetailDialogReaderContext.Provider>
        </PropertyRankingDetailDialogWriterContext.Provider>
    );
}

export function useOpenPropertyRankingDetailDialog() {
    const setId = usePropertyRankingDetailDialogWriter();

    return useMemo(
        () => ({
            open: (value: PropertyRankingDetailDialogValues) => {
                setId(value);
            },
            close: () => {
                setId(null);
            },
        }),
        [setId]
    );
}

export function PropertyRankingDetailDialog() {
    const { close } = useOpenPropertyRankingDetailDialog();
    const id = usePropertyRankingDetailDialogReader();

    return (
        <Modal
            isOpen={Boolean(id)}
            title={trans('label.ad_quality_calculation')}
            onClose={close}
            footer={<Button onClick={close}>{trans('label.ok')}</Button>}
        >
            {id && <RankingDetailModal propertyId={id} />}
        </Modal>
    );
}
