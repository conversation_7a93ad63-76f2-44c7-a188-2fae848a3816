import { Badge } from '@gx-design/badge';
import { SECRET_PROPERTY_VISIBILITY_KEY } from 'constants/propertyVisibilities';
import { ReactNode } from 'react';
import { AnyString } from 'types/utils';
import { trans } from '@pepita-i18n/babelfish';
import { ucFirst } from 'lib/strings-formatter';
import { VisibilityPopoverContent } from '../VisibilityPopover/VisibilityPopover';
import { Popover } from '@gx-design/popover';
import { ExtraVisibilities } from '../PropertyList/helpers/property-list-mapper';
import { Tooltip } from '@gx-design/tooltip';

export const visibilityNameMap = new Map([
    [
        'searchable',
        {
            label: window.gtxConstants.PREMIUM_VISIBILITY_NAME,
            className: 'premium',
        },
    ],
    [
        'showcase',
        {
            label: window.gtxConstants.SHOWCASE_VISIBILITY_NAME,
            className: 'showcase',
        },
    ],
    [
        'star',
        {
            label: window.gtxConstants.STAR_VISIBILITY_NAME,
            className: 'star',
        },
    ],
    [
        'top',
        {
            label: window.gtxConstants.TOP_VISIBILITY_NAME,
            className: 'top',
        },
    ],
    [
        'sky',
        {
            label: window.gtxConstants.SKY_VISIBILITY_NAME,
            className: 'sky',
        },
    ],
    [
        SECRET_PROPERTY_VISIBILITY_KEY,
        {
            label: ucFirst(trans('label.secret')),
            className: 'secret',
        },
    ],
]);

export function Extravisibility(props: {
    name?: AnyString<'searchable' | 'showcase' | 'star' | 'top' | 'sky'> | null;
    emptyElement?: ReactNode;
    short?: boolean;
}) {
    if (!props.name) {
        return <>{props.emptyElement}</>;
    }

    const visibilityName = visibilityNameMap.get(props.name);

    if (!visibilityName) {
        return <>{props.emptyElement}</>;
    }

    return (
        <Tooltip text={visibilityName.label} position="top">
            <Badge
                className={`visibility-${visibilityName.className}`}
                text={
                    props.short
                        ? visibilityName.label.charAt(0)
                        : visibilityName.label
                }
            />
        </Tooltip>
    );
}

export function ExtravisibilityWithPopover(props: {
    name?: AnyString<'searchable' | 'showcase' | 'star' | 'top' | 'sky'> | null;
    emptyElement?: ReactNode;
    short?: boolean;
    value: ExtraVisibilities;
    adPortal: string;
}) {
    if (!props.name) {
        return <>{props.emptyElement}</>;
    }

    const visibilityName = visibilityNameMap.get(props.name);

    if (!visibilityName) {
        return <>{props.emptyElement}</>;
    }

    return (
        <Popover
            title=""
            onEdge={false}
            large={false}
            position="top"
            content={
                <VisibilityPopoverContent
                    title={trans('label.ad_is_visible')}
                    extraVisibilities={props.value}
                    adPortal={props.adPortal}
                />
            }
        >
            <Badge
                className={`visibility-${visibilityName.className}`}
                text={
                    props.short
                        ? visibilityName.label.charAt(0)
                        : visibilityName.label
                }
            />
        </Popover>
    );
}
