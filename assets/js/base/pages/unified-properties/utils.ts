import { trans } from '@pepita-i18n/babelfish';
import * as yup from 'yup';
import { PropertyStatus } from './types';
import { AnyString } from 'types/utils';

// TODO evaluate to move this file to a common place
export const isTuristicProperty = (categoryId: number) =>
    categoryId === window.gtxConstants.CATEGORIA_VACANZE || categoryId === window.gtxConstants.CATEGORIA_VACANZE_V1;

export const isPropertyDisabled = (statusId: number) => statusId !== window.gtxConstants.PROPERTY_ACTIVE_PORTAL_STATUS;

export const isNotResidentialProperty = (categoryId: number) =>
    categoryId !== window.gtxConstants.CATEGORIA_RESIDENZIALE;

// TODO: should be update assets/js/commons/constants/propertyVisibilities.ts with this one
export function getVisibilities(
    tenant: 'it' | 'es' | 'fr' | 'hr' | 'rs',
    env: { secretPropertyEnabled: boolean }
): Array<{
    icon?: string;
    label: string;
    value: AnyString<
        | typeof window.gtxConstants.STAR_VISIBILITY_KEY
        | typeof window.gtxConstants.SHOWCASE_VISIBILITY_KEY
        | typeof window.gtxConstants.SECRET_PROPERTY_VISIBILITY_KEY
        | typeof window.gtxConstants.PREMIUM_VISIBILITY_KEY
    >;
    extraType: boolean;
    shortLabel?: string;
}> {
    return {
        it: [
            {
                icon: 'showcase',
                label: window.gtxConstants.SHOWCASE_VISIBILITY_NAME,
                value: window.gtxConstants.SHOWCASE_VISIBILITY_KEY,
                shortLabel: 'V',
                extraType: true,
            },
            {
                icon: 'star',
                label: window.gtxConstants.STAR_VISIBILITY_NAME,
                value: window.gtxConstants.STAR_VISIBILITY_KEY,
                shortLabel: 'S',
                extraType: true,
            },
            {
                icon: 'top',
                label: window.gtxConstants.TOP_VISIBILITY_NAME,
                value: window.gtxConstants.TOP_VISIBILITY_KEY,
                extraType: true,
                shortLabel: 'T',
            },
            {
                icon: 'premium',
                label: window.gtxConstants.PREMIUM_VISIBILITY_NAME,
                value: window.gtxConstants.PREMIUM_VISIBILITY_KEY,
                extraType: false,
            },
            {
                // AGENCY_PAGE_VISIBILITY_KEY
                label: trans('label.agency_page_publish'),
                value: window.gtxConstants.AGENCY_PAGE_VISIBILITY_KEY,
                extraType: false,
            },
            ...(env.secretPropertyEnabled
                ? [
                      {
                          label: `${trans('label.only')} ${trans('label.secret_plural')}`,
                          value: window.gtxConstants.SECRET_PROPERTY_VISIBILITY_KEY,
                          extraType: false,
                      },
                  ]
                : []),
        ],
        es: [
            {
                icon: 'showcase',
                label: window.gtxConstants.SHOWCASE_VISIBILITY_NAME,
                shortLabel: 'T',
                value: window.gtxConstants.SHOWCASE_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'star',
                label: window.gtxConstants.STAR_VISIBILITY_NAME,
                shortLabel: 'S',
                value: window.gtxConstants.STAR_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'top',
                label: window.gtxConstants.TOP_VISIBILITY_NAME,
                shortLabel: 'D',
                value: window.gtxConstants.TOP_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'premium',
                label: window.gtxConstants.PREMIUM_VISIBILITY_NAME,
                value: window.gtxConstants.PREMIUM_VISIBILITY_KEY,
                extraType: false,
            },
        ],
        fr: [
            {
                icon: 'showcase',
                label: window.gtxConstants.SHOWCASE_VISIBILITY_NAME,
                shortLabel: 'T',
                value: window.gtxConstants.SHOWCASE_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'star',
                label: window.gtxConstants.STAR_VISIBILITY_NAME,
                shortLabel: 'S',
                value: window.gtxConstants.STAR_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'top',
                label: window.gtxConstants.TOP_VISIBILITY_NAME,
                shortLabel: 'D',
                value: window.gtxConstants.TOP_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'premium',
                label: window.gtxConstants.PREMIUM_VISIBILITY_NAME,
                value: window.gtxConstants.PREMIUM_VISIBILITY_KEY,
                extraType: false,
            },
        ],
        hr: [
            {
                icon: 'showcase',
                label: window.gtxConstants.SHOWCASE_VISIBILITY_NAME,
                shortLabel: 'P',
                value: window.gtxConstants.SHOWCASE_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'star',
                label: window.gtxConstants.STAR_VISIBILITY_NAME,
                shortLabel: 'A',
                value: window.gtxConstants.STAR_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'top',
                label: window.gtxConstants.TOP_VISIBILITY_NAME,
                shortLabel: 'S',
                value: window.gtxConstants.TOP_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'premium',
                label: window.gtxConstants.PREMIUM_VISIBILITY_NAME,
                value: window.gtxConstants.PREMIUM_VISIBILITY_KEY,
                extraType: false,
            },
            {
                label: trans('label.agency_page_publish'),
                value: window.gtxConstants.AGENCY_PAGE_VISIBILITY_KEY,
                extraType: false,
            },
        ],
        rs: [
            {
                icon: 'showcase',
                label: window.gtxConstants.SHOWCASE_VISIBILITY_NAME,
                shortLabel: 'P',
                value: window.gtxConstants.SHOWCASE_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'star',
                label: window.gtxConstants.STAR_VISIBILITY_NAME,
                shortLabel: 'A',
                value: window.gtxConstants.STAR_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'top',
                label: window.gtxConstants.TOP_VISIBILITY_NAME,
                shortLabel: 'S',
                value: window.gtxConstants.TOP_VISIBILITY_KEY,
                extraType: true,
            },
            {
                icon: 'premium',
                label: window.gtxConstants.PREMIUM_VISIBILITY_NAME,
                value: window.gtxConstants.PREMIUM_VISIBILITY_KEY,
                extraType: false,
            },
            {
                label: trans('label.agency_page_publish'),
                value: window.gtxConstants.AGENCY_PAGE_VISIBILITY_KEY,
                extraType: false,
            },
        ],
    }[tenant];
}

// Helper function to transform empty strings to null, useful for forms
const transformEmptyStringToNull = (value: unknown, originalValue: unknown) => {
    if (typeof originalValue === 'string' && originalValue.trim() === '') {
        return null;
    }
    return value;
};

/**
 *  Creates a Yup schema for validating min and max values.
 *  The schema ensures that the min value is less than or equal to the max value.
 *  Both min and max values can be null or undefined, making them optional.
 *  The schema also transforms empty strings to undefined to handle form inputs.
 * @param keys - The keys for the min and max values.
 * @returns
 */
export const createMinMaxSchema = <MinKey extends string, MaxKey extends string>(keys: { min: MinKey; max: MaxKey }) =>
    yup.object().shape({
        [keys.min]: yup
            .number()
            .positive()
            .nullable() // Makes the field optional, accepts null
            .transform(transformEmptyStringToNull) // Converts '' to null
            .typeError('Min must be a number') // Message if it's not a valid number
            // Validation applies only if 'max' has a value.
            // If 'max' is not defined, 'min' can be any number (or null).
            .when(keys.max, (maxFieldValue, schema) => {
                // maxFieldValue is an array containing the value of the 'max' field
                // schema is the current schema for 'min'
                const max = maxFieldValue[0];
                if (max !== undefined && max !== null) {
                    // If max has a value, then min (if present) must not be greater than max
                    return schema.max(max, `${trans('error.value_max')} ${max}`);
                }
                // Otherwise (if max is not defined), do not add further restrictions to min
                return schema;
            }),
        [keys.max]: yup
            .number()
            .positive()
            .nullable() // Makes the field optional, accepts null
            .transform(transformEmptyStringToNull) // Converts '' to null
            .typeError('Max must be a number'), // Message if it's not a valid number
    });

/**
 * Returns the category and contract based on the typology.
 * If the typology is 'new_construction', it returns the category for new constructions.
 * If the typology is 'auction', it returns the category for auctions.
 * Otherwise, it returns the original category and contract.
 */
export const getCategoriesByTypology = (
    filters: { category: string; contract: string; portalCategories: Array<string> },
    typology: string
) => {
    let category = filters.category;
    let contract = filters.contract;

    switch (typology) {
        case 'property':
            return {
                category: filters.category ? filters.category : filters.portalCategories.join(','),
                contract: filters.contract,
            };
        case 'new_construction':
            return {
                category: [
                    window.gtxConstants.CATEGORIA_NUOVE_COSTRUZIONI.toString(),
                    window.gtxConstants.CATEGORIA_PROGETTI.toString(),
                ].join(','),
                contract: '',
            };
        case 'auction':
            return { category: window.gtxConstants.CATEGORIA_ASTE.toString(), contract: '' };
        default:
            return { category, contract };
    }
};

/**
 * This function returns the default status for sold and rent filter.
 * If the status is 'sold', it returns the default status for sold properties.
 * Otherwise, it returns an empty string.
 * If the value is provided, it returns the value.
 *
 * ! THIS FUNCTION IS A WORKAROUND
 * TODO: Remove this function when the backend will return the correct status for sold and rent filter.
 */
export function getDefaultStatusForSoldAndRentFilter(
    args: { isGetrix: boolean; status: PropertyStatus },
    value: string
) {
    if (args.status !== 'sold') {
        return '';
    }

    if (value) {
        return value;
    }

    return getDefaultStatusForSoldAndRent(args.isGetrix);
}

export function getDefaultStatusForSoldAndRent(isGetrix: boolean) {
    const active = [3];

    if (isGetrix) {
        active.push(23);
    }

    return active.join(',');
}

export function getStatusOptionsForSoldAndRent(isGetrix: boolean) {
    const active = [3];
    const drafts = [12];
    const archived = [13];

    // TODO: They come from Getrix, it is a temporary solution
    if (isGetrix) {
        active.push(23);
        drafts.push(32);
        archived.push(33);
    }

    return [
        { value: active.join(','), label: trans('label.active_plural') },
        { value: drafts.join(','), label: trans('label.drafts') },
        { value: archived.join(','), label: trans('label.archived_plural') },
    ];
}
