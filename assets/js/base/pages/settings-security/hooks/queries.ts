import { useQuery } from '@tanstack/react-query';
import { queries } from '../utils';
import { getAuthenticatedDevices } from '../web-api/api';
import { AuthenticatedDeviceItem } from '../types';
import { ucFirst } from 'lib/strings-formatter';

export function useAllAuthenticatedDevicesQuery() {
    return useQuery({
        queryKey: queries.allAuthenticatedDevices,
        queryFn: getAuthenticatedDevices,
        select(data) {
            return data.map<AuthenticatedDeviceItem>((device) => ({
                id: device.id,
                browser: {
                    os: device.browser.os || '---',
                    deviceType: ucFirst(device.browser.deviceType || '---'),
                    name: device.browser.name || '---',
                },
                agent: !device.agent ? '---' : `${device.agent?.nome || ''} ${device.agent.cognome || ''}`,
                lastAccess: device.lastAccess,
                ip: device.user_ip,
                isCurrent: device.isMine,
                isRevokeable: device.canRevoke,
            }));
        },
    });
}
