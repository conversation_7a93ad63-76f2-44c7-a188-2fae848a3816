import { handleActions } from 'redux-actions';
import {
    addMessage,
    postThreadMessageError,
    postThreadMessageSuccess,
    setMessagesRead,
    preferredSuccess,
    updateThread,
} from '../../actions/thread';
import { getTodayDatetime } from '../../utils';
import { Message, MessageStatus, Thread, ThreadState, ThreadReadingStatus, IPostMessageResponse, ThreadMessageSendPayload } from '../../types';
import { restoreSuccess, archiveSuccess } from '../../actions/thread';

const DEFAULT_STATE = {};

export const thread = handleActions(
    {
        [addMessage](state: Thread, action) {
            const {
                messageId,
                authorUuid,
                textPlain,
                attachments,
                data,
            } = action.payload as ThreadMessageSendPayload;

            const draft = { ...state };
            const attachmentsList = [];

            if (attachments) {
                for (let i = 0; i < attachments.length; i++) {
                    attachmentsList.push({
                        id: `${attachments[i].name}${i}`,
                        mimeType: attachments[i].type,
                        fileName: attachments[i].name,
                        url: URL.createObjectURL(attachments[i]),
                    });
                }
            }

            draft.messages = draft.messages.concat({
                id: messageId,
                authorUuid,
                textPlain,
                attachments: attachmentsList,
                data,
                status: MessageStatus.PENDING,
                createdAt: getTodayDatetime(),
            } as Message);

            return draft;
        },
        [postThreadMessageSuccess](state: Thread, action: { type: string, payload: { msg: ThreadMessageSendPayload, resp: IPostMessageResponse } }) {
            const { messageId } = action.payload.msg;
            const draft = { ...state };

            const message = draft.messages.find(
                (m: Message) => m.id === messageId
            );

            if(message){
                message.status = MessageStatus.SENT;
                draft.status = action.payload.resp.data.threadStatus
    
                draft.readStatus = ThreadReadingStatus.REPLIED
            }

            return draft;
        },
        [postThreadMessageError](state, action) {
            const { messageId } = action.payload;
            const draft = { ...state };

            const message = draft.messages.find(
                (m: Message) => m.id === messageId
            );

            if(message){
                message.status = MessageStatus.FAILED;
            }

            return draft;
        },
        [updateThread](state: Thread, action: { type: string, payload: ThreadState }) {
            const { thread } = action.payload;
            const draft = {
                ...state,
                ...thread
            };

            if (
                draft.messages.some(
                    (m: Message) => m.status === MessageStatus.PENDING
                )
            ) {
                return state;
            }

            draft.messages = thread.messages.map(
                (m: Message, index: number) => {
                    if (m.authorUuid && thread.authorUuid === m.authorUuid) {
                        return m;
                    }

                    return {
                        ...m,
                        id: m.id,
                        status: m.status,
                        attachments: draft.messages[index]
                            ? draft.messages[index]['attachments']
                            : null,
                    };
                }
            );

            return draft;
        },
        [setMessagesRead](state: Thread) {
            const draft = { ...state };

            draft.readStatus = ThreadReadingStatus.READ;
            draft.unread = 0;

            return draft;
        },
        [preferredSuccess](state: Thread, action) {
            const { isPreferred } = action.payload;
            const draft = { ...state };
            draft.preferred = isPreferred;

            return draft;
        },
        [restoreSuccess]: (state: Thread) => {
            const draft = { ...state }

            draft.status = 'active';

            return draft
        },
        [archiveSuccess]: (state: Thread) => {
            const draft = { ...state }

            draft.status = 'archived';

            return draft
        },
    },
    DEFAULT_STATE
);
