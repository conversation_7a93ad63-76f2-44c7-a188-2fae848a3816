import { describe, expect, it } from 'vitest';
import { getMessagesWithPhoneReply } from './getMessagesWithPhoneReply';

const messagesStub = [
    {
        id: '99841055',
        authorUuid: '9aaed578-0029-539a-b1a5-21c69c185630',
        textPlain: 'test',
        textHtml: 'test',
        createdAt: '2023-04-13 15:51:07',
        status: 'read',
        attachments: null,
        data: null,
    },

    {
        id: '100503185',
        authorUuid: '9aaed578-0029-539a-b1a5-21c69c185630',
        textPlain: 'Lorem ipsum. Doloret.',
        textHtml: 'Lorem ipsum. <b>Doloret.</b>',
        createdAt: '2023-04-19 09:58:34',
        status: 'read',
        attachments: null,
        data: null,
    },
    // This should be the first, ordered by createdAt
    {
        id: '97327737',
        authorUuid: '9aaed578-0029-539a-b1a5-21c69c185630',
        textPlain: 'Mi interessa questo immobile, vorrei avere maggiori informazioni',
        textHtml: null,
        createdAt: '2023-03-22 16:02:14',
        status: 'read',
        attachments: null,
        data: {
            visitRequest: null,
            visitPlan: null,
            property: {
                id: '2909748',
                reference: 'NON MODIFICARE',
                url: null,
            },
        },
    },
    {
        id: '100522303',
        authorUuid: '5fc5117c-ae36-589b-aabe-d58ef63f50f2',
        textPlain: 'test',
        textHtml: null,
        createdAt: '2023-04-19 11:58:01',
        status: 'read',
        attachments: null,
        data: null,
    },
    {
        id: '105982552',
        authorUuid: '9aaed578-0029-539a-b1a5-21c69c185630',
        textPlain: 'test',
        textHtml: 'test',
        createdAt: '2023-06-05 16:18:41',
        status: 'read',
        attachments: null,
        data: null,
    },
    {
        id: '106334866',
        authorUuid: '9aaed578-0029-539a-b1a5-21c69c185630',
        textPlain: 'test',
        textHtml: 'test',
        createdAt: '2023-06-08 11:09:08',
        status: 'read',
        attachments: null,
        data: null,
    },
    {
        id: '109251499',
        authorUuid: '9aaed578-0029-539a-b1a5-21c69c185630',
        textPlain: 'test 2',
        textHtml: 'test 2',
        createdAt: '2023-07-04 16:33:44',
        status: 'read',
        attachments: null,
        data: null,
    },
    {
        id: '112500323',
        authorUuid: '5fc5117c-ae36-589b-aabe-d58ef63f50f2',
        textPlain: null,
        textHtml: null,
        createdAt: '2024-03-13 15:17:14',
        status: 'unread',
        attachments: null,
        data: {
            visitRequest: null,
            visitPlan: {
                visitType: 'in-person',
                visitDateTime: '2024-03-13T15:16:50+01:00',
                agentLink: '',
                guestLink: '',
                note: '',
            },
            property: null,
        },
    },
    {
        id: '112500327',
        authorUuid: '5fc5117c-ae36-589b-aabe-d58ef63f50f2',
        textPlain: 'test',
        textHtml: null,
        createdAt: '2024-03-14 16:52:53',
        status: 'unread',
        attachments: null,
        data: null,
    },
    {
        id: '112500335',
        authorUuid: '5fc5117c-ae36-589b-aabe-d58ef63f50f2',
        textPlain: 'axdassd',
        textHtml: null,
        createdAt: '2024-03-19 15:36:45',
        status: 'unread',
        attachments: null,
        data: null,
    },
    {
        id: '112500337',
        authorUuid: '5fc5117c-ae36-589b-aabe-d58ef63f50f2',
        textPlain: null,
        textHtml: null,
        createdAt: '2024-03-19 16:04:03',
        status: 'unread',
        attachments: [
            {
                id: '3555311',
                fileName: '1.png',
                mimeType: 'image/png',
                // size: '194932',
                url: 'https://gestionale.it.localhost/messaggi/threads/70367455/messages/112500337/attachments/3555311',
            },
        ],
        data: null,
    },
    {
        id: '112500339',
        authorUuid: '5fc5117c-ae36-589b-aabe-d58ef63f50f2',
        textPlain: 'aaaaa',
        textHtml: null,
        createdAt: '2024-03-20 14:37:53',
        status: 'unread',
        attachments: null,
        data: null,
    },
    {
        id: '112500341',
        authorUuid: '5fc5117c-ae36-589b-aabe-d58ef63f50f2',
        textPlain: null,
        textHtml: null,
        createdAt: '2024-03-20 14:38:20',
        status: 'unread',
        attachments: null,
        data: {
            visitRequest: null,
            visitPlan: {
                visitType: 'in-person',
                visitDateTime: '2024-03-20T13:37:35+01:00',
                agentLink: '',
                guestLink: '',
                note: '',
            },
            property: null,
        },
    },
    {
        id: '112500343',
        authorUuid: '5fc5117c-ae36-589b-aabe-d58ef63f50f2',
        textPlain: null,
        textHtml: null,
        createdAt: '2024-03-20 14:38:43',
        status: 'unread',
        attachments: null,
        data: {
            visitRequest: null,
            visitPlan: {
                visitType: 'in-person',
                visitDateTime: '2024-03-20T16:38:22+01:00',
                agentLink: '',
                guestLink: '',
                note: '',
            },
            property: null,
        },
    },
];

const state = {
    content: {
        propertyImagePlaceholder: 'img.png',
        propertyImagePlaceholderNoBorder: 'img.png',
        agencyPageImagePlaceholder: 'img.png',
        agencyPageImageRetinaPlaceholder: '/img.png',
        hasScheduledVisitImmovisita: true,
    },
    thread: {
        messages: messagesStub,
        id: '70367455',
        updateDate: '2024-03-20 14:38:43',
        authorUuid: '9aaed578-0029-539a-b1a5-21c69c185630',
        mode: 'active',
        status: 'active',
        readStatus: 'replied',
        unread: 0,
        preferred: false,
        total: 14,
        participants: [
            {
                uuid: '9aaed578-0029-539a-b1a5-21c69c185630',
                fullName: 'Oliver Tran',
                phone: null,
                email: '<EMAIL>',
                type: 'private',
                lastRead: '2023-07-04 16:33:44',
            },
            {
                uuid: '5fc5117c-ae36-589b-aabe-d58ef63f50f2',
                fullName: 'Casamia',
                phone: null,
                email: null,
                type: 'agency',
                lastRead: '2024-03-20 14:38:43',
            },
        ],
        property: {
            id: 81869282,
            reference: 'NON MODIFICARE',
            typology: {
                id: 259,
                name: 'Coworking',
                parent: {
                    id: 257,
                    name: 'Coworking',
                    parent: {
                        id: 255,
                        name: 'Uffici / Coworking',
                        parent: null,
                    },
                },
            },
            geographyInformation: {
                city: {
                    id: 10803,
                    name: 'Romana',
                    province: {
                        id: 'SS',
                        name: 'Sassari',
                    },
                    cityMacroZoneType: null,
                    chiefTown: null,
                    istatCode: null,
                    coordinates: null,
                },
                macroZone: null,
                address: {
                    street: 'Via Roma',
                    number: null,
                },
                coordinates: {},
                showAddress: true,
                printableCity: 'Romana (SS)',
                printableAddress: 'Via Roma',
                printableZone: null,
                macrozone: null,
            },
            agent: {
                id: 235374,
                firstName: 'Guido',
                lastName: 'Napoli',
                email: null,
                profileImageId: 1117142660,
                profileImageUrl: 'https://media-gestionale-it./agenti/235374/1117142660/80.jpg',
            },
            prices: null,
            status: {
                id: 4,
                name: 'Archiviato',
            },
            contract: {
                id: 2,
                name: 'Affitto',
            },
            surface: '44 m²',
            rooms: null,
            imageId: 1085080371,
            portalLink: 'https://immobiliare-it-reference.kube-dev.it3.ns.farm/annunci/81869282/2909748',
            previewLink: 'https://gestionale.it.localhost/dettaglio.php?id=81869282',
            mainThumbUrl: 'http://media.getrix.daniele.irsuti.dev.loc/image/1085080371/vetrina.jpg',
        },
        project: null,
        contactedPropertyId: 2909748,
        answeredByPhone: null,
    },
};

describe('getMessagesWithPhoneReply', () => {
    it('should return messages sorted by createdAt in ascending order', () => {
        const result = getMessagesWithPhoneReply(state as any);

        expect(result).toHaveLength(14);
        // messagesStub[2] should be the first, ordered by createdAt
        expect(result[0]).toEqual(expect.objectContaining(messagesStub[2]));
    });
});
