import { Header } from 'gtx-react/components';
import { Header as TopHeader } from 'gtx-react/containers/Header';
import { Button, ButtonProps } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { useDispatch } from 'react-redux';
import { openAllMessagesReadModal } from '../../actions/list';
import { useTrackMixpanelEvents } from '../../hooks/useTrackMixpanelEvents';

const ReadAllButton = ({ size }: Partial<ButtonProps>) => {
    const dispatch = useDispatch();
    const { trackReadAll } = useTrackMixpanelEvents();
    const onClick = () => {
        trackReadAll();
        dispatch(openAllMessagesReadModal(true));
    };

    return (
        <Button onClick={onClick} size={size} iconOnly>
            <Icon name="chat-check" />
        </Button>
    );
};

export const ThreadsListHeader = () => (
    <Header
        title={
            document?.querySelector<HTMLDivElement>('#site-section-title')
                ?.innerText
        }
        actions={<ReadAllButton />}
    />
);

export const DefaultTopHeader = () => (
    <TopHeader
        isSectionLabelVisible={false}
        actions={<ReadAllButton size="small" />}
    />
);

export const DefaultTopHeaderWithoutActions = () => {
    return <TopHeader isSectionLabelVisible={false} />;
};
