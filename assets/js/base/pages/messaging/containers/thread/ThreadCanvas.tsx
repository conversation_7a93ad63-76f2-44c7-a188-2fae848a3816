import { trans } from '@pepita-i18n/babelfish';
import { useQueryClient } from '@tanstack/react-query';
import { Fragment, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';

import { Image } from 'gtx-react/components';
import { Skeleton } from 'gtx-react/components/Skeleton';
import {
    contentSelector,
    getContactedPropertyId,
    getMessagesWithPhoneReply,
    getProject,
    getProjectPrices,
    getProperty,
    getPropertyImagePlaceholder,
    getPropertyPrice,
    getRealEstateAddress,
    getRealEstateCity,
    getRealEstateId,
    getRealEstateMainThumbUrl,
    getRealEstatePortalLink,
    getRealEstateReference,
    getThreadAuthorUuid,
    getThreadMode,
    getThreadReadingStatus,
    getThreadUnreadMessages,
    hasRealEstate,
    threadSelector,
} from '../../selectors/thread';
import {
    Message,
    MessageStatus,
    MessageWithPosition,
    ThreadMode,
    ThreadReadingStatus,
} from '../../types';
import {
    formatRealEstateLocation,
    formatThreadDetailProjectPrice,
    getFormattedDate,
    getUnreadMessagesLabel,
} from '../../utils';
import { threadHasLastMessageFromCustomer } from '../../utils/thread/threadHasLastMessageFromCustomer';
import { ThreadHeader } from './ThreadHeader';
import { ThreadInput } from './ThreadInput';
import { ThreadMessage } from './ThreadMessage';
import { ThreadPhoneReplyAction } from './ThreadPhoneReplyAction';
import { ThreadPhotoViewer } from './ThreadPhotoViewer';

/** Scrolling to top, this is the threshold used to let the 'nextPage' request start: when scrollTop is less than this value, the request will start. */
const SCROLL_FETCH_THRESHOLD = 200;

function groupMessageByDate(messages: Message[]) {
    if (!messages) {
        return;
    }

    return messages.reduce((result, item) => {
        const date = new Date(item.createdAt).toISOString().split('T')[0]!;

        result[date] = result[date] || [];
        result[date].push(item);
        return result;
    }, Object.create(null));
}

function setMessageGroupPosition(messages: Message[]): MessageWithPosition[] {
    const result = [...messages];
    let author;

    for (let i = 0; i < messages.length; i++) {
        let item: MessageWithPosition = { ...result[i]! };
        let prevAuthor;
        author = item.authorUuid;

        if (i !== 0) {
            prevAuthor = result[i - 1]?.authorUuid;
        }

        if (i === 0) {
            item.position = 'first';
            result[i] = item;
            continue;
        }

        if (i === messages.length - 1) {
            item.position = 'last';
            result[i] = item;
            continue;
        }

        if (author !== prevAuthor) {
            let prevItem: MessageWithPosition = { ...messages[i - 1]! };
            item.position = 'first';
            result[i] = item;

            prevItem.position = 'last';
            result[i - 1] = prevItem;

            continue;
        }

        item.position = 'middle';
        result[i] = item;
    }

    return result;
}

export type ThreadPhotoViewerState = {
    open: boolean;
    imageName?: string | null;
    imageUrl?: string | null;
};

type ThreadCanvasProps = {
    fetchNextPage: () => Promise<unknown>;
    isFetching: boolean;
    isFetchingNextPage: boolean;
    hasNextPage: boolean;
    isSendingNewMsg: boolean;
    setIsSendingNewMsg: React.Dispatch<React.SetStateAction<boolean>>;
};

export const ThreadCanvas: React.FC<ThreadCanvasProps> = ({
    fetchNextPage,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    isSendingNewMsg,
    setIsSendingNewMsg,
}) => {
    const thread = useSelector(threadSelector);
    const threadReadingStatus = getThreadReadingStatus(thread);
    const threadUnreadMessages = getThreadUnreadMessages(thread);
    const contactedPropertyId = getContactedPropertyId(thread);
    const realEstate = getProperty(thread)
        ? getProperty(thread)
        : getProject(thread)
        ? getProject(thread)
        : null;
    const realEstateReference = getRealEstateReference(realEstate);
    const realEstateLocation = formatRealEstateLocation(
        getRealEstateAddress(realEstate),
        getRealEstateCity(realEstate)
    );
    const authorUuid = getThreadAuthorUuid(thread);
    const messages = useSelector(getMessagesWithPhoneReply);
    const threadMode = getThreadMode(thread);
    const content = useSelector(contentSelector);

    const [photoViewer, setPhotoViewer] = useState<ThreadPhotoViewerState>({
        open: false,
        imageName: null,
        imageUrl: null,
    });
    const [savedHeight, setSavedHeight] = useState<number | null>(null);
    const [savedCount, setSavedCount] = useState<number>(0);

    const queryClient = useQueryClient();

    const unreadTagRef = useRef<HTMLDivElement>(null);
    const threadMessagesRef = useRef<HTMLDivElement>(null);

    const threadRef = useRef<HTMLDivElement>(null);

    const messagesGroupedByDate = groupMessageByDate(messages);

    const isFirstUnreadMessage = (message: Message): boolean => {
        const filteredMessages = messages.filter(
            (itm) => !itm.data?.phoneReply
        );

        return (
            filteredMessages.findIndex(
                (m) => m.id === message.id && message.authorUuid === authorUuid
            ) ===
            filteredMessages.length - threadUnreadMessages
        );
    };

    const scrollDownCb = () => {
        if (
            typeof threadMessagesRef.current?.lastElementChild
                ?.scrollIntoView === 'function'
        ) {
            threadMessagesRef.current.lastElementChild.scrollIntoView({
                behavior: 'smooth',
            });
        }
    };

    useLayoutEffect(() => {
        const handleScroll = async (e) => {
            if (
                !isSendingNewMsg &&
                e.currentTarget.scrollTop < SCROLL_FETCH_THRESHOLD &&
                !isFetchingNextPage &&
                typeof fetchNextPage === 'function'
            ) {
                if (isFetching) {
                    // here when  user tries to see previous messages and a polling query is running
                    queryClient.cancelQueries({ queryKey: ['thread'] });
                }
                const previousHeight = e.currentTarget.scrollHeight;
                // saving height and total messages before fetching
                setSavedHeight(previousHeight);
                setSavedCount(thread.messages.length);
                await fetchNextPage();
            }
        };

        const refCopy = threadRef.current;
        threadRef.current?.addEventListener('scroll', handleScroll);

        return () => refCopy?.removeEventListener('scroll', handleScroll);
    }, [
        fetchNextPage,
        isFetching,
        isFetchingNextPage,
        isSendingNewMsg,
        queryClient,
        thread.messages.length,
    ]);

    useLayoutEffect(() => {
        if (
            !isFetchingNextPage &&
            savedHeight &&
            savedCount < thread.messages.length
        ) {
            // getting the height right after old messages are prepended
            const currentHeight = threadRef.current?.scrollHeight;
            if (currentHeight) {
                const scrollGap = currentHeight - savedHeight;
                threadRef.current.scrollBy({
                    top: scrollGap,
                    behavior: 'instant',
                });
                setSavedHeight(null);
            }
        }
    }, [isFetchingNextPage, savedHeight, savedCount, thread.messages.length]);

    // questo parte quando arrivano nuovi messaggi
    useEffect(() => {
        if (
            !unreadTagRef ||
            !unreadTagRef.current ||
            threadReadingStatus !== ThreadReadingStatus.UNREAD
        ) {
            return;
        }
        // scroll to the unread messages tag
        unreadTagRef.current.scrollIntoView({ behavior: 'smooth' });
    }, [unreadTagRef, threadReadingStatus]);

    useEffect(() => {
        // a new message was posted
        if (unreadTagRef && unreadTagRef.current) {
            return;
        }

        // scroll to the bottom if there are pending messages
        if (
            messages.some((m: Message) => m.status === MessageStatus.PENDING) &&
            typeof threadMessagesRef?.current?.lastElementChild
                ?.scrollIntoView === 'function'
        ) {
            threadMessagesRef.current.lastElementChild.scrollIntoView({
                behavior: 'smooth',
            });
        }
    }, [messages]);

    useEffect(() => {
        // page load with no new messages (no "new message" tag)
        if (unreadTagRef && unreadTagRef.current) {
            return;
        }

        if (
            typeof threadMessagesRef?.current?.lastElementChild
                ?.scrollIntoView === 'function'
        ) {
            // scroll to the bottom
            threadMessagesRef.current.lastElementChild.scrollIntoView();
        }
    }, []);

    return (
        <div className="gx-page-content">
            <ThreadHeader />
            <div ref={threadRef} className="messaging-threadCanvas">
                <div className="messaging-mobileRef">
                    <div className="messaging-reInfo">
                        <Image
                            className="annuncio-image"
                            src={getRealEstateMainThumbUrl(realEstate)}
                            loading="lazy"
                            fallbackSrc={getPropertyImagePlaceholder(content)}
                        />
                        <div className="messaging-reInfo__content">
                            {hasRealEstate(thread) && realEstate ? (
                                <>
                                    <div className="messaging-reInfo__rif">
                                        <a
                                            href={getRealEstatePortalLink(
                                                realEstate
                                            )}
                                            target="_blank"
                                        >
                                            {realEstateReference
                                                ? `${trans(
                                                      'label.reference_short'
                                                  ).toLocaleUpperCase()} ${realEstateReference}`
                                                : contactedPropertyId
                                                ? contactedPropertyId
                                                : getRealEstateId(realEstate)}
                                        </a>
                                    </div>
                                    <div className="messaging-reInfo__address">
                                        {realEstateLocation}
                                    </div>
                                    <div className="messaging-reInfo__price">
                                        {getProperty(thread)
                                            ? getPropertyPrice(thread)
                                            : formatThreadDetailProjectPrice(
                                                  getProjectPrices(thread)
                                              )}
                                    </div>
                                </>
                            ) : (
                                <>{trans('label.messaging.from_agency_page')}</>
                            )}
                        </div>
                    </div>
                </div>

                <div
                    ref={threadMessagesRef}
                    className="messaging-threadCanvas__messages"
                    role="list"
                >
                    {hasNextPage && (
                        <div
                            style={{ display: 'flex', flexDirection: 'column' }}
                        >
                            <Skeleton
                                height={94}
                                width={240}
                                className="messaging-message"
                            />
                            <div style={{ alignSelf: 'flex-end' }}>
                                <Skeleton
                                    height={94}
                                    width={200}
                                    className="messaging-message"
                                />
                            </div>
                        </div>
                    )}
                    {messagesGroupedByDate
                        ? Object.keys(messagesGroupedByDate).map(
                              (date, groupIndex) => (
                                  <Fragment key={`${date}${groupIndex}`}>
                                      <div className="messaging-threadDate">
                                          <span>
                                              {getFormattedDate(date, {
                                                  day: 'numeric',
                                                  month: 'short',
                                                  year: 'numeric',
                                              })}
                                          </span>
                                      </div>
                                      {setMessageGroupPosition(
                                          messagesGroupedByDate[date]
                                      )?.map((m, msgIndex) => (
                                          <ThreadMessage
                                              message={m}
                                              key={`${m.id}${msgIndex}`}
                                              threadReadingStatus={
                                                  threadReadingStatus
                                              }
                                              isFirstUnread={isFirstUnreadMessage(
                                                  m
                                              )}
                                              unreadTagRef={unreadTagRef}
                                              unreadMessagesLabel={getUnreadMessagesLabel(
                                                  threadUnreadMessages
                                              )}
                                              setPhotoViewer={setPhotoViewer}
                                              authorUuid={authorUuid}
                                          />
                                      ))}
                                  </Fragment>
                              )
                          )
                        : null}
                    {threadHasLastMessageFromCustomer({
                        ...thread,
                        messages,
                    }) && (
                        <ThreadPhoneReplyAction
                            onPhoneReplyPosted={scrollDownCb}
                        />
                    )}
                </div>
            </div>
            {threadMode === ThreadMode.ACTIVE ? (
                <ThreadInput
                    setIsSending={setIsSendingNewMsg}
                    scrollDown={scrollDownCb}
                />
            ) : (
                <div className="messaging-noUserMail">
                    {trans('label.messaging.contact_has_no_mail')}
                </div>
            )}
            {photoViewer.open && (
                <ThreadPhotoViewer
                    onClose={() =>
                        setPhotoViewer({
                            open: false,
                            imageName: null,
                            imageUrl: null,
                        })
                    }
                    photoName={photoViewer.imageName}
                    photoUrl={photoViewer.imageUrl}
                />
            )}
        </div>
    );
};
