import { Icon } from '@gx-design/icon';

type ThreadPhotoViewerProps = {
    photoName?: string | null;
    photoUrl?: string | null;
    onClose: any;
};

export const ThreadPhotoViewer = ({
    photoName,
    photoUrl,
    onClose,
}: ThreadPhotoViewerProps) => {
    return (
        <div className="messaging-threadPhotoViewer">
            <div className="messaging-threadPhotoViewer__header">
                <span>{photoName}</span>
                <Icon
                    onClick={onClose}
                    className="messaging-threadPhotoViewer__headerClose"
                    name="cross"
                />
            </div>
            <div className="messaging-threadPhotoViewer__content">
                <img src={photoUrl} alt={photoName} />
            </div>
        </div>
    );
};
