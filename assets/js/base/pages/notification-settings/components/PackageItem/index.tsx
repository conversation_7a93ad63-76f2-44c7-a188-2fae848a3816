import { FC, useState } from 'react';
import { imageMap } from '../../constants/imageMap';
import { Toggle } from '@gx-design/toggle';
import { trans } from '@pepita-i18n/babelfish';
import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { EnableZoneModal } from './EnableZoneModal';
import { ZoneCategoryCounter } from './ZoneCategoryCounter';
import { DisableZoneModal } from './DisableZoneModal';
import { useFormikContext } from 'formik';
import { getZoneNameFromPackage } from '../../utils/getZoneNameFromPackage';
import { PackageModal } from './PackageModal';
import { FormNotificationSettings } from '../../types';
import { useSearchesCategoriesLookup } from '../../hooks/useSearchesCategoriesLookup';

type PackageItemProps = {
    packageIndex: number;
};
export const PackageItem: FC<PackageItemProps> = ({ packageIndex }) => {
    const baseFieldName = `items[${packageIndex}]`;

    const [showEnableModal, setShowEnableModal] = useState<boolean>(false);
    const [showDisableModal, setShowDisableModal] = useState<boolean>(false);
    const [showPackageModal, setShowPackageModal] = useState<boolean>(false);

    const { initialValues, setFieldValue } =
        useFormikContext<FormNotificationSettings>();

    const initialPackage = initialValues.items[packageIndex]!;

    const { allRentCategories, allSellCategories } =
        useSearchesCategoriesLookup();

    const packageHasNoCategories =
        initialPackage.rentCategoryIds.length === 0 &&
        initialPackage.sellCategoryIds.length === 0;

    return (
        <>
            <tr
                className="gx-table__row package-itm-row"
                data-testid="package-row"
            >
                <td className="gx-table-new__cell">
                    <div className="gx-row">
                        <div className="package-itm-row__package-icon">
                            <img src={imageMap[initialPackage.tier]} />
                        </div>
                        <div>
                            <strong className="gx-text-ellipsis">
                                {getZoneNameFromPackage(initialPackage)}
                            </strong>

                            <div className="notification-settings-category-counter">
                                {initialPackage.enabled ? (
                                    <ZoneCategoryCounter
                                        onClick={() =>
                                            setShowPackageModal(true)
                                        }
                                        packageItem={initialPackage}
                                    />
                                ) : (
                                    trans('label.deactivate_plural_female')
                                )}
                            </div>
                        </div>
                    </div>
                </td>
                <td className="gx-table-new__cell">
                    <Toggle
                        name={`${baseFieldName}.enabled`}
                        checked={initialPackage.enabled}
                        onChange={(ev) => {
                            ev.preventDefault();
                            if (initialPackage.enabled) {
                                setShowDisableModal(true);
                                setFieldValue(
                                    `${baseFieldName}.enabled`,
                                    false
                                );
                            } else {
                                setShowEnableModal(true);
                                setFieldValue(`${baseFieldName}.enabled`, true);
                                if (packageHasNoCategories) {
                                    setFieldValue(
                                        `${baseFieldName}.rentCategoryIds`,
                                        allRentCategories
                                    );
                                    setFieldValue(
                                        `${baseFieldName}.sellCategoryIds`,
                                        allSellCategories
                                    );
                                }
                            }
                        }}
                    />
                </td>
                <td className="gx-table-new__cell">
                    {initialPackage.enabled && (
                        <Button
                            onClick={() => setShowPackageModal(true)}
                            variant="ghost"
                        >
                            <span>
                                {trans('label.customize').toLocaleUpperCase()}
                            </span>
                            <Icon name="arrow-right" />
                        </Button>
                    )}
                </td>
            </tr>

            {showEnableModal && (
                <EnableZoneModal
                    onClose={() => {
                        setShowEnableModal(false);
                    }}
                    packageIndex={packageIndex}
                />
            )}

            {showDisableModal && (
                <DisableZoneModal
                    onClose={() => {
                        setShowDisableModal(false);
                    }}
                    packageIndex={packageIndex}
                />
            )}

            {showPackageModal && (
                <PackageModal
                    onClose={() => {
                        setShowPackageModal(false);
                    }}
                    packageIndex={packageIndex}
                    baseFieldName={baseFieldName}
                />
            )}
        </>
    );
};
