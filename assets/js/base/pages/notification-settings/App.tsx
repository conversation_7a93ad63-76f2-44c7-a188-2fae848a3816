import { MswDeveloperBoundary } from 'gtx-react/components/MswDeveloperBoundary';
import { notificationSettingsPage } from './pages/NotificationSettingsPage';
import { GtxApp } from 'gtx-react/components/GtxApp/GtxApp';
import { MixpanelProvider } from 'gtx-react/contexts/MixpanelProvider';
import { MIXPANEL_CONFIG } from './constants/mixpanel_config';

export const App = () => {
    return (
        <MswDeveloperBoundary isDisabled>
            <MixpanelProvider mixpanelConfig={MIXPANEL_CONFIG}>
                <GtxApp pages={[notificationSettingsPage]} />
            </MixpanelProvider>
        </MswDeveloperBoundary>
    );
};
