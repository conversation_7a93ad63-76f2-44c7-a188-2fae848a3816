import { infoData } from '../../utils/infoData';
import { ZonesManagerInfoIntro } from './ZonesManagerInfoIntro';
import { ZonesManagerInfoFooter } from './ZonesManagerInfoFooter';
import { ZonesManagerInfoPackages } from './ZonesManagerInfoPackages';

type ZonesManagerInfoType = {
    testId: string;
};

export const ZonesManagerInfo: React.FC<ZonesManagerInfoType> = ({
    testId,
}) => {
    return (
        <div data-testid={testId} className="gx-card gx-card--rounded">
            <div className="gx-card__content gx-card__content--paddingDouble">
                <ZonesManagerInfoIntro data={infoData} />
                <ZonesManagerInfoPackages data={infoData} />
                <ZonesManagerInfoFooter />
            </div>
        </div>
    );
};
