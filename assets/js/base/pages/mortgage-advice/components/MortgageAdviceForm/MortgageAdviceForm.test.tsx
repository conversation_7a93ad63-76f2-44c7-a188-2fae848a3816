import { QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from 'lib/queryClient';
import { render, screen } from '#tests/react/testing-library-enhanced';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { MortgageAdviceForm } from './MortgageAdviceForm';

const minimumFormValues = {
    nome: 'Mario',
    comuneImmobile: 11131, // 'Palermo'
    cognome: 'Rossi',
    email: '<EMAIL>',
    telefono: '**********',
    eta: 33,
    comune: 11131,
    posizioneLavorativa: '',
    indirizzoImmobile: 'Via Roma 19',
    valoreImmobile: 10000,
    importoMutuo: 100,
    consenso: true,
    statoRicerca: '5', //  'Individuato immobile'
};

const fullFormValues = {
    ...minimumFormValues,
    posizioneLavorativa: '1', // 'Dipendente a tempo indeterminato'
};

describe('Mortgage Advice Form', () => {
    const onSubmitMock = vi.fn((data) => data);

    const renderMortgageAdviceForm = () => {
        const queryClient = createQueryClient();
        return render(<MortgageAdviceForm onSubmit={onSubmitMock} />, {
            wrapper: (props) => (
                <QueryClientProvider client={queryClient}>
                    {props.children}
                </QueryClientProvider>
            ),
        });
    };

    afterEach(() => {
        vi.resetAllMocks();
    });

    it('should render the form', () => {
        renderMortgageAdviceForm();

        expect(screen.getByTestId('mortgage-advice-form')).toBeInTheDocument();
    });

    it('should show a disabled button when the consesus has not been accepted', () => {
        renderMortgageAdviceForm();

        const button = screen.getByRole('button', { name: 'btn.label.send' });

        expect(button).toBeDisabled();
    });

    /**
     * Required attributes are validated by the browser, so we have to test the validation
     * filling the form and checking the error messages one by one.
     * We also have to check that the form is not submitted when it's not valid.
     */
    it("should show the error message when the form isn't valid", async () => {
        const { user } = renderMortgageAdviceForm();

        const sendButton = screen.getByRole('button', {
            name: 'btn.label.send',
        });
        const acceptConsesusButton = screen.getByLabelText(
            /financial_advice.form.privacy/
        );

        const nameElement = screen.getByLabelText(/label.name/);
        const surnameElement = screen.getByLabelText(/label.surname/);
        const emailElement = screen.getByLabelText(/label.mail/);
        const phoneElement = screen.getByLabelText(/label.phone/);
        const ageElement = screen.getByLabelText(/label.age/);
        const propertyValueElement =
            screen.getByLabelText(/label.property_value/);
        const mortgageAmountElement = screen.getByLabelText(
            /label.mortgage_amount/
        );
        const propertyAddressElement = screen.getByLabelText(
            /label.property_address_2/
        );
        const searchStatusElement = screen.getByLabelText(
            /label.property_research_status/
        );

        await user.click(acceptConsesusButton);

        expect(acceptConsesusButton).toBeChecked();

        await user.click(sendButton);
        expect(onSubmitMock).not.toHaveBeenCalled();

        expect(nameElement).toBeInvalid();
        expect(onSubmitMock).not.toHaveBeenCalled();

        await user.type(nameElement, minimumFormValues.nome);

        await user.click(sendButton);
        expect(onSubmitMock).not.toHaveBeenCalled();
        expect(surnameElement).toBeInvalid();

        await user.type(surnameElement, minimumFormValues.cognome);

        await user.click(sendButton);
        expect(onSubmitMock).not.toHaveBeenCalled();

        expect(emailElement).toBeInvalid();

        await user.type(emailElement, 'not a valid email');

        await user.click(sendButton);
        expect(onSubmitMock).not.toHaveBeenCalled();

        await user.clear(emailElement);
        await user.type(emailElement, minimumFormValues.email);

        await user.click(sendButton);

        expect(onSubmitMock).not.toHaveBeenCalled();

        expect(phoneElement).toBeInvalid();

        await user.type(phoneElement, minimumFormValues.telefono);

        await user.click(sendButton);
        expect(onSubmitMock).not.toHaveBeenCalled();

        expect(ageElement).toBeInvalid();

        await user.type(ageElement, minimumFormValues.eta.toString());

        await user.click(sendButton);

        expect(propertyAddressElement).toBeInvalid();
        expect(onSubmitMock).not.toHaveBeenCalled();

        await user.type(
            propertyAddressElement,
            minimumFormValues.indirizzoImmobile
        );

        await user.click(sendButton);
        expect(onSubmitMock).not.toHaveBeenCalled();

        expect(propertyValueElement).toBeInvalid();

        await user.type(
            propertyValueElement,
            fullFormValues.valoreImmobile.toString()
        );

        await user.click(sendButton);

        expect(searchStatusElement).toBeInvalid();

        await user.selectOptions(searchStatusElement, 'Individuato immobile');

        await user.click(sendButton);
        expect(onSubmitMock).not.toHaveBeenCalled();

        expect(mortgageAmountElement).toBeInvalid();

        await user.type(
            mortgageAmountElement,
            fullFormValues.importoMutuo.toString()
        );

        await user.click(sendButton);

        expect(screen.queryAllByText(/label.required_value/)).toHaveLength(2);
        expect(onSubmitMock).not.toHaveBeenCalled();
    });

    it.todo('should validate with the form with minimum data', async () => {
        // ! Why this test has been skipped?
        // ! It is not working as expected and it's not clear why. We suppose is a problem with fake timers.
        // vi.useFakeTimers();
        // const { user: baseUser } = renderMortgageAdviceForm();
        // const user = baseUser.setup({
        //     advanceTimers: (ms) => vi.advanceTimersByTime(ms),
        // });
        // const sendButton = screen.getByRole('button', {
        //     name: 'btn.label.send',
        // });
        // const acceptConsesusButton = screen.getByLabelText(
        //     /financial_advice.form.privacy/
        // );
        // const nameElement = screen.getByLabelText(/label.name/);
        // const surnameElement = screen.getByLabelText(/label.surname/);
        // const emailElement = screen.getByLabelText(/label.mail/);
        // const phoneElement = screen.getByLabelText(/label.phone/);
        // const ageElement = screen.getByLabelText(/label.age/);
        // const propertyValueElement =
        //     screen.getByLabelText(/label.property_value/);
        // const searchStatusElement = screen.getByLabelText(
        //     /label.property_research_status/
        // );
        // const mortgageAmountElement = screen.getByLabelText(
        //     /label.mortgage_amount/
        // );
        // const propertyAddressElement = screen.getByLabelText(
        //     /label.property_address_2/
        // );
        // await user.click(acceptConsesusButton);
        // expect(acceptConsesusButton).toBeChecked();
        // await user.type(nameElement, minimumFormValues.nome);
        // await user.type(surnameElement, minimumFormValues.cognome);
        // await user.type(emailElement, minimumFormValues.email);
        // await user.type(phoneElement, minimumFormValues.telefono);
        // await user.type(ageElement, minimumFormValues.eta.toString());
        // user.type(screen.getByTestId('comune-autocomplete'), 'pal');
        // await vi.advanceTimersByTimeAsync(4000);
        // const domicileAutoComplete = await screen.findByTestId(
        //     'comune-autocomplete-item-0'
        // );
        // await user.click(domicileAutoComplete);
        // user.type(screen.getByTestId('comuneImmobile-autocomplete'), 'pal');
        // await vi.advanceTimersByTimeAsync(4000);
        // const propertyAutoComplete = await screen.findByTestId(
        //     'comuneImmobile-autocomplete-item-0'
        // );
        // await user.click(propertyAutoComplete);
        // await user.type(
        //     propertyAddressElement,
        //     minimumFormValues.indirizzoImmobile
        // );
        // await user.type(
        //     propertyValueElement,
        //     minimumFormValues.valoreImmobile.toString()
        // );
        // await user.type(
        //     mortgageAmountElement,
        //     minimumFormValues.importoMutuo.toString()
        // );
        // await user.selectOptions(searchStatusElement, 'Individuato immobile');
        // await user.click(sendButton);
        // expect(onSubmitMock).toHaveBeenCalledWith(
        //     minimumFormValues,
        //     expect.anything()
        // );
    });

    it.todo('should validate with the form with full data', async () => {
        // ! Why this test has been skipped?
        // ! It is not working as expected and it's not clear why. We suppose is a problem with fake timers.
        //     const { user } = renderMortgageAdviceForm();
        //     const sendButton = screen.getByRole('button', {
        //         name: 'btn.label.send',
        //     });
        //     const acceptConsesusButton = screen.getByLabelText(
        //         /financial_advice.form.privacy/
        //     );
        //     const nameElement = screen.getByLabelText(/label.name/);
        //     const surnameElement = screen.getByLabelText(/label.surname/);
        //     const emailElement = screen.getByLabelText(/label.mail/);
        //     const phoneElement = screen.getByLabelText(/label.phone/);
        //     const ageElement = screen.getByLabelText(/label.age/);
        //     const jobPositionElement = screen.getByLabelText(/label.job_position/);
        //     const propertyValueElement =
        //         screen.getByLabelText(/label.property_value/);
        //     const mortgageAmountElement = screen.getByLabelText(
        //         /label.mortgage_amount/
        //     );
        //     const propertyAddressElement = screen.getByLabelText(
        //         /label.property_address_2/
        //     );
        //     const searchStatusElement = screen.getByLabelText(
        //         /label.property_research_status/
        //     );
        //     await user.click(acceptConsesusButton);
        //     expect(acceptConsesusButton).toBeChecked();
        //     await user.type(nameElement, fullFormValues.nome);
        //     await user.type(surnameElement, fullFormValues.cognome);
        //     await user.type(emailElement, fullFormValues.email);
        //     await user.type(phoneElement, fullFormValues.telefono);
        //     await user.type(ageElement, fullFormValues.eta.toString());
        //     await user.type(screen.getByTestId('comune-autocomplete'), 'pal');
        //     const domicileAutoComplete = await screen.findByTestId(
        //         'comune-autocomplete-item-0'
        //     );
        //     await user.click(domicileAutoComplete);
        //     await user.type(
        //         screen.getByTestId('comuneImmobile-autocomplete'),
        //         'pal'
        //     );
        //     const propertyAutoComplete = await screen.findByTestId(
        //         'comuneImmobile-autocomplete-item-0'
        //     );
        //     await user.click(propertyAutoComplete);
        //     await user.type(
        //         propertyAddressElement,
        //         fullFormValues.indirizzoImmobile
        //     );
        //     await user.type(
        //         propertyValueElement,
        //         fullFormValues.valoreImmobile.toString()
        //     );
        //     await user.type(
        //         mortgageAmountElement,
        //         fullFormValues.importoMutuo.toString()
        //     );
        //     await user.selectOptions(
        //         jobPositionElement,
        //         'Dipendente a tempo indeterminato'
        //     );
        //     await user.selectOptions(searchStatusElement, 'Individuato immobile');
        //     await user.click(sendButton);
        //     expect(onSubmitMock).toHaveBeenCalledWith(
        //         fullFormValues,
        //         expect.anything()
        //     );
    });
});
