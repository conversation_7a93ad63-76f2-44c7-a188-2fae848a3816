import { QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from 'lib/queryClient';
import { GtxPageWithoutStore } from '../../../../commons/gtx-react/components/GtxApp/GtxApp';
import LandingPageView from '../views/LandingPageView';
import { endpoints } from '../web-api/endpoints';
import estimatesConfig from '../configs/estimatesConfig';

const queryClient = createQueryClient();

export const EstimatesPage: GtxPageWithoutStore = {
    container: () => (
        <LandingPageView
            showLogoBox={false}
            moduleImg={false}
            shadowImg={false}
            data={estimatesConfig}
        />
    ),
    path: endpoints.ESTIMATES,
    header: false,
    wrapper: ({ children }) => (
        <QueryClientProvider client={queryClient}>
            {children}
        </QueryClientProvider>
    ),
};
