import { FC, useCallback, useState } from 'react';
import { Icon } from '@gx-design/icon';

export const PropertyImage: FC<{ src?: string; alt?: string }> = ({
    src,
    alt = '',
}) => {
    const [hasError, setHasError] = useState(false);

    const handleError = useCallback(() => setHasError(true), []);

    if (hasError || !src) {
        return (
            <div className="crm-property-item__pic no-image">
                <Icon name="image-off" />
            </div>
        );
    }

    return (
        <div className="crm-property-item__pic">
            <img alt={alt} onError={handleError} src={src} />
        </div>
    );
};
