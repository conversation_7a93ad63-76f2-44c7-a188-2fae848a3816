import { Button } from '@gx-design/button';
import { Icon, IconProps } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import { useQueryClient } from '@tanstack/react-query';
import { Formik, useFormikContext } from 'formik';
import { GxFkSelect } from 'gtx-react/components/gx-formik';
import useFormikGeoFields from 'gtx-react/hooks/useFormikGeoFields';
import { FC, PropsWithChildren, useState } from 'react';
import * as yup from 'yup';
import {
    cityByProvinceOptions,
    countyOptions,
    provinceByRegionOptions,
    regionOptions,
    zoneByCityOptions,
} from '../../../utils/queries';
import clsx from 'clsx';
import { Modal } from '@gx-design/modal';

const locationSchema = yup.object({
    country: yup.string().default(''),
    region: yup.string().default(''),
    zone: yup.string().default(''),
    province: yup.string().default(''),
    city: yup.string().default(''),
});

type LocationFormValues = yup.InferType<typeof locationSchema>;

export type DropdownLocationInputProps = {
    name: string;
    submitOnSelect?: boolean;
    selected?: boolean;
    icon?: IconProps['name'];
};

export const DropdownLocationInput: FC<
    PropsWithChildren<DropdownLocationInputProps>
> = ({ name, selected, icon, children }) => {
    const { values, setValues, submitForm } =
        useFormikContext<Record<string, any>>();
    const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);

    const toggleDropdown = () => setIsDropdownOpen((prevState) => !prevState);

    return (
        <>
            <button
                className={clsx('gx-range-inputControl', {
                    'is-open': isDropdownOpen,
                    'is-selected': selected,
                })}
                onClick={toggleDropdown}
                type="button"
            >
                {icon && (
                    <Icon className="gx-range-inputControl__icon" name={icon} />
                )}
                <span>{children}</span>
                <Icon
                    className="gx-range-inputControl__caret"
                    name="arrow-down"
                />
            </button>
            <DropdownLocationForm
                onSubmit={(formValues) => {
                    setValues({ ...values, [name]: formValues });

                    toggleDropdown();
                    submitForm();
                }}
                initialValues={values[name]}
            >
                <Modal
                    title={trans('label.place')}
                    isOpen={isDropdownOpen}
                    onClose={toggleDropdown}
                    footer={<DropdownLocationSubmit />}
                >
                    <DropdownLocationFields />
                </Modal>
            </DropdownLocationForm>
        </>
    );
};

const DropdownLocationFields = () => {
    const queryClient = useQueryClient();
    const getFormikGeoFieldProps = useFormikGeoFields([
        {
            placeholder: trans('label.all'),
            type: 'country',
            customFetch: () => {
                return queryClient.fetchQuery({
                    ...countyOptions(),
                    staleTime: Infinity,
                });
            },
        },
        {
            placeholder: trans('label.all'),
            type: 'region',
            customFetch: (searchParams) => {
                return queryClient.fetchQuery({
                    ...regionOptions(searchParams),
                    staleTime: Infinity,
                });
            },
        },
        {
            placeholder: trans('label.all'),
            type: 'province',
            customFetch: (searchParams) => {
                return queryClient.fetchQuery({
                    ...provinceByRegionOptions(searchParams),
                    staleTime: Infinity,
                });
            },
        },
        {
            placeholder: trans('label.all'),
            type: 'city',
            customFetch: (searchParams) => {
                return queryClient.fetchQuery({
                    ...cityByProvinceOptions(searchParams),
                    staleTime: Infinity,
                });
            },
        },
        {
            placeholder: trans('label.all'),
            type: 'zone',
            customFetch: (searchParams) => {
                return queryClient.fetchQuery({
                    ...zoneByCityOptions(searchParams),
                    staleTime: Infinity,
                });
            },
        },
    ]);

    return (
        <div className="gx-row">
            <div className="gx-col-xs-12 gx-col-md-6">
                <div className="gx-box-row">
                    <GxFkSelect {...getFormikGeoFieldProps('country')} />
                </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
                <div className="gx-box-row">
                    <GxFkSelect {...getFormikGeoFieldProps('region')} />
                </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
                <div className="gx-box-row">
                    <GxFkSelect {...getFormikGeoFieldProps('province')} />
                </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
                <div className="gx-box-row">
                    <GxFkSelect {...getFormikGeoFieldProps('city')} />
                </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
                <div className="gx-box-row">
                    <GxFkSelect {...getFormikGeoFieldProps('zone')} />
                </div>
            </div>
        </div>
    );
};

type DropdownLocationFormProps = PropsWithChildren<{
    onSubmit: (values: LocationFormValues) => void;
    initialValues: LocationFormValues;
}>;

const DropdownLocationForm = (props: DropdownLocationFormProps) => {
    return (
        <Formik<LocationFormValues>
            initialValues={props.initialValues}
            validationSchema={locationSchema}
            onSubmit={(values) => props.onSubmit(values)}
        >
            <>{props.children}</>
        </Formik>
    );
};

const DropdownLocationSubmit = () => {
    const { submitForm } = useFormikContext();

    return (
        <Button variant="accent" onClick={submitForm}>
            {trans('label.apply_filters')}
        </Button>
    );
};
