import { GtxTable } from 'gtx-react/components/GtxTable/GtxTable';
import { EstimateType } from 'lib/REST/types/acquisition-estimates';
import { trans } from '@pepita-i18n/babelfish';
import { TableColumn } from 'gtx-react/components/GxTable/types';
import {
    ActionsItem,
    ContactItem,
    DateItem,
    OutcomeItem,
    PlaceItem,
    PriceItem,
    PropertyItem,
    StatusAndPropertyItem,
} from './TableItems';

export const ESTIMATES_LIST_FIELDS = {
    date: 'date',
    place: 'place',
    property: 'property',
    price: 'price',
    contact: 'contact',
    statusAndProperty: 'statusAndProperty',
    outcome: 'outcome',
    actions: 'actions',
};

export const estimatesListTableConfig = (): TableColumn<EstimateType>[] => [
    {
        key: ESTIMATES_LIST_FIELDS.date,
        header: trans('label.date'),
        renderCell: (data) => <DateItem estimate={data} />,
        main: true,
    },
    {
        key: ESTIMATES_LIST_FIELDS.place,
        header: trans('label.place'),
        renderCell: (data) => <PlaceItem estimate={data} />,
    },
    {
        key: ESTIMATES_LIST_FIELDS.property,
        header: trans('label.messaging.real_estate_info'),
        renderCell: (data) => <PropertyItem estimate={data} />,
    },
    {
        key: ESTIMATES_LIST_FIELDS.price,
        header: trans('label.price'),
        renderCell: (data) => <PriceItem estimate={data} />,
    },
    {
        key: ESTIMATES_LIST_FIELDS.contact,
        header: trans('label.contact'),
        renderCell: (data) => <ContactItem estimate={data} />,
    },
    {
        key: ESTIMATES_LIST_FIELDS.statusAndProperty,
        header: trans('label.property_and_status'),

        renderCell: (data) => <StatusAndPropertyItem estimate={data} />,
    },
    {
        key: ESTIMATES_LIST_FIELDS.outcome,
        header: trans('label.outcome'),
        renderCell: (data) => <OutcomeItem estimate={data} />,
    },
    {
        key: ESTIMATES_LIST_FIELDS.actions,
        header: '',
        renderCell: (data) => <ActionsItem estimate={data} />,
    },
];

export const EstimatesListBigScreen = ({
    estimates,
}: {
    estimates: EstimateType[];
}) => (
    <GtxTable
        data-testid="estimates-list-table"
        columns={estimatesListTableConfig()}
        data={{
            items: estimates || [],
            extraItemFields: ['id'],
        }}
    />
);
