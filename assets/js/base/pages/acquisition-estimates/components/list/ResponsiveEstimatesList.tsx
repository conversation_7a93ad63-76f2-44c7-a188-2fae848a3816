import { EstimateType } from 'lib/REST/types/acquisition-estimates';
import { EstimatesListBigScreen } from './EstimatesListBigScreen';
import { EstimatesListPagination } from './EstimatesListPagination';
import { EstimatesListSmallScreen } from './EstimatesListSmallScreen';
import { useMediaMatch } from '@gx-design/use-media-match';

export const ResponsiveEstimatesList = ({
    estimates,
}: {
    estimates: EstimateType[];
}) => {
    const isLargeDesktop = useMediaMatch('largeDesktop');

    return (
        <>
            {isLargeDesktop ? (
                <EstimatesListBigScreen estimates={estimates} />
            ) : (
                <EstimatesListSmallScreen estimates={estimates} />
            )}
            <EstimatesListPagination />
        </>
    );
};
