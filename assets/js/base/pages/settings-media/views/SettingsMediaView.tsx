import { groupExists } from '@getrix/common/js/featureToggleConfigs';
import { Alert } from '@gx-design/alert';
import { Modal } from '@gx-design/modal';
import { trans } from '@pepita-i18n/babelfish';
import { FC, useState } from 'react';

import { Loader } from 'gtx-react/components';
import { ucFirst } from 'lib/strings-formatter';
import { ImageUploaderWithPreview } from '../components/ImageUploaderWithPreview';
import { IMAGE_EXAMPLE_SRC, IMAGE_EXAMPLE_SRC_SET } from '../constants';
import { extractAgencyData, onLogoChange } from '../utils';
import {
    removeImage,
    removeLogo,
    updateImage,
    updateLogo,
} from '../web-api/api';

export const SettingsMediaView: FC = () => {
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isExampleModalOpen, setIsExampleModalOpen] =
        useState<boolean>(false);
    const agency = extractAgencyData();

    const toggleExampleModal = (value: boolean) => () =>
        setIsExampleModalOpen(value);

    return (
        <div className="gx-container gx-container--maxWidth">
            {!agency || !Object.keys(agency).length ? (
                <div className="contract-box-wrap">
                    <Alert style="info" withMargin>
                        <span>{trans('settings.general.alert.no_data')}</span>
                    </Alert>
                </div>
            ) : (
                <>
                    <Loader loading={isLoading} />
                    <div className="gx-section">
                        <ImageUploaderWithPreview
                            title={ucFirst(trans('label.logo'))}
                            description={
                                <div>
                                    {groupExists('multisend') && (
                                        <p>
                                            {trans('settings.media.logo.text1')}
                                        </p>
                                    )}
                                    <p>{trans('settings.media.logo.text3')}</p>
                                </div>
                            }
                            caption={trans('settings.media.logo.text2')}
                            inputKeyName="logo"
                            outputKeyName="logoUrl"
                            resultKeyName="logoUrl"
                            updateApi={updateLogo}
                            removeApi={removeLogo}
                            setIsLoading={setIsLoading}
                            updateSuccessMessage={trans(
                                'media.logo.upload_success'
                            )}
                            updateErrorMessage={trans(
                                'media.logo.upload_error'
                            )}
                            removeSuccessMessage={trans(
                                'media.logo.remove_success'
                            )}
                            removeErrorMessage={trans(
                                'media.logo.remove_error'
                            )}
                            previewClassName="sts-logo"
                            onImageChange={onLogoChange(agency.nome)}
                        />
                    </div>
                    {groupExists('zones') && (
                        <div className="gx-section">
                            <ImageUploaderWithPreview
                                title={trans('label.agency_image')}
                                description={
                                    <div className="gx-subtitle">
                                        <span>{`${trans(
                                            'settings.media.image.text1'
                                        )} (`}</span>
                                        <a
                                            onClick={toggleExampleModal(true)}
                                            className="gx-pointer"
                                        >
                                            {trans(
                                                'label.see_example'
                                            ).toLowerCase()}
                                        </a>
                                        <span>{`)`}</span>
                                        <p>
                                            {trans(
                                                'settings.media.image.text2'
                                            )}
                                        </p>
                                    </div>
                                }
                                caption={trans('settings.media.image.text3')}
                                inputKeyName="image"
                                outputKeyName="imageUrl"
                                resultKeyName="url"
                                updateApi={updateImage}
                                removeApi={removeImage}
                                setIsLoading={setIsLoading}
                                updateSuccessMessage={trans(
                                    'media.image.upload_success'
                                )}
                                updateErrorMessage={trans(
                                    'media.image.upload_error'
                                )}
                                removeSuccessMessage={trans(
                                    'media.image.remove_success'
                                )}
                                removeErrorMessage={trans(
                                    'media.image.remove_error'
                                )}
                                previewClassName="sts-image-agency"
                            />
                            <Modal
                                isOpen={isExampleModalOpen}
                                title={trans('label.example')}
                                onClose={toggleExampleModal(false)}
                            >
                                <div>
                                    <div className="settings-media-image-modal">
                                        <div className="settings-media-image-modal__content">
                                            <div className="settings-media-image-modal__contentLegend">
                                                <div className="settings-media-image-modal__contentLegendItem">
                                                    <span>
                                                        {trans(
                                                            'label.agency_image'
                                                        )}
                                                    </span>
                                                    <div />
                                                </div>
                                                <div className="settings-media-image-modal__contentLegendItem">
                                                    <span>
                                                        {trans(
                                                            'label.agency_logo'
                                                        )}
                                                    </span>
                                                    <div />
                                                </div>
                                            </div>
                                            <img
                                                className="settings-media-image-modal__contentImage"
                                                src={IMAGE_EXAMPLE_SRC}
                                                srcSet={IMAGE_EXAMPLE_SRC_SET}
                                            ></img>
                                        </div>
                                    </div>
                                </div>
                            </Modal>
                        </div>
                    )}
                </>
            )}
        </div>
    );
};
