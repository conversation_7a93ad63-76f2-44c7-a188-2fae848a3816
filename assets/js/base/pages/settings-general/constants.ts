import gtxConstants from '@getrix/common/js/gtx-constants';
import { GtxLoggedUser } from 'types/gtxLoggedUser';
import { extractPrivacyInstrumentAppointment } from './utils/utils';
import { GxNavigationBus } from 'lib/gx-navigation-bus';

export const IS_SUPERADMIN: GtxLoggedUser['roles']['ROLE_SUPER_AMMINISTRATORE'] = Boolean(
    window?.['gtxLoggedUser'].roles.ROLE_SUPER_AMMINISTRATORE
);
export const IS_ROLE_BACKOFFICE: GtxLoggedUser['roles']['ROLE_USER_BACKOFFICE'] = Boolean(
    window?.['gtxLoggedUser'].roles.ROLE_USER_BACKOFFICE
);
export const PRIVACY_INSTRUMENT_APPOINTMENT = extractPrivacyInstrumentAppointment();
export const REGEX_EMAIL = new RegExp(gtxConstants('REGEX_EMAIL'));
export const REGEX_PHONE = new RegExp(gtxConstants('REGEX_PHONE'));
export const REGEX_URL = new RegExp(gtxConstants('REGEX_URL'));
export const PHONE_CALLS_ROUTE = gtxConstants('PHONE_CALLS_ROUTE');
export const BASE_URL = `https://${gtxConstants('AD_PORTAL')}`;
export const PRIVACY_STATEMENT_URL = `${BASE_URL}/terms/privacy/`;
export const DEED_APPOINTMENT_URL = `/v2/terms/privacy/atto-nomina`;
/** max length for agency description */
export const DESC_MAX_LENGTH = 3000;
export const UPDATE_AGENCY = GxNavigationBus.createEventType('update-agency', 'in');
