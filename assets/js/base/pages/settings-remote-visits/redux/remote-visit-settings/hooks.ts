import { useDispatch, useSelector } from 'react-redux';
import { TypedUseSelectorHook } from 'react-redux';
import { RemoteVisitConfigDispatch, RemoteVisitConfigState } from './store';

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useRemoteVisitConfigDispatch: () => RemoteVisitConfigDispatch = useDispatch;
export const useRemoteVisitSelector: TypedUseSelectorHook<RemoteVisitConfigState> = useSelector;
