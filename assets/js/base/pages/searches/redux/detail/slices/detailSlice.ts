import {
    ActionCreatorWithOptionalPayload,
    createSlice,
    PayloadAction,
} from '@reduxjs/toolkit';
import { SearchesDetailRootState } from '../store';
import { SearchDetailData } from '../../../detailTypes';

// Define a type for the slice state
type SearchesDetailState = {
    isSidebarOpen: boolean;
    data: SearchDetailData;
};

// Define the initial state using that type
const initialState: SearchesDetailState = {
    isSidebarOpen: false,
    data: null,
};

export const detailSlice = createSlice({
    name: 'detail',
    // `createSlice` will infer the state type from the `initialState` argument
    initialState,
    reducers: {
        setDetailItemData: (state, action: PayloadAction<SearchDetailData>) => {
            state.data = action.payload;
        },
        updateDetailItemData: (
            state,
            action: PayloadAction<Partial<SearchDetailData>>
        ) => {
            state.data = {
                ...state.data,
                ...action.payload,
            };
        },
        setSidebarOpen: (state, action: PayloadAction<boolean>) => {
            state.isSidebarOpen = action.payload;
        },
    },
});

export const { setSidebarOpen, setDetailItemData } = detailSlice.actions;

//Lo esporto esplicitamente perchè da solo RTK non riusciva a fare l'infer del tipo con il Partial
export const updateDetailItemData: ActionCreatorWithOptionalPayload<
    Partial<SearchDetailData>,
    string
> = detailSlice.actions.updateDetailItemData;

// Other code such as selectors can use the imported `RootState` type
export const selectDetailData = (state: SearchesDetailRootState) =>
    state.detail.data;
export const selectDetailSidebarOpen = (state: SearchesDetailRootState) =>
    state.detail.isSidebarOpen;

export default detailSlice.reducer;
