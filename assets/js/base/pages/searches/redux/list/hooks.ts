import { useDispatch, useSelector } from 'react-redux';
import { TypedUseSelectorHook } from 'react-redux';
import { SearchesDispatch, SearchesRootState } from './store';

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useSearchesListDispatch: () => SearchesDispatch = useDispatch;
export const useSearchesListSelector: TypedUseSelectorHook<SearchesRootState> = useSelector;
