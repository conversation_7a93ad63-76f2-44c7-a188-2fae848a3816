import { LIST_PATH, NEW_PATH } from '../web-api/endpoints';
import { SearchesCreate } from '../views/SearchesCreate';
import { searchesNewStore } from '../redux/new/store';
import { GtxPageWithStore } from 'gtx-react/components/GtxApp/GtxApp';
import { getQueryString } from 'lib/utility';
import { getReceivedReqApi } from '../web-api/api/receivedreq';
import { setCreateItemData } from '../redux/new/slices/newSlice';
import { QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from 'lib/queryClient';

export const createPage: GtxPageWithStore = {
    store: searchesNewStore,
    container: SearchesCreate,
    path: NEW_PATH,
    wrapper: ({ children }) => {
        const queryClient = createQueryClient();
        return (
            <QueryClientProvider client={queryClient}>
                {children}
            </QueryClientProvider>
        );
    },
    initFunc: async (dispatch) => {
        const queryString: { [key: string]: string } = getQueryString();
        if (
            queryString['receivedReqId'] &&
            !!parseInt(queryString['receivedReqId'])
        ) {
            const receivedReq = await getReceivedReqApi(
                queryString['receivedReqId']
            );
            dispatch(
                setCreateItemData({
                    ...receivedReq,
                    receivedSearchId: parseInt(queryString['receivedReqId']),
                })
            );
        }
    },
    header: {
        backUrl: LIST_PATH,
    },
};
