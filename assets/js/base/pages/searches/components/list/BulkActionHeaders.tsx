import { MediaQuery } from 'gtx-react/components/MediaQuery';
import { COLUMNS_MEDIA_QUERY } from 'gtx-react/constants';
import React, { useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useStickyElement } from '../../hooks/useStickyElement';
import { selectListSelections } from '../../../../../commons/gtx-react/rtk/slices/lists/listSelectionsSlice';
import { ActionBarBulkMobile } from './ActionBarBulkMobile';
import { createPortal } from 'react-dom';
import { ActionBarBulkDesktop } from './ActionBarBulkDesktop';
import { trans } from '@pepita-i18n/babelfish';
import { ActionBarBulkDeleteDialog } from './ActionBarBulkDeleteDialog';
import { BulkFunc } from '../../redux/list/thunks/listThunks';
import { selectSearchesFilters } from '../../redux/list/selectors/selectSearchesFilters';

interface IBulkActionsHeaders {
    archiveBulk: BulkFunc;
    restoreBulk: BulkFunc;
    deleteBulk: BulkFunc;
}
export const BulkActionHeaders: React.FC<IBulkActionsHeaders> = ({
    archiveBulk,
    restoreBulk,
    deleteBulk,
}) => {
    const selections = useSelector(selectListSelections);
    const bulkActionsRef = useRef<HTMLDivElement>(null);
    const isSticky = useStickyElement(bulkActionsRef);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(null);
    const filters = useSelector(selectSearchesFilters);

    return (
        <>
            <MediaQuery queries={COLUMNS_MEDIA_QUERY}>
                {(match) => (
                    <>
                        {match.MOBILE && selections.length > 0 && (
                            <>
                                <ActionBarBulkMobile
                                    innerRef={bulkActionsRef}
                                    setIsDeleteModalOpen={setIsDeleteModalOpen}
                                />
                                {isSticky &&
                                    createPortal(
                                        <ActionBarBulkMobile
                                            isActionsSticky={isSticky}
                                            setIsDeleteModalOpen={
                                                setIsDeleteModalOpen
                                            }
                                        />,
                                        document.body
                                    )}
                            </>
                        )}
                    </>
                )}
            </MediaQuery>
            <MediaQuery queries={COLUMNS_MEDIA_QUERY}>
                {(match) => (
                    <>
                        {!match.MOBILE && (
                            <ActionBarBulkDesktop
                                show={selections.length > 0}
                                ref={bulkActionsRef}
                                archiveBulkFunc={archiveBulk}
                                restoreBulkFunc={restoreBulk}
                                setIsDeleteModalOpen={setIsDeleteModalOpen}
                                filters={filters}
                                isActionsSticky={isSticky}
                            />
                        )}
                    </>
                )}
            </MediaQuery>

            <ActionBarBulkDeleteDialog
                isDeleteModalOpen={isDeleteModalOpen}
                title={
                    selections.length > 1
                        ? trans('label.searches.bulk_delete.title')
                        : trans('label.searches.delete.title')
                }
                body={
                    selections.length > 1
                        ? trans('label.searches.bulk_delete.confirm', {
                              NUMBER_OF: selections.length,
                          })
                        : trans('label.searches.delete_confirm')
                }
                setIsDeleteModalOpen={setIsDeleteModalOpen}
                deleteBulk={deleteBulk}
            />
        </>
    );
};
