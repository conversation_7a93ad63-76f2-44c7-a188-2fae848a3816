import { Toolbar } from '@gx-design/toolbar';
import { useSelector } from 'react-redux';
import * as Yup from 'yup';
import { selectSearchesFilters } from '../../redux/list/selectors/selectSearchesFilters';
import { useEffect, useState } from 'react';
import { selectPagination } from '../../../../../commons/gtx-react/rtk/slices/lists/paginationSlice';
import {
    activeInitialValue,
    archivedInitialValue,
    IListFilters,
} from '../../constants/filters';
import { TOOLBAR_LABELS } from '../../constants/toolbar';
import { useHasFilters } from './filters/hooks/useHasFilters';
import { trans } from '@pepita-i18n/babelfish';
import { Modal } from '@gx-design/modal';
import { FiltersForm } from './filters/FiltersForm';
import { FiltersFormFooter } from './filters/FiltersFormFooter';
import { getQueryString } from 'lib/utility';
import { useDispatchFetchListWithLock } from '../../hooks/useDispatchFetchListWithLock';
import { selectListCounters } from '../../redux/list/slices/listCounters';
import { ucFirst } from 'lib/strings-formatter';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { Formik } from 'formik';
import { convertObjectKeysToSnakeCase } from 'lib/objects';

export const SearchesListToolBar = () => {
    const { trackEvent } = useMixpanelContext();
    const filters = useSelector(selectSearchesFilters);
    const [isFiltersModalOpen, setIsFiltersModalOpen] =
        useState<boolean>(false);
    const pagination = useSelector(selectPagination);
    const [quickSearchText, setQuickSearchText] = useState<string>(filters.q);
    const hasSomeFilter = useHasFilters(['status']);
    const { dispatchFetchListWithLock } = useDispatchFetchListWithLock();
    const { archived: archivedCount, active: activeCount } =
        useSelector(selectListCounters);

    useEffect(() => {
        if (hasSomeFilter) {
            setQuickSearchText('');
        }

        if (filters.q) {
            setQuickSearchText(filters.q);
        }
    }, [filters]);

    //Update field value on mount
    useEffect(() => {
        const queryString: { [key: string]: string } = getQueryString();
        if (queryString.q) {
            setQuickSearchText(queryString.q);
        }
    }, []);

    const resetQuickSearch = () => {
        setQuickSearchText('');
        dispatchFetchListWithLock({
            pagination: {
                ...pagination,
                page: 1,
            },
            filters: {
                status: filters.status,
            },
        });
    };

    const handleQuickSearch = (value: string) => {
        dispatchFetchListWithLock(
            {
                pagination: {
                    ...pagination,
                    page: 1,
                },
                filters: {
                    q: value,
                    status: filters.status,
                } as Partial<IListFilters>,
            },
            () => {
                setQuickSearchText(value);
            }
        );
        trackEvent({
            event: 'customers_search_selection',
            extra: {
                ['list_tab']: ucFirst(filters.status),
                code: value,
            },
        });
    };

    const handleTabsNavigation = (value: string) => {
        setQuickSearchText('');
        dispatchFetchListWithLock({
            pagination: {
                ...pagination,
                page: 1,
            },
            filters: {
                status: value,
            },
            resetSelectionOnSuccess: true,
        });
    };

    const handleSubmitFilters = (data: any) => {
        setIsFiltersModalOpen(false);
        dispatchFetchListWithLock({
            pagination: {
                ...pagination,
                page: 1,
            },
            filters: {
                ...data,
            },
        });
        trackEvent({
            event: 'customers_filter_selection',
            extra: convertObjectKeysToSnakeCase(data),
        });
    };

    return (
        <>
            <Toolbar
                labels={TOOLBAR_LABELS}
                isFiltersSearchActive={hasSomeFilter}
                searchedText={quickSearchText}
                onShowFiltersAction={() => setIsFiltersModalOpen(true)}
                onInputSearchAction={(value) => handleQuickSearch(value)}
                onNavigateAction={(value) => handleTabsNavigation(value)}
                onResetInputSearchAction={() => resetQuickSearch()}
                onRemoveFiltersAction={() => resetQuickSearch()}
                searchItemsCount={pagination?.total || 0}
            >
                <Toolbar.Navigation
                    navigationItems={[
                        {
                            active: filters.status === 'active',
                            label: trans('label.active_plural_female'),
                            value: 'active',
                            counter: activeCount,
                        },
                        {
                            active: filters.status === 'archived',
                            label: trans('label.archived'),
                            value: 'archived',
                            counter: archivedCount,
                        },
                    ]}
                />
                <Toolbar.Actions />
                <Toolbar.Results />
            </Toolbar>
            <Formik
                initialValues={
                    filters.status === 'active'
                        ? activeInitialValue
                        : archivedInitialValue
                }
                enableReinitialize={true}
                onSubmit={handleSubmitFilters}
                validationSchema={Yup.object().shape({
                    email: Yup.string().email(
                        trans('label.insert_valid_mail_2')
                    ),
                })}
            >
                <Modal
                    isOpen={isFiltersModalOpen}
                    title={trans('label.filter')}
                    footer={<FiltersFormFooter />}
                    onClose={() => setIsFiltersModalOpen(false)}
                >
                    <FiltersForm />
                </Modal>
            </Formik>
        </>
    );
};
