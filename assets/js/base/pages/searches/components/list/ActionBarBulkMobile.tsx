import { ActionBarSelectionsCounter } from './ActionBarSelectionsCounter';
import { useBulkActions } from '../../hooks/useBulkActions';
import { Dropdown } from '@gx-design/dropdown';
import { ActionList, ActionListItem } from '@gx-design/action-list';
import { Icon } from '@gx-design/icon';

interface IActionBarBulkDesktopProps {
    setIsDeleteModalOpen: (isOpen: boolean) => void;
    innerRef?: React.MutableRefObject<HTMLDivElement>;
    isActionsSticky?: boolean;
}

export const ActionBarBulkMobile: React.FC<IActionBarBulkDesktopProps> = ({
    setIsDeleteModalOpen,
    innerRef,
    isActionsSticky,
}) => {
    const dropDownActions = useBulkActions({ setIsDeleteModalOpen });

    return (
        <div
            ref={innerRef}
            className={`messaging__bulkActionsBar ${
                isActionsSticky ? 'messaging__bulkActionsBar--sticky' : ''
            }`}
        >
            <div className="messaging__bulkActionsBarInfo">
                <ActionBarSelectionsCounter />
            </div>
            <div>
                <Dropdown
                    buttonContent={<Icon name="ellipsis" />}
                    buttonIsIconOnly
                    position="bottomRight"
                >
                    <ActionList>
                        {dropDownActions.map((action, index) => (
                            <ActionListItem
                                key={index}
                                onClick={action.onClick}
                                text={action.text}
                                startElement={action.startElement}
                            />
                        ))}
                    </ActionList>
                </Dropdown>
            </div>
        </div>
    );
};
