import { useEffect, useState } from 'react';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { trans } from '@pepita-i18n/babelfish';
import { useDispatch, useSelector } from 'react-redux';
import {
    selectDetailData,
    updateDetailItemData,
} from '../../redux/detail/slices/detailSlice';
import { IntersectionProposedItem } from '../../detailTypes';
import {
    deleteSearchIntersection,
    getSearchIntersectionsCountApi,
    getSearchIntersectionsProposedCountApi,
    getSearchIntersesctionsProposedListApi,
} from '../../web-api/api';
import { SearchDetailCanvasIntersectionItem } from './SearchDetailCanvasIntersectionItem';
import { Loader } from 'gtx-react/components';
import { EmptyState } from '@gx-design/empty-state';
import { loadingEnd, loadingStart } from 'gtx-react/rtk/slices/loaderSlice';
import { showNotify } from 'gtx-react/rtk/slices/notificationsSlice';
import { Tooltip } from '@gx-design/tooltip';
import { Modal } from '@gx-design/modal';
import { formatDate } from 'lib/datefns';

export const SearchesDetailCanvasSent = () => {
    const [intersectionsSent, setIntersectionsSent] = useState<
        IntersectionProposedItem[]
    >([]);
    const detailData = useSelector(selectDetailData);
    const [isLoading, setIsLoading] = useState(false);
    const dispatch = useDispatch();
    const [hasError, setHasError] = useState<boolean>(false);

    useEffect(() => {
        setIsLoading(true);
        getSearchIntersesctionsProposedListApi(detailData.id.toString())
            .then((data) => {
                setIsLoading(false);
                setIntersectionsSent(data);
                setHasError(false);
            })
            .catch(() => {
                setIsLoading(false);
                setHasError(true);
                dispatch(
                    showNotify({
                        type: 'error',
                        message: trans(
                            'label.searches.detail.intersection_proposed.list_fetch_error'
                        ),
                    })
                );
            });
    }, []);

    const [deleteDialog, setDeleteDialog] = useState<{
        isOpen: boolean;
        propertyId: string | null;
    }>({
        isOpen: false,
        propertyId: '',
    });

    return (
        <>
            {isLoading ? (
                <div className="gx-section research-section">
                    <Loader
                        fixedOverlay={false}
                        loading={true}
                        centered
                        inlineOverlay
                    />
                </div>
            ) : (
                <>
                    {!hasError && intersectionsSent.length > 0 ? (
                        <>
                            <div className="gx-section research-section">
                                <div className="research-crosses-sent-intro">
                                    <p
                                        dangerouslySetInnerHTML={{
                                            __html: trans(
                                                'label.searches.sent_properties.info'
                                            ),
                                        }}
                                    ></p>
                                </div>
                            </div>
                            <div className="research-property-list research-property-list--sent">
                                {intersectionsSent.map((intSentItm) => {
                                    return (
                                        <div
                                            className="research-property-row"
                                            key={`intersection-sent-item-${intSentItm.property.id}`}
                                        >
                                            <SearchDetailCanvasIntersectionItem
                                                item={intSentItm.property}
                                            />
                                            <div className="research-property-row__delete">
                                                <div className="research-property-row__sent-date">
                                                    <div className="gx-is-hidden-xsm-down">
                                                        {trans(
                                                            'label.searches.sent_properties.sent_date',
                                                            {
                                                                DATE: formatDate(
                                                                    intSentItm.date,
                                                                    'dd MMM yyyy'
                                                                ),
                                                            }
                                                        )}
                                                    </div>
                                                    <div className="gx-is-hidden-sm-up">
                                                        {formatDate(
                                                            intSentItm.date,
                                                            'dd MMM yyyy',
                                                            {
                                                                fallBackStr:
                                                                    '--',
                                                            }
                                                        )}
                                                    </div>
                                                </div>
                                                <Tooltip
                                                    text={trans(
                                                        'label.searches.sent_properties.delete_btn_tooltip'
                                                    )}
                                                    position="top"
                                                >
                                                    <Button
                                                        onClick={() =>
                                                            setDeleteDialog({
                                                                isOpen: true,
                                                                propertyId:
                                                                    intSentItm.property.id.toString(),
                                                            })
                                                        }
                                                        className=""
                                                        iconOnly
                                                    >
                                                        <Icon name="bin" />
                                                    </Button>
                                                </Tooltip>
                                            </div>
                                        </div>
                                    );
                                })}

                                <Modal
                                    isOpen={deleteDialog.isOpen}
                                    size="small"
                                    title={trans('label.research_delete_sent')}
                                    onClose={() =>
                                        setDeleteDialog({
                                            isOpen: false,
                                            propertyId: '',
                                        })
                                    }
                                    footer={
                                        <>
                                            <Button
                                                variant="ghost"
                                                onClick={() =>
                                                    setDeleteDialog({
                                                        isOpen: false,
                                                        propertyId: '',
                                                    })
                                                }
                                            >
                                                {trans('label.cancel')}
                                            </Button>
                                            <Button
                                                variant="accent"
                                                onClick={() => {
                                                    dispatch(loadingStart());
                                                    setDeleteDialog({
                                                        isOpen: false,
                                                        propertyId:
                                                            deleteDialog.propertyId.toString(),
                                                    });
                                                    deleteSearchIntersection(
                                                        detailData.id.toString(),
                                                        deleteDialog.propertyId.toString()
                                                    )
                                                        .then(() => {
                                                            dispatch(
                                                                showNotify({
                                                                    type: 'success',
                                                                    message:
                                                                        trans(
                                                                            'label.searches.detail.intersection.delete.success'
                                                                        ),
                                                                })
                                                            );
                                                            dispatch(
                                                                loadingEnd()
                                                            );
                                                            setDeleteDialog({
                                                                isOpen: false,
                                                                propertyId: '',
                                                            });
                                                            setIntersectionsSent(
                                                                (currentList) =>
                                                                    currentList.filter(
                                                                        (
                                                                            listItm
                                                                        ) =>
                                                                            listItm.property.id.toString() !==
                                                                            deleteDialog.propertyId.toString()
                                                                    )
                                                            );

                                                            getSearchIntersectionsProposedCountApi(
                                                                detailData.id.toString()
                                                            ).then((data) => {
                                                                dispatch(
                                                                    updateDetailItemData(
                                                                        {
                                                                            intersectionsShared:
                                                                                data,
                                                                        }
                                                                    )
                                                                );
                                                            });

                                                            getSearchIntersectionsCountApi(
                                                                detailData.id.toString()
                                                            )
                                                                .json()
                                                                .then(
                                                                    (resp) => {
                                                                        dispatch(
                                                                            updateDetailItemData(
                                                                                {
                                                                                    intersections:
                                                                                        resp.data,
                                                                                }
                                                                            )
                                                                        );
                                                                    }
                                                                );
                                                        })
                                                        .catch(() => {
                                                            dispatch(
                                                                loadingEnd()
                                                            );
                                                            dispatch(
                                                                showNotify({
                                                                    type: 'error',
                                                                    message:
                                                                        trans(
                                                                            'label.searches.detail.intersection.delete.error'
                                                                        ),
                                                                })
                                                            );
                                                            setDeleteDialog({
                                                                isOpen: false,
                                                                propertyId: '',
                                                            });
                                                        });
                                                }}
                                            >
                                                {trans('label.remove')}
                                            </Button>
                                        </>
                                    }
                                >
                                    <p>
                                        {trans(
                                            'label.research.proposed_delete'
                                        )}
                                    </p>
                                </Modal>
                            </div>
                        </>
                    ) : (
                        <EmptyState
                            title={trans(
                                'label.searches.detail.intersections_list.empty'
                            )}
                        />
                    )}
                </>
            )}
        </>
    );
};
