import { useEffect } from "react"
import { useDispatch } from "react-redux"
import { Dispatch } from "redux"


interface IRTKInitialFuncComponentProps {
    initFunction: (dispatch: Dispatch) => Promise<any>,
    setIsLoading: (isLoading: boolean) => void
    setIsError: (isError: boolean) => void
}


export const RTKInitialFuncComponent: React.FC<IRTKInitialFuncComponentProps> = ({
    initFunction,
    setIsLoading,
    setIsError
}) => {

    const dispatch = useDispatch()

    useEffect(() => {
        initFunction(dispatch)
            .then(() => { setIsError(false) })
            .catch(error => {
                setIsError(true)
            })
            .finally(() => { setIsLoading(false) })
    }, [])

    return null
}
