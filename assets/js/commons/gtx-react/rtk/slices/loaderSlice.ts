import { createSlice, createAction } from '@reduxjs/toolkit'

export type Loader = {
    isLoading: boolean
}

export type WithLoader<T> = T & {
    loader: Loader
}

const submitFiltersError = createAction('SUBMIT_FILTERS_ERROR')
const removeFiltersError = createAction('REMOVE_FILTERS_ERROR')



// Define a type for the slice state
type LoaderState = Loader

// Define the initial state using that type
const initialState: LoaderState = { isLoading: false }

export const loaderSlice = createSlice({
  name: 'loader',
  // `createSlice` will infer the state type from the `initialState` argument
  initialState,
  reducers: {
    loadingStart: state => { state.isLoading = true },
    loadingEnd: state => { state.isLoading = false },
    loadingSearchSubmitStart: state => { state.isLoading = true },
    loadingSearchSubmitEnd: state => { state.isLoading = false }
  },
})

export const { loadingStart, loadingEnd, loadingSearchSubmitEnd, loadingSearchSubmitStart} = loaderSlice.actions

// Other code such as selectors can use the imported `RootState` type
export const selectLoader = (state: WithLoader<unknown>) => state.loader

export default loaderSlice.reducer
