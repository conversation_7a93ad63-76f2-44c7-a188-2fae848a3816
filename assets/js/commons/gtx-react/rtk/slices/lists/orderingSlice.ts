import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { submitFiltersStart } from './filtersSlice'

export type Ordering = {
    field: string|null
    direction: 'ASC'|'DESC'|null
}

export type WithOrdering<T> = T & {
    ordering: Ordering
}



// Define a type for the slice state
type OrderingState = Ordering

// Define the initial state using that type
const initialState: OrderingState = {
    field: null,
    direction: null
}

export const OrderingSlice = createSlice({
  name: 'ordering',
  // `createSlice` will infer the state type from the `initialState` argument
  initialState,
  reducers: {
    orderingStart: (state, action: PayloadAction<OrderingState>) => {
        let { field, direction } = action.payload;

        return {
            field: field,
            direction: direction,
        }
    },
  },
  extraReducers: builder => builder
    .addCase(submitFiltersStart, () => {
        return {
            field: null,
            direction: null,
        }
    })
})

export const { orderingStart } = OrderingSlice.actions

// Other code such as selectors can use the imported `RootState` type
export const selectOrdering = (state: WithOrdering<unknown>) => state.ordering

export default OrderingSlice.reducer
