import thunk from 'redux-thunk';
import { createStore as createReduxStore, applyMiddleware, compose, combineReducers } from 'redux';

const composeWithDevTools = global.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;

const middlewares = [thunk];

export let createStore = (reducers, preloadedState, addmiddlewares) => {
    if (!preloadedState || typeof preloadedState !== 'object') {
        throw new Error(
            'The inital state of the store is missing. Probably there are some problems with the data from the API.'
        );
    }
    const addedmiddlewares = Array.isArray(addmiddlewares) ? addmiddlewares : [];

    return createReduxStore(
        combineReducers(reducers),
        preloadedState,
        composeWithDevTools(applyMiddleware(...middlewares, ...addedmiddlewares))
    );
};
