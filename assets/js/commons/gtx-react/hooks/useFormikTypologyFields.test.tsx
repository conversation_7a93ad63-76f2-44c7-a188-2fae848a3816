import { render, screen, waitFor } from '#tests/react/testing-library-enhanced';
import { QueryClientProvider } from '@tanstack/react-query';
import { Formik } from 'formik';
import { createQueryClient } from 'lib/queryClient';
import { ErrorBoundary } from 'react-error-boundary';
import { describe, expect, it, vitest } from 'vitest';

import { GxFkSelect } from 'gtx-react/components/gx-formik';
import { useFormikTypologyFields } from './useFormikTypologyFields';

const CATEGORIES_RESPONSE = {
    type: 'categories',
    data: [
        { value: '1', label: 'Residenziale', extra: null },
        { value: '2', label: 'Commerciale', extra: null },
        { value: '4', label: 'Stanze', extra: null },
    ],
};

const TYPOLOGIES_RESIDENZIALE_RESPONSE = {
    type: 'typologies',
    data: [
        { value: '4', label: 'Appartamento', extra: null },
        { value: '5', label: 'Attico / Mansarda', extra: null },
        { value: '7', label: 'Casa indipendente', extra: null },
        { value: '31', label: 'Loft / Open Space', extra: null },
        { value: '11', label: 'Rustico / Casale', extra: null },
        { value: '12', label: 'Villa', extra: null },
    ],
};

const TYPOLOGIES_COMMERCIALE_RESPONSE = { type: 'typologies', data: [] };

const TYPOLOGIES_STANZE_RESPONSE = {
    type: 'typologies',
    data: [
        { value: '46', label: 'Altro', extra: null },
        { value: '49', label: 'Appartamento', extra: null },
    ],
};

const CONTRACTS_FULL_RESPONSE = {
    data: [
        { label: 'Vendita', value: 1 },
        { label: 'Affitto', value: 2 },
    ],
};

const CONTRACTS_RENT_ONLY_RESPONSE = {
    data: [{ label: 'Affitto', value: 2 }],
};

const select = ({ data }) => data;
const getCategories = () =>
    new Promise((resolve) => resolve(CATEGORIES_RESPONSE));
const getTypologies = (value: string) =>
    new Promise((resolve) => {
        switch (value) {
            case '1':
                return resolve(TYPOLOGIES_RESIDENZIALE_RESPONSE);
            case '2':
                return resolve(TYPOLOGIES_COMMERCIALE_RESPONSE);
            case '4':
                return resolve(TYPOLOGIES_STANZE_RESPONSE);
            default:
                return resolve(null);
        }
    });
const getContracts = (value: string) =>
    new Promise((resolve) => {
        switch (value) {
            case '1':
            case '2':
                return resolve(CONTRACTS_FULL_RESPONSE);
            case '4':
                return resolve(CONTRACTS_RENT_ONLY_RESPONSE);
            default:
                return resolve(null);
        }
    });

const getContractQueryKey = (value: string) => ['lookup', 'contracts', value];
const getTypologiesQueryKey = (value: string) => [
    'lookup',
    'typologies',
    value,
];

const inputParamsCategory = {
    category: {
        queryKey: ['lookup', 'categories'],
        queryFn: getCategories,
        select,
    },
};

const inputParamsContract = {
    contract: {
        queryKey: getContractQueryKey,
        queryFn: getContracts,
        select,
    },
};

const inputParamsTypology = {
    typology: {
        queryKey: getTypologiesQueryKey,
        queryFn: getTypologies,
        select,
    },
};
const inputParamsFull = {
    ...inputParamsCategory,
    ...inputParamsTypology,
    ...inputParamsContract,
};

const valuesEmpty = {
    category: '',
    typology: '',
    contract: '',
};

const valuesFull = {
    category: '1',
    typology: '12',
    contract: '1',
};

const TypologiesFormFull = () => {
    const getTypologyFieldProps = useFormikTypologyFields(inputParamsFull);
    return (
        <>
            <GxFkSelect {...getTypologyFieldProps('category')} />
            <GxFkSelect {...getTypologyFieldProps('typology')} />
            <GxFkSelect {...getTypologyFieldProps('contract')} />
        </>
    );
};

const TypologiesFormNoTypology = () => {
    const getTypologyFieldProps = useFormikTypologyFields({
        ...inputParamsCategory,
        ...inputParamsContract,
    });
    return (
        <>
            <GxFkSelect {...getTypologyFieldProps('category')} />
            <GxFkSelect {...getTypologyFieldProps('contract')} />
        </>
    );
};

const TypologiesFormNoContract = () => {
    const getTypologyFieldProps = useFormikTypologyFields({
        ...inputParamsCategory,
        ...inputParamsTypology,
    });
    return (
        <>
            <GxFkSelect {...getTypologyFieldProps('category')} />
            <GxFkSelect {...getTypologyFieldProps('typology')} />
        </>
    );
};

const TypologiesFormCustom = () => {
    const getTypologyFieldProps = useFormikTypologyFields({
        category: {
            id: 'categoryId',
            queryKey: ['lookup', 'categories'],
            queryFn: getCategories,
            select,
        },
        contract: {
            queryFn: getContracts,
            queryKey: getContractQueryKey,
            select,
            id: 'contractId',
        },
        typology: {
            queryKey: getTypologiesQueryKey,
            queryFn: getTypologies,
            select,
        },
    });
    return (
        <>
            <GxFkSelect {...getTypologyFieldProps('category')} />
            <GxFkSelect {...getTypologyFieldProps('typology')} />
            <GxFkSelect {...getTypologyFieldProps('contract')} />
        </>
    );
};

const TypologiesFormBad = () => {
    const getTypologyFieldProps = useFormikTypologyFields({
        ...inputParamsCategory,
        ...inputParamsContract,
    });
    return (
        <>
            <GxFkSelect {...getTypologyFieldProps('category')} />
            <GxFkSelect {...getTypologyFieldProps('contract')} />
            <GxFkSelect {...getTypologyFieldProps('typology')} />
        </>
    );
};

const renderFullForm = (initialValues) => {
    const queryClient = createQueryClient();
    return render(
        <QueryClientProvider client={queryClient}>
            <Formik initialValues={initialValues} onSubmit={() => {}}>
                <form role="form">
                    <TypologiesFormFull />
                </form>
            </Formik>
        </QueryClientProvider>
    );
};

const renderNoTypologyForm = () => {
    const queryClient = createQueryClient();
    return render(
        <QueryClientProvider client={queryClient}>
            <Formik
                initialValues={{
                    category: '',
                    contract: '',
                }}
                onSubmit={() => {}}
            >
                <form role="form">
                    <TypologiesFormNoTypology />
                </form>
            </Formik>
        </QueryClientProvider>
    );
};

const renderNoContractForm = () => {
    const queryClient = createQueryClient();
    return render(
        <QueryClientProvider client={queryClient}>
            <Formik
                initialValues={{
                    category: '',
                    typology: '',
                }}
                onSubmit={() => {}}
            >
                <form role="form">
                    <TypologiesFormNoContract />
                </form>
            </Formik>
        </QueryClientProvider>
    );
};

const renderCustomForm = () => {
    const queryClient = createQueryClient();
    return render(
        <QueryClientProvider client={queryClient}>
            <Formik
                initialValues={{
                    categoryId: '',
                    contractId: '',
                    typology: '',
                }}
                onSubmit={() => {}}
            >
                <form role="form">
                    <TypologiesFormCustom />
                </form>
            </Formik>
        </QueryClientProvider>
    );
};

describe('useFormikTypologyFields', () => {
    it('should be correctly integrated into a form, with categories available to choose, and typologies and contracts disabled', () => {
        renderFullForm(valuesEmpty);
        const categorySelect = screen.getByLabelText('label.category');
        const typologySelect = screen.getByLabelText('label.typology');
        const contractSelect = screen.getByLabelText('label.contract');

        expect(categorySelect).toBeInTheDocument();
        expect(categorySelect).toBeEnabled();
        expect(typologySelect).toBeInTheDocument();
        expect(typologySelect).toBeDisabled();
        expect(contractSelect).toBeInTheDocument();
        expect(contractSelect).toBeDisabled();
    });
    it('should enable typologies and contracts if "Residenziale" category is chosen, and be able to select all options', async () => {
        const { user } = renderFullForm(valuesEmpty);
        const categorySelect = screen.getByLabelText('label.category');
        const typologySelect = screen.getByLabelText('label.typology');
        const contractSelect = screen.getByLabelText('label.contract');
        const form = screen.getByRole('form');

        await waitFor(() => {
            expect(categorySelect).toBeEnabled();
        });
        await user.selectOptions(categorySelect, 'Residenziale');
        expect(typologySelect).toBeEnabled();
        expect(contractSelect).toBeEnabled();
        await user.selectOptions(typologySelect, 'Villa');
        await user.selectOptions(contractSelect, 'Vendita');

        expect(form).toHaveFormValues(valuesFull);
    });
    it('should reset and disable typology/contract select, on a pre-filled form, when category is reset', async () => {
        const { user } = renderFullForm(valuesFull);
        const categorySelect = screen.getByLabelText('label.category');
        const typologySelect = screen.getByLabelText('label.typology');
        const contractSelect = screen.getByLabelText('label.contract');
        const form = await screen.findByRole('form');

        expect(form).toHaveFormValues(valuesFull);

        await user.selectOptions(categorySelect, 'label.choose');
        expect(typologySelect).toBeDisabled();
        expect(contractSelect).toBeDisabled();
        expect(form).toHaveFormValues(valuesEmpty);
    });
    it('should disable typology field when there are less than 2 options available', async () => {
        const { user } = renderFullForm(valuesEmpty);
        const categorySelect = screen.getByLabelText('label.category');
        const typologySelect = screen.getByLabelText('label.typology');
        const contractSelect = screen.getByLabelText('label.contract');
        const form = screen.getByRole('form');

        await waitFor(() => {
            expect(categorySelect).toBeEnabled();
        });
        await user.selectOptions(categorySelect, 'Commerciale');
        expect(typologySelect).toBeDisabled();
        expect(contractSelect).toBeEnabled();
        await user.selectOptions(contractSelect, 'Affitto');

        expect(form).toHaveFormValues({
            category: '2',
            typology: '',
            contract: '2',
        });
    });
    it('should disable contract field when there are less than 2 options available', async () => {
        const { user } = renderFullForm(valuesEmpty);
        const categorySelect = screen.getByLabelText('label.category');
        const typologySelect = screen.getByLabelText('label.typology');
        const contractSelect = screen.getByLabelText('label.contract');
        const form = screen.getByRole('form');

        await waitFor(() => {
            expect(categorySelect).toBeEnabled();
        });
        await user.selectOptions(categorySelect, 'Stanze');

        expect(contractSelect).toBeDisabled();
        expect(typologySelect).toBeEnabled();

        await user.selectOptions(typologySelect, 'Altro');

        expect(form).toHaveFormValues({
            category: '4',
            typology: '46',
            contract: '',
        });
    });
    it('should work properly using custom IDs for form fields', async () => {
        const { user } = renderCustomForm();
        const categorySelect = screen.getByLabelText('label.category');
        const typologySelect = screen.getByLabelText('label.typology');
        const contractSelect = screen.getByLabelText('label.contract');
        const form = screen.getByRole('form');

        await waitFor(() => {
            expect(categorySelect).toBeEnabled();
        });
        await user.selectOptions(categorySelect, 'Residenziale');
        expect(typologySelect).toBeEnabled();
        expect(contractSelect).toBeEnabled();
        await user.selectOptions(typologySelect, 'Villa');
        await user.selectOptions(contractSelect, 'Vendita');

        expect(form).toHaveFormValues({
            categoryId: '1',
            typology: '12',
            contractId: '1',
        });
    });
    it('should work properly when using only category and contract fields', async () => {
        const { user } = renderNoTypologyForm();
        const categorySelect = screen.getByLabelText('label.category');
        const contractSelect = screen.getByLabelText('label.contract');
        const form = await screen.findByRole('form');

        expect(categorySelect).toBeEnabled();
        expect(contractSelect).toBeDisabled();

        await user.selectOptions(categorySelect, 'Residenziale');

        expect(contractSelect).toBeEnabled();
        expect(form).toHaveFormValues({
            category: '1',
            contract: '',
        });
        expect(form).not.toHaveFormValues({
            typology: '',
        });
    });
    it('should work properly when using only category and typology fields', async () => {
        const { user } = renderNoContractForm();
        const categorySelect = screen.getByLabelText('label.category');
        const typologySelect = screen.getByLabelText('label.typology');
        const form = await screen.findByRole('form');

        expect(categorySelect).toBeEnabled();
        expect(typologySelect).toBeDisabled();

        await user.selectOptions(categorySelect, 'Residenziale');

        expect(typologySelect).toBeEnabled();
        expect(form).toHaveFormValues({
            category: '1',
            typology: '',
        });
        expect(form).not.toHaveFormValues({
            contract: '',
        });
    });
    it('should throw an error when trying to use a field not specified into input', async () => {
        const queryClient = createQueryClient();
        const fallbackRender = vitest.fn(() => null);
        render(
            <ErrorBoundary fallbackRender={fallbackRender}>
                <QueryClientProvider client={queryClient}>
                    <Formik
                        initialValues={{
                            category: '',
                            contract: '',
                        }}
                        onSubmit={() => {}}
                    >
                        <form role="form">
                            <TypologiesFormBad />
                        </form>
                    </Formik>
                </QueryClientProvider>
            </ErrorBoundary>
        );

        await waitFor(() => expect(fallbackRender).toHaveBeenCalled());
    });
});
