import { act, renderHook } from '@testing-library/react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import { describe, expect, it } from 'vitest';
import * as yup from 'yup';
import { useSearchParamsSchema } from './useSearchParamsSchema';

describe('useSearchParamsSchema', () => {
    it('should the object described in the schema stripping the other query params', async () => {
        const { result } = renderHook(
            () =>
                useSearchParamsSchema(
                    yup.object({
                        name: yup.string(),
                        age: yup.number(),
                    })
                ),
            {
                wrapper({ children }) {
                    return (
                        <MemoryRouter
                            initialEntries={[
                                '/?name=ciccio&age=1&another=stuff',
                            ]}
                        >
                            <Routes>
                                <Route path="/" element={children} />
                            </Routes>
                        </MemoryRouter>
                    );
                },
            }
        );

        let searchParams = result.current[0];
        let setSearchParams = result.current[1];

        expect(searchParams).toEqual({ name: 'cic<PERSON>', age: 1 });

        searchParams = result.current[0];
        setSearchParams = result.current[1];

        act(() => setSearchParams({ name: 'giulio', age: 2 }));

        expect(result.current[0]).toEqual({ name: 'giulio', age: 2 });

        act(() => setSearchParams({ name: 'ciccio' }));

        expect(result.current[0]).toEqual({ name: 'ciccio' });
    });

    it('should avoid to not throw any error if the value is not valid', async () => {
        const { result } = renderHook(
            () =>
                useSearchParamsSchema(
                    yup.object({
                        name: yup.string(),
                        age: yup
                            .number()
                            .transform(
                                (value) => (isNaN(value) ? undefined : value) // This is necessary to avoid the error, it depends on the schema
                            )
                            .optional(),
                    })
                ),
            {
                wrapper({ children }) {
                    return (
                        <MemoryRouter
                            initialEntries={[
                                '/?name=ciccio&age=string&another=stuff',
                            ]}
                        >
                            <Routes>
                                <Route path="/" element={children} />
                            </Routes>
                        </MemoryRouter>
                    );
                },
            }
        );

        let searchParams = result.current[0];

        expect(searchParams).toEqual({ name: 'ciccio' });
    });

    it('should not throw any error if the values are not in the schema', async () => {
        const { result } = renderHook(
            () =>
                useSearchParamsSchema(
                    yup.object({
                        name: yup.string().optional(),
                        age: yup.number().optional(),
                    })
                ),
            {
                wrapper({ children }) {
                    return (
                        <MemoryRouter
                            initialEntries={[
                                '/?notinschema=ciccio&notinschema2=string&another=stuff',
                            ]}
                        >
                            <Routes>
                                <Route path="/" element={children} />
                            </Routes>
                        </MemoryRouter>
                    );
                },
            }
        );

        let searchParams = result.current[0];

        expect(searchParams).toEqual({});
    });

    it('should not append any values if they are not in the schema', async () => {
        const { result } = renderHook(
            () =>
                useSearchParamsSchema(
                    yup.object({
                        name: yup.string().optional(),
                        age: yup.number().optional(),
                    })
                ),
            {
                wrapper({ children }) {
                    return (
                        <MemoryRouter
                            initialEntries={[
                                '/?notinschema=ciccio&notinschema2=string&another=stuff',
                            ]}
                        >
                            <Routes>
                                <Route path="/" element={children} />
                            </Routes>
                        </MemoryRouter>
                    );
                },
            }
        );

        let setSearchParams = result.current[1];

        act(() =>
            // @ts-expect-error this is a test
            setSearchParams({ x: 'ciccio', y: 2, notinschema: 'value' })
        );

        expect(result.current[0]).toEqual({});
    });

    it('cannot set a searchparams with ""', async () => {
        const { result } = renderHook(
            () =>
                useSearchParamsSchema(
                    yup.object({
                        name: yup.string().optional(),
                        lastname: yup.string().nullable(),
                    })
                ),
            {
                wrapper({ children }) {
                    return (
                        <MemoryRouter
                            initialEntries={[
                                '/?name=ciccio&lastname=pasticcio',
                            ]}
                        >
                            <Routes>
                                <Route path="/" element={children} />
                            </Routes>
                        </MemoryRouter>
                    );
                },
            }
        );

        expect(result.current[0]).toEqual({
            name: 'ciccio',
            lastname: 'pasticcio',
        });

        act(() => result.current[1]({ name: 'ciccio', lastname: '' }));

        expect(result.current[0]).toEqual({
            name: 'ciccio',
        });
    });

    it('cannot set a searchparams with null', async () => {
        const { result } = renderHook(
            () =>
                useSearchParamsSchema(
                    yup.object({
                        name: yup.string().optional(),
                        lastname: yup.string().nullable(),
                    })
                ),
            {
                wrapper({ children }) {
                    return (
                        <MemoryRouter
                            initialEntries={[
                                '/?name=ciccio&lastname=pasticcio',
                            ]}
                        >
                            <Routes>
                                <Route path="/" element={children} />
                            </Routes>
                        </MemoryRouter>
                    );
                },
            }
        );

        expect(result.current[0]).toEqual({
            name: 'ciccio',
            lastname: 'pasticcio',
        });

        act(() => result.current[1]({ name: 'ciccio', lastname: null }));

        expect(result.current[0]).toEqual({
            name: 'ciccio',
        });
    });

    it('cannot set a searchparams with undefined', async () => {
        const { result } = renderHook(
            () =>
                useSearchParamsSchema(
                    yup.object({
                        name: yup.string().optional(),
                        lastname: yup.string().nullable(),
                    })
                ),
            {
                wrapper({ children }) {
                    return (
                        <MemoryRouter
                            initialEntries={[
                                '/?name=ciccio&lastname=pasticcio',
                            ]}
                        >
                            <Routes>
                                <Route path="/" element={children} />
                            </Routes>
                        </MemoryRouter>
                    );
                },
            }
        );

        expect(result.current[0]).toEqual({
            name: 'ciccio',
            lastname: 'pasticcio',
        });

        act(() => result.current[1]({ name: 'ciccio', lastname: undefined }));

        expect(result.current[0]).toEqual({
            name: 'ciccio',
        });
    });
});
