import { useEffect } from 'react';

export const useListener = (
    element,
    name,
    callback,
    enabled
) => {
    useEffect(() => {
        if (enabled === false) {
            return;
        }

        if (!element) {
            return;
        }

        element.addEventListener(name, callback);

        return () => {
            element.removeEventListener(name, callback);
        };
    }, [callback, enabled]);
};
