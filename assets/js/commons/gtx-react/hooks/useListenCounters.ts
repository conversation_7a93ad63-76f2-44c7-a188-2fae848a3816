import { captureException } from '@sentry/browser';
import { useState, useEffect } from 'react';
import * as Yup from 'yup';

const countersSchema = Yup.object().shape({
    newAgencyActiveSearches: Yup.number().required(),
    newMatches: Yup.number().required(),
    unreadThreads: Yup.number().required(),
});

const getJson = (value: string) => {
    try {
        return JSON.parse(value);
    } catch (err) {
        throw new Error('Unable to parse string to JSON');
    }
};

type CountersObjectFromLocalStorage = Yup.InferType<typeof countersSchema>;

export const useListenCounters = () => {
    const [counters, setCounters] = useState<CountersObjectFromLocalStorage>({
        newAgencyActiveSearches: 0,
        newMatches: 0,
        unreadThreads: 0,
    });

    useEffect(() => {
        const countersString = localStorage.getItem('gtx-counters-data');

        if (countersString) {
            try {
                const countersObj = getJson(countersString);
                const countersData = countersSchema.validateSync(countersObj);
                setCounters(countersData);
            } catch (error) {
                captureException(error);
            }
        }
    }, []);

    useEffect(() => {
        const updateCounter = (evt: Event) => {
            setCounters((evt as CustomEvent<CountersObjectFromLocalStorage>).detail);
        };

        window.addEventListener('gtx_menu_counter_manager_counter_update', updateCounter);

        return () => window.removeEventListener('gtx_menu_counter_manager_counter_update', updateCounter);
    }, []);

    return { counters };
};
