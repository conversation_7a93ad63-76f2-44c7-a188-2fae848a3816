import { ReactElement, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

/**
 * This hook is used to create a portal for the actions in the navigation bar.
 * @param component The component to be rendered in the portal.
 * @returns The portal component.
 */
export const useGxNavActionsPortal = (component: ReactElement) => {
    const [gxNavPortal, setGxNavPortal] = useState<HTMLElement | null>(
        document.getElementById('gx-navigation-actions-portal')
    );

    useEffect(() => {
        const gxNavElement = document.getElementById('gx-navigation');

        let observer: MutationObserver | null = null;

        if (gxNavElement) {
            observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        const portalContainer = document.getElementById(
                            'gx-navigation-actions-portal'
                        );
                        if (portalContainer) {
                            setGxNavPortal(portalContainer);
                        }
                    }
                });
            });

            observer.observe(gxNavElement, {
                childList: true,
                subtree: true,
            });
        }

        return () => {
            if (observer) {
                observer.disconnect();
            }
        };
    }, []);

    return <>{gxNavPortal && createPortal(component, gxNavPortal)}</>;
};
