import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { RadioGroup } from '@gx-design/radio';
import { trans } from '@pepita-i18n/babelfish';
import { useFormikContext } from 'formik';
import { useState } from 'react';

import {
    GxFkButtonInput,
    GxFkRadio,
    GxFkRangeInput,
    GxFkSelect,
} from 'gtx-react/components/gx-formik';
import { useFormikTypologyFields } from 'gtx-react/hooks/useFormikTypologyFields';
import { getCategories, getContracts, getTypologies } from '../../web-api';

const TYPOLOGY_CONFIG = {
    category: {
        id: 'categories',
        queryKey: ['lookup', 'categories'],
        queryFn: getCategories,
        select: ({ data }) => data,
    },
    typology: {
        id: 'typologyIds',
        queryKey: (value: string) => ['lookup', 'typologies', value],
        queryFn: getTypologies,
        select: ({ data }) => data,
    },
    contract: {
        id: 'contractId',
        queryKey: (value: string) => ['lookup', 'contracts', value],
        queryFn: getContracts,
        select: ({ data }) => data,
    },
};

type Props = { hasFilters?: boolean };

export const ChangeRealEstateFilters: React.FC<Props> = ({ hasFilters }) => {
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const { submitForm, resetForm } = useFormikContext();
    const getTypologyFieldProps = useFormikTypologyFields(TYPOLOGY_CONFIG);

    const toggleOpen = () => setIsOpen((prev) => !prev);

    const onClick = (reset: boolean) => () => {
        reset && resetForm();
        submitForm();
        toggleOpen();
    };

    return (
        <>
            <div className="iv-changeRealEstate__search">
                <GxFkButtonInput
                    name="q"
                    isLabelVisible={false}
                    label=""
                    placeholder={trans('label.search_string_form')}
                    buttonContent={trans('label.search')}
                    onButtonClick={submitForm}
                    className="iv-changeRealEstate__searchInput"
                />
                <Button
                    onClick={toggleOpen}
                    className={
                        !isOpen && hasFilters ? 'is-selected' : undefined
                    }
                >
                    <Icon name="sliders" />
                    <span>
                        {trans(isOpen ? 'label.close' : 'label.filters')}
                    </span>
                </Button>
            </div>
            {isOpen && (
                <div className="iv-changeRealEstateFilters">
                    <div className="iv-changeRealEstateFilters__row">
                        <GxFkSelect {...getTypologyFieldProps('category')} />
                        <GxFkSelect {...getTypologyFieldProps('typology')} />
                        <GxFkSelect {...getTypologyFieldProps('contract')} />
                    </div>
                    <div className="iv-changeRealEstateFilters__row">
                        <GxFkRangeInput
                            name="price"
                            label={trans('label.price')}
                            type="number"
                            min={0}
                        />
                        <GxFkRangeInput
                            name="surface"
                            label={trans('label.surface')}
                            type="number"
                            min={0}
                        />
                    </div>
                    <div className="iv-changeRealEstateFilters__row">
                        <RadioGroup
                            label={trans(
                                'label.immovisita.guest_change_realestate.radio_title'
                            )}
                            variant="button"
                        >
                            <GxFkRadio
                                label={trans('label.yes')}
                                name="withVirtualTour"
                                value="true"
                            />
                            <GxFkRadio
                                label={trans('label.no')}
                                name="withVirtualTour"
                                value=""
                            />
                        </RadioGroup>
                        <div className="iv-changeRealEstateFilters__buttons">
                            {hasFilters && (
                                <Button variant="ghost" onClick={onClick(true)}>
                                    <span>{trans('label.remove_filters')}</span>
                                </Button>
                            )}
                            <Button variant="accent" onClick={onClick(false)}>
                                <span>{trans('label.apply_filters')}</span>
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};
