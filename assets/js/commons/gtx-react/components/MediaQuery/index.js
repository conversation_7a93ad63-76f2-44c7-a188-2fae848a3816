import React, { Component } from 'react';

let createMediaQueries = (queries) => Object.keys(queries).map((name) => [name, matchMedia(queries[name])]);

let listenMediaQueries = (mediaList, callback) =>
    mediaList.forEach(([_, media]) => {
        media.addListener(callback);
    });

let removeMediaQueriesListeners = (mediaList, callback) =>
    mediaList.forEach(([_, media]) => {
        media.removeListener(callback);
    });

let getMediaMatches = (mediaList) =>
    mediaList.reduce((matches, [name, media]) => {
        matches[name] = media.matches;

        return matches;
    }, {});
/**
 * @typedef {Object} ListFieldProps
 * @property {string} [className]
 * @property {React.ReactNode} [children]
 * @property {boolean} [interactive]
 */

/**
 * @param {ListFieldProps} props
 */
export class MediaQuery extends Component {
    activeMediaQueries = createMediaQueries(this.props.queries);

    state = {
        matches: getMediaMatches(this.activeMediaQueries),
    };

    componentDidMount() {
        listenMediaQueries(this.activeMediaQueries, this.handleMediaChange);
    }

    componentWillUnmount() {
        removeMediaQueriesListeners(this.activeMediaQueries, this.handleMediaChange);
    }

    handleMediaChange = () => this.setState({ matches: getMediaMatches(this.activeMediaQueries) });

    render() {
        return this.props.children ? this.props.children(this.state.matches) : null;
    }
}

export const VIEWPORT_MEDIA_QUERIES = {
    isDesktop: '(min-width: 1024px)',
    isTablet: '(min-width: 768px) and (max-width: 1023px)',
    isMobile: '(max-width: 767px)',
};

export let ViewportMediaQuery = ({ children }) => <MediaQuery queries={VIEWPORT_MEDIA_QUERIES}>{children}</MediaQuery>;
