import gtxConstants from '@getrix/common/js/gtx-constants';
import { trans } from '@pepita-i18n/babelfish';
import { Formik, useFormikContext } from 'formik';
import type { PropsWithChildren } from 'react';
import { object, string, type InferType } from 'yup';
import { GxFkInput, GxFkTextarea } from '../gx-formik';

const requestServiceSchema = object().shape({
    name: string().required(trans('label.required_value')),
    subject: string().required(trans('label.required_value')),
    message: string(),
    telephone: string()
        .matches(
            new RegExp(gtxConstants('REGEX_PHONE')),
            trans('phone_verification.valid_number.error')
        )
        .required(trans('label.required_value')),

    email: string()
        .matches(
            new RegExp(gtxConstants('REGEX_EMAIL')),
            trans('register.form.error.mail')
        )
        .required(trans('label.required_value')),
});

type RequestServiceValues = InferType<typeof requestServiceSchema>;

type RequestServiceFormProps = PropsWithChildren<{
    onSubmit: (values: RequestServiceValues) => void;
    initialValues?: Partial<RequestServiceValues>;
}>;

export const useRequestServiceFormikContext = () =>
    useFormikContext<RequestServiceValues>();

export function RequestServiceForm(props: RequestServiceFormProps) {
    return (
        <Formik
            initialValues={{
                name: '',
                telephone: '',
                email: '',
                message: '',
                subject: '',
                ...props.initialValues,
            }}
            validationSchema={requestServiceSchema}
            onSubmit={(values) => {
                props.onSubmit(values);
            }}
        >
            {props.children}
        </Formik>
    );
}

type RequestServiceFieldsProps = PropsWithChildren<{
    testId?: string;
}>;

export function RequestServiceFields(props: RequestServiceFieldsProps) {
    const formik = useFormikContext<RequestServiceValues>();

    return (
        <form data-testid={props.testId} onSubmit={formik.handleSubmit}>
            <div>
                <div className="gx-row">
                    <div className="gx-col-xs-12">
                        <div className="gx-box-row">
                            <GxFkInput
                                id="request-service-subject"
                                label={trans('label.subject')}
                                type="text"
                                required
                                disabled
                                name="subject"
                            />
                        </div>
                    </div>
                </div>
                <div className="gx-row">
                    <div className="gx-col-xs-12">
                        <div className="gx-box-row">
                            <GxFkInput
                                label={trans('label.referent')}
                                id="request-service-referent"
                                type="text"
                                name="name"
                                required
                            />
                        </div>
                    </div>
                </div>
                <div className="gx-row">
                    <div className="gx-col-xs-6">
                        <div className="gx-box-row">
                            <GxFkInput
                                label={trans('label.phone')}
                                id="request-service-telephone"
                                type="text"
                                required
                                name="telephone"
                            />
                        </div>
                    </div>
                    <div className="gx-col-xs-6">
                        <div className="gx-box-row">
                            <GxFkInput
                                label={trans('label.mail')}
                                id="request-service-email"
                                type="email"
                                name="email"
                                required
                            />
                        </div>
                    </div>
                </div>
                <div className="gx-row">
                    <div className="gx-col-xs-12">
                        <div className="gx-box-row">
                            <GxFkTextarea
                                label={`${trans('label.message')} (${trans(
                                    'label.optional'
                                )})`}
                                id="request-service-message"
                                name="message"
                                rows={3}
                            />
                        </div>
                    </div>
                </div>
            </div>
            {props.children}
        </form>
    );
}
