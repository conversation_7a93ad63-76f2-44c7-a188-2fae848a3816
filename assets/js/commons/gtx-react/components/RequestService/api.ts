import { http } from '@pepita/http';

const endpoint = '/amministrazione/customer-service-request';

export type CreateRequestServiceArgs = {
    servizio: string;
    sezione?: string;
    tipo: string;
    oggetto: string;
    nome: string;
    telefono: string;
    email: string;
    messaggio?: string;
    /**  Should be a stringified JSON */
    extraInfo?: string | null;
    riepilogo?: string;
};

/**
 * This function is used to send a request to the customer service.
 * @example
 * // A request to increase the advertising spaces on Immobiliare.it
 * {
 *   servizio: 'immobiliare'
 *   sezione: ''
 *   tipo: 'increaseadvspaces'
 *   oggetto: 'Richiesta aumento spazi pubblicitari "Immobiliare.it"'
 *   nome: '<PERSON>chino'
 *   telefono: '+393391239874'
 *   email: '<EMAIL>'
 *   messaggio: ''
 *   extraInfo: 'null'
 * }
 * @description Returns `true` if the request was successful, `false` otherwise.
 */

export async function createRequestService({
    email,
    extraInfo,
    messaggio,
    nome,
    oggetto,
    servizio,
    sezione,
    telefono,
    tipo,
    riepilogo,
}: CreateRequestServiceArgs) {
    const response = await http
        .post(endpoint, {
            form: {
                email,
                extraInfo: extraInfo || '',
                messaggio: messaggio || '',
                nome,
                oggetto,
                servizio,
                sezione: sezione || null,
                telefono,
                tipo,
                riepilogo,
            },
        })
        .raw();

    return response.status === 204;
}
