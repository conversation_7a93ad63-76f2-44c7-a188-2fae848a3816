import { render, screen } from '#tests/react/testing-library-enhanced';
import { describe, expect, it, vi } from 'vitest';
import { RequestServiceFields, RequestServiceForm } from './RequestServiceForm';

describe('RequestServiceForm', () => {
    it('should render correctly', () => {
        const onSubmitMock = vi.fn();
        render(
            <RequestServiceForm onSubmit={onSubmitMock}>
                <RequestServiceFields />
            </RequestServiceForm>
        );

        expect(screen.getByLabelText(/label.subject/)).toBeInTheDocument();
        expect(screen.getByLabelText(/label.referent/)).toBeInTheDocument();
        expect(screen.getByLabelText(/label.message/)).toBeInTheDocument();
    });

    it('should call the onSubmit function with minimum values', async () => {
        const onSubmitMock = vi.fn();
        const { user } = render(
            <RequestServiceForm
                initialValues={{
                    subject: 'Attivazione qualcosa',
                }}
                onSubmit={onSubmitMock}
            >
                <RequestServiceFields testId="form-id">
                    <button type="submit">submit</button>
                </RequestServiceFields>
            </RequestServiceForm>
        );

        await user.click(screen.getByRole('button', { name: 'submit' }));

        expect(onSubmitMock).not.toHaveBeenCalled();

        await user.type(screen.getByLabelText(/label.referent/), 'Mario Rossi');
        await user.type(screen.getByLabelText(/label.phone/), '3333333333');

        await user.type(screen.getByLabelText(/label.mail/), '<EMAIL>');

        await user.click(screen.getByRole('button', { name: 'submit' }));

        expect(onSubmitMock).toHaveBeenCalledWith({
            email: '<EMAIL>',
            message: '',
            name: 'Mario Rossi',
            subject: 'Attivazione qualcosa',
            telephone: '3333333333',
        });
    });
});
