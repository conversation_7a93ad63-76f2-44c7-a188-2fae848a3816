import { FC, useState } from 'react';
import { Dropdown } from '@gx-design/dropdown';
import { ActionList, ActionListItem } from '@gx-design/action-list';

export type ColorpickerProps = {
    colors: Array<string>;
    colorDefault?: string;
    onColorSelect?: (selectedColor: string) => unknown;
};

export const Colorpicker: FC<ColorpickerProps> = ({
    colors,
    colorDefault = '',
    onColorSelect,
}) => {
    const [selectedColor, setSelectedColor] = useState<string>(colorDefault);

    const onColorClick = (color: string) => () => {
        setSelectedColor(color);
        if (onColorSelect) {
            onColorSelect(color);
        }
    };

    return (
        <div className="color-picker" data-testid="color-picker">
            <Dropdown
                buttonContent={
                    <div
                        className="color-picker__selected"
                        style={{ backgroundColor: selectedColor }}
                        data-testid="color-picker-button"
                    />
                }
                position="bottomLeft"
                showCaret
                className="gx-dropdown--intoModal"
            >
                <ActionList>
                    {colors.map((color, idx) => (
                        <ActionListItem
                            key={`color-picker-item-${idx}`}
                            text=""
                            onClick={onColorClick(color)}
                            startElement={
                                <div
                                    className="color-picker__item-value"
                                    style={{ backgroundColor: color }}
                                    data-testid="color-picker-item"
                                />
                            }
                        />
                    ))}
                </ActionList>
            </Dropdown>
        </div>
    );
};
