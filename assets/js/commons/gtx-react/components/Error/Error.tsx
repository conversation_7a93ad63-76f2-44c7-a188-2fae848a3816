import React from 'react';
import { trans } from '@pepita-i18n/babelfish';
import errorImage from '@getrix/common/img/error/error404.png';

type ErrorProps = {
    image?: string;
    title?: string;
    description?: string;
};

export const Error: React.FC<ErrorProps> = ({ image, title, description }) => {
    return (
        <div className="gx-error">
            <div className="gx-error__content">
                <div className="gx-error__pic">
                    <img src={image ? image : errorImage} />
                </div>
                <h3 className="gx-error__title">
                    {title ? title : trans('label.error')}
                </h3>
                <p className="gx-error__text">
                    {description ? description : trans('error.generic.retry')}
                </p>
            </div>
        </div>
    );
};
