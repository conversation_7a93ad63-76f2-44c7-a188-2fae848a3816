import { render, screen } from '#tests/react/testing-library-enhanced';
import { describe, it, expect, vi } from 'vitest';
import { ContentDropdownContext } from './useContentDropdown';
import { ContentDropdown } from './ContentDropdown';

describe('DropdownRangeInput', () => {
    it('DropdownRangeInput - renders children correctly', () => {
        render(
            <ContentDropdownContext.Provider
                value={{ isOpen: true, setIsOpen: vi.fn() }}
            >
                <ContentDropdown
                    renderAction={() => <button>Test Action</button>}
                >
                    <div>Test Child</div>
                </ContentDropdown>
            </ContentDropdownContext.Provider>
        );

        expect(screen.getByText('Test Child')).toBeInTheDocument();
    });
});
