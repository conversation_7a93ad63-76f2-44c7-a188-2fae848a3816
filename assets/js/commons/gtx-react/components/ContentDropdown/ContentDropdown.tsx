import React, {
    useState,
    useEffect,
    useRef,
    ReactNode,
    useMemo,
    useCallback,
} from 'react';
import { createPortal } from 'react-dom';
import useForkedRefs from '@gx-design/use-forked-refs';
import { useOnClickOutside } from '@gx-design/use-on-click-outside';
import { useContentDropdown } from './useContentDropdown';
import { GxFkNumberInput } from './DropdownRangeInput/NumberInput';
import { RangeOptionsList } from './DropdownRangeInput/RangeOptionsList';
import { DropdownFooter } from './DropdownFooter';
import { OptionsList } from './DropdownSelectInput/OptionsList';
import { useContentDropdownContext } from './ContentDropdownContext';
import ResizeObserver from 'resize-observer-polyfill';

// ! This is a patch and should be removed introducing new dropdowns with floating ui
function isSafari13OrLower() {
    const ua = navigator.userAgent;

    const isSafari =
        ua.includes('Safari') &&
        !ua.includes('Chrome') &&
        !ua.includes('CriOS') &&
        !ua.includes('FxiOS') &&
        !ua.includes('EdgiOS');

    if (!isSafari) {
        return false;
    }

    const match = ua.match(/Version\/([\d.]+)/);
    if (!match) {
        return false;
    }

    const version = parseInt(match[1]?.split('.')[0] ?? '', 10);

    return version <= 13;
}

export type ContentDropdownProps = {
    children: ReactNode | Array<ReactNode>;
    position?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
    onClickOutside?: () => void;
    renderAction: (props: {
        ref: React.RefObject<HTMLButtonElement>;
    }) => ReactNode;
};

const BaseContentDropdown = React.forwardRef<
    HTMLDivElement,
    ContentDropdownProps
>(
    (
        {
            children,
            position = 'bottom-left',
            onClickOutside = () => undefined,
            renderAction,
        },
        ref
    ) => {
        const { isOpen, setIsOpen } = useContentDropdown();
        const { dropdownAnchorElement, onDropdownChange } =
            useContentDropdownContext();

        useEffect(() => {
            onDropdownChange?.(isOpen);
        }, [isOpen, onDropdownChange]);

        const buttonRef = useRef<HTMLButtonElement>(null);
        const innerRef = useRef<HTMLElement>(null);

        const dropdownRef = useForkedRefs(innerRef, ref);

        const [coords, setCoords] = useState<React.CSSProperties>({
            top: 0,
            left: 0,
            display: 'none',
        });

        const refs = useMemo(
            () => [innerRef, buttonRef],
            [innerRef, buttonRef]
        );

        useOnClickOutside({
            refs,
            handler: () => {
                setIsOpen(false);
                onClickOutside();
            },
            shouldListen: isOpen,
        });

        const updateCoords = useCallback(() => {
            const dropdownEl = innerRef.current;
            const buttonEl = buttonRef.current;
            if (!dropdownEl || !buttonEl) {
                return;
            }

            const buttonRect = buttonEl.getBoundingClientRect();
            const dropdownRect = dropdownEl.getBoundingClientRect();
            const rootEl = dropdownAnchorElement?.current || document.body;
            const rootRect =
                dropdownAnchorElement?.current?.getBoundingClientRect?.();
            const gxSpacing = 4;

            const buttonX = rootRect ? buttonRect.x - rootRect.x : buttonRect.x;
            const buttonY = rootRect ? buttonRect.y - rootRect.y : buttonRect.y;

            switch (position) {
                case 'bottom-right':
                    setCoords({
                        top:
                            buttonY +
                            buttonRect.height +
                            gxSpacing +
                            rootEl.scrollTop,
                        left:
                            buttonX +
                            buttonRect.width -
                            dropdownRect.width +
                            rootEl.scrollLeft,
                        display: 'block',
                    });
                    break;
                case 'bottom-left':
                default:
                    setCoords({
                        top:
                            buttonY +
                            buttonRect.height +
                            gxSpacing +
                            rootEl.scrollTop,
                        left: buttonX + rootEl.scrollLeft,
                        display: 'block',
                    });
                    break;
            }
        }, [position, dropdownAnchorElement]);

        useEffect(() => {
            if (!isOpen || !innerRef.current || !buttonRef.current) {
                return;
            }

            const dropdownEl = innerRef.current;

            // !This is a patch and should be removed introducing new dropdowns with floating ui
            if (isSafari13OrLower()) {
                updateCoords();
            }

            const observer = new ResizeObserver(() => {
                updateCoords();
            });

            observer.observe(dropdownEl);

            return () => observer.disconnect();
        }, [isOpen, position, dropdownAnchorElement, updateCoords]);

        return (
            <>
                {renderAction({
                    ref: buttonRef,
                })}

                {isOpen &&
                    createPortal(
                        <div
                            style={coords}
                            ref={dropdownRef}
                            className="gx-range-input gx-dropdown"
                        >
                            {children}
                        </div>,
                        dropdownAnchorElement?.current || document.body
                    )}
            </>
        );
    }
);

type CompoundComponent = typeof BaseContentDropdown & {
    GxFkNumberInput: typeof GxFkNumberInput;
    RangeOptionsList: typeof RangeOptionsList;
    Footer: typeof DropdownFooter;
    OptionsList: typeof OptionsList;
};

const ContentDropdown = BaseContentDropdown as CompoundComponent;
ContentDropdown.GxFkNumberInput = GxFkNumberInput;
ContentDropdown.RangeOptionsList = RangeOptionsList;
ContentDropdown.Footer = DropdownFooter;
ContentDropdown.OptionsList = OptionsList;

export { ContentDropdown };
