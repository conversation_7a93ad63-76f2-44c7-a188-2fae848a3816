import React, { FC, useEffect, useRef } from 'react';
import { useFormikContext } from 'formik';
import clsx from 'clsx';
import { ensureVisible, Align } from '@pepita/dom-ensure-visible';
import { useContentDropdown } from '../useContentDropdown';

export type DropdownSelectInputOption = {
    label: string;
    value: string | number;
    selected?: boolean;
    disabled?: boolean;
};

type OptionsListProps = {
    inputName: string;
    options: DropdownSelectInputOption[];
};

export const OptionsList: FC<OptionsListProps> = ({ inputName, options }) => {
    const { setFieldValue, values, submitForm } =
        useFormikContext<Record<string, any>>();

    const { setIsOpen } = useContentDropdown();

    const listRef = useRef<HTMLDivElement>(null);
    const selectedElementRef = useRef<HTMLLIElement>(null);

    const onOptionClick = (value: DropdownSelectInputOption['value']) => () => {
        if (value !== values[inputName]) {
            setFieldValue(inputName, value);
            submitForm();
        }
        setIsOpen(false);
    };

    useEffect(() => {
        if (selectedElementRef?.current && listRef?.current) {
            ensureVisible(selectedElementRef.current, listRef.current, {
                force: true,
                align: Align.Center,
            });
        }
    }, []);

    return (
        <div ref={listRef} className="gx-range-input__list">
            <ul data-testid="options-list">
                {options.map(({ label, value }) => (
                    <li
                        className={clsx([
                            'gx-range-input__listItem',
                            {
                                'is-selected': values[inputName] === value,
                            },
                        ])}
                        key={`option-${label}-${value}`}
                        onClick={onOptionClick(value)}
                        ref={
                            value === values[inputName]
                                ? selectedElementRef
                                : null
                        }
                    >
                        {label}
                    </li>
                ))}
            </ul>
        </div>
    );
};
