import { render, screen } from '#tests/react/testing-library-enhanced';
import { describe, it, expect, vi } from 'vitest';
import { Formik } from 'formik';
import { ComponentProps } from 'react';
import { DropdownSelectInput } from './DropdownSelectInput';

const SELECT_OPTIONS = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
];

const renderDropdown = (
    props?: Omit<
        ComponentProps<typeof DropdownSelectInput>,
        'name' | 'maxInputName' | 'options' | 'placeholder'
    > & {
        onSubmit?: () => void;
    }
) => {
    return render(
        <DropdownSelectInput
            {...props}
            name="test"
            options={SELECT_OPTIONS}
            placeholder="Select options"
        />,
        {
            wrapper({ children }) {
                return (
                    <>
                        <div data-testid="outside">Outside Element</div>
                        <Formik
                            initialValues={{ min: '', max: '' }}
                            onSubmit={props?.onSubmit || (() => {})}
                        >
                            {children}
                        </Formik>
                    </>
                );
            },
        }
    );
};

describe('DropdownSelectInput', () => {
    it('DropdownSelectInput - renders RangeOptions', async () => {
        const { user } = renderDropdown();

        const dropdown = screen.getByRole('button', {
            name: 'Select options',
        });
        expect(dropdown).toBeInTheDocument();

        await user.click(dropdown);
        expect(screen.getByTestId('options-list')).toBeInTheDocument();
    });

    it('DropdownSelectInput - should submit when clicking on a list element', async () => {
        const onSubmit = vi.fn();
        const { user } = renderDropdown({ onSubmit });

        const dropdown = screen.getByRole('button', {
            name: 'Select options',
        });
        await user.click(dropdown);

        expect(screen.getByTestId('options-list')).toBeInTheDocument();

        const option1 = screen.getByText('Option 1');
        await user.click(option1);
        expect(onSubmit).toHaveBeenCalled();
        expect(dropdown).toHaveTextContent('Option 1');
    });
});
