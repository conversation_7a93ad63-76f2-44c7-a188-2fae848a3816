import { FC, useState } from 'react';
import { useFormikContext } from 'formik';
import { ContentDropdown } from '../ContentDropdown';
import { DropdownSelectInputOption } from './OptionsList';
import { Icon, IconProps } from '@gx-design/icon';
import { ContentDropdownContext } from '../useContentDropdown';
import { Button } from '@gx-design/button';
import clsx from 'clsx';

export type DropdownSelectInputProps = {
    name: string;
    options: DropdownSelectInputOption[];
    placeholder: string;
    selected?: boolean;
    icon?: IconProps['name'];
};

export const DropdownSelectInput: FC<DropdownSelectInputProps> = ({
    name,
    options,
    placeholder,
    selected,
    icon,
}) => {
    const { values } = useFormikContext<Record<string, any>>();
    const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);

    const buttonContent = values[name]
        ? options.find((opt) => opt.value === values[name])?.label
        : placeholder;

    return (
        <ContentDropdownContext.Provider
            value={{
                isOpen: isDropdownOpen,
                setIsOpen: (value) => setIsDropdownOpen(value),
            }}
        >
            <ContentDropdown
                renderAction={({ ref }) => (
                    <Button
                        ref={ref}
                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                        className={clsx('gx-range-inputControl', {
                            'is-open': isDropdownOpen,
                            'is-selected': selected,
                        })}
                        type="button"
                    >
                        <Icon
                            className="gx-range-inputControl__icon"
                            name={icon as IconProps['name']}
                        />
                        <span>{buttonContent}</span>
                        <Icon
                            className="gx-range-inputControl__caret"
                            name="arrow-down"
                        />
                    </Button>
                )}
            >
                <ContentDropdown.OptionsList
                    inputName={name}
                    options={options}
                />
            </ContentDropdown>
        </ContentDropdownContext.Provider>
    );
};
