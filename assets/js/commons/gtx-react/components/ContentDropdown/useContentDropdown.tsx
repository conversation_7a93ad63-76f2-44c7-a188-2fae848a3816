import { createContext, useContext } from 'react';

type ContentDropdownContextType = {
    isOpen: boolean;
    setIsOpen: (v: boolean) => void;
};

export const ContentDropdownContext = createContext<
    ContentDropdownContextType | undefined
>(undefined);

export const useContentDropdown = () => {
    const ctx = useContext(ContentDropdownContext);
    if (!ctx) {
        throw new Error(
            'useContentDropdown must be used within a ContentDropdownContext.Provider'
        );
    }
    return ctx;
};
