import { FC, useEffect, useMemo, useRef, useState } from 'react';
import { useFormikContext } from 'formik';
import {
    buttonContent,
    checkAndSwitchValues,
    filterRangeOptions,
} from './utils';
import { Icon, IconProps } from '@gx-design/icon';
import { ContentDropdownContext } from '../useContentDropdown';
import { ContentDropdown } from '../ContentDropdown';
import { Button } from '@gx-design/button';
import clsx from 'clsx';
// eslint-disable-next-line you-dont-need-lodash-underscore/get
import { get } from 'lodash';

export type DropdownRangeInputOption = {
    label: string;
    value: string;
};

export type RangeMode = 'min' | 'max';

export type RangeFilter = {
    min: string;
    max: string;
};

export type DropdownRangeInputProps = {
    minInputName: string;
    maxInputName: string;
    options: DropdownRangeInputOption[];
    placeholder: string;
    placeholderMin?: string;
    placeholderMax?: string;
    submitLabel: string;
    selected?: boolean;
    formatter?: (range: RangeFilter) => string;
    icon?: IconProps['name'];
    variant?: 'chip' | 'default';
    buttonClassName?: string;
};

export const DropdownRangeInput: FC<DropdownRangeInputProps> = ({
    minInputName,
    maxInputName,
    options,
    placeholder,
    placeholderMin,
    placeholderMax,
    submitLabel,
    selected,
    formatter,
    icon,
    variant = 'default',
    buttonClassName = '',
}) => {
    const { values, setFieldValue, submitForm } =
        useFormikContext<Record<string, any>>();

    const minInputRef = useRef<HTMLInputElement>(null);
    const maxInputRef = useRef<HTMLInputElement>(null);

    const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
    const [mode, setMode] = useState<RangeMode>('min');

    const getFilteredOptions = useMemo(
        () =>
            filterRangeOptions({
                options,
                minValue: get(values, minInputName),
                maxValue: get(values, maxInputName),
                mode,
            }),
        [options, values, minInputName, maxInputName, mode]
    );

    const getButtonContent = useMemo(
        () =>
            buttonContent({
                minValue: get(values, minInputName),
                maxValue: get(values, maxInputName),
                placeholder,
                formatter,
            }),
        [values, minInputName, maxInputName, placeholder, formatter]
    );

    const checkAndFixRange = () =>
        checkAndSwitchValues({
            setFieldValue,
            minValue: get(values, minInputName),
            maxValue: get(values, maxInputName),
            minInputName,
            maxInputName,
        });

    const onSubmit = () => {
        checkAndFixRange();
        setMode('min');
        submitForm();
    };

    useEffect(() => {
        if (isDropdownOpen && mode === 'min' && minInputRef?.current) {
            minInputRef.current.focus({ preventScroll: true });
        }
        if (isDropdownOpen && mode === 'max' && maxInputRef?.current) {
            maxInputRef.current.focus();
        }
    }, [mode, minInputRef, maxInputRef, isDropdownOpen]);

    return (
        <ContentDropdownContext.Provider
            value={{
                isOpen: isDropdownOpen,
                setIsOpen: setIsDropdownOpen,
            }}
        >
            <ContentDropdown
                renderAction={({ ref }) => (
                    <Button
                        ref={ref}
                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                        variant={variant}
                        className={clsx(buttonClassName, {
                            'is-open': isDropdownOpen,
                            'is-selected': selected,
                        })}
                        type="button"
                    >
                        <Icon
                            className="gx-range-inputControl__icon"
                            name={icon as IconProps['name']}
                        />
                        <span>{getButtonContent}</span>
                        <Icon
                            className="gx-range-inputControl__caret"
                            name="arrow-down"
                        />
                    </Button>
                )}
                onClickOutside={onSubmit}
            >
                <div className="gx-range-input__inputContainer">
                    <ContentDropdown.GxFkNumberInput
                        ref={minInputRef}
                        name={minInputName}
                        placeholder={placeholderMin}
                        onFocus={() => {
                            setMode('min');
                        }}
                        onBlur={checkAndFixRange}
                    />
                    <ContentDropdown.GxFkNumberInput
                        ref={maxInputRef}
                        name={maxInputName}
                        placeholder={placeholderMax}
                        onFocus={() => {
                            setMode('max');
                            submitForm();
                        }}
                        onBlur={checkAndFixRange}
                    />
                </div>
                <ContentDropdown.RangeOptionsList
                    options={getFilteredOptions}
                    mode={mode}
                    minInputName={minInputName}
                    maxInputName={maxInputName}
                    setMode={setMode}
                    onSubmit={onSubmit}
                />
                <ContentDropdown.Footer
                    onSubmit={onSubmit}
                    submitLabel={submitLabel}
                />
            </ContentDropdown>
        </ContentDropdownContext.Provider>
    );
};
