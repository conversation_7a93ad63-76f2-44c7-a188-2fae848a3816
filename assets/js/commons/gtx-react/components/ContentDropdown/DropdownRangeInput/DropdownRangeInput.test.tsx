import {
    render,
    screen,
    waitFor,
    within,
} from '#tests/react/testing-library-enhanced';
import { describe, it, expect, vi } from 'vitest';
import { DropdownRangeInput } from './DropdownRangeInput';
import { Formik } from 'formik';
import { ComponentProps } from 'react';
import { generatePriceOptions } from './utils';
import { DEFAULT_PRICE_OPTIONS } from './constants';

describe('generatePriceOptions', () => {
    it('should generate correct price options with fixed step', () => {
        const options = generatePriceOptions({
            min: 100000,
            max: 300000,
            step: 50000,
        });

        expect(options).toHaveLength(5); // 100k, 150k, 200k, 250k, 300k
        expect(options[0]).toEqual({
            label: '100.000 €',
            value: '100000',
        });
        expect(options[2]).toEqual({
            label: '200.000 €',
            value: '200000',
        });
        expect(options[4]).toEqual({
            label: '300.000 €',
            value: '300000',
        });
    });

    it('should handle custom currency symbol', () => {
        const options = generatePriceOptions({
            min: 1000,
            max: 3000,
            step: 1000,
            currency: '$',
        });

        expect(options).toHaveLength(3);
        expect(options[0]).toEqual({
            label: '1.000 $',
            value: '1000',
        });
        expect(options[2]).toEqual({
            label: '3.000 $',
            value: '3000',
        });
    });

    it('should return empty array for invalid inputs', () => {
        const negativeMin = generatePriceOptions({
            min: -100,
            max: 100,
            step: 50,
        });
        expect(negativeMin).toEqual([]);

        const minGreaterThanMax = generatePriceOptions({
            min: 200,
            max: 100,
            step: 50,
        });
        expect(minGreaterThanMax).toEqual([]);

        const zeroOrNegativeStep = generatePriceOptions({
            min: 100,
            max: 300,
            step: 0,
        });
        expect(zeroOrNegativeStep).toEqual([]);
    });
    it('should handle edge cases with min equal to max', () => {
        const options = generatePriceOptions({
            min: 1000,
            max: 1000,
            step: 500,
        });

        // Function returns empty array when max <= min
        expect(options).toEqual([]);
    });
    it('should correctly handle precise values', () => {
        const options = generatePriceOptions({
            min: 10000,
            max: 15000,
            step: 2500,
        });

        expect(options).toHaveLength(3); // 10000, 12500, 15000
        expect(options.map((opt) => opt.value)).toEqual([
            '10000',
            '12500',
            '15000',
        ]);
        expect(options.map((opt) => opt.label)).toEqual([
            '10.000 €',
            '12.500 €',
            '15.000 €',
        ]);
    });
});

const renderDropdown = (
    props?: Omit<
        ComponentProps<typeof DropdownRangeInput>,
        | 'minInputName'
        | 'maxInputName'
        | 'options'
        | 'placeholder'
        | 'submitLabel'
        | 'placeholderMin'
        | 'placeholderMax'
    > & {
        onSubmit?: () => void;
    }
) => {
    return render(
        <DropdownRangeInput
            {...props}
            minInputName="min"
            maxInputName="max"
            options={DEFAULT_PRICE_OPTIONS}
            placeholder="Select price range"
            submitLabel="Submit"
            placeholderMin="From"
            placeholderMax="To"
        />,
        {
            wrapper({ children }) {
                return (
                    <>
                        <div data-testid="outside">Outside Element</div>
                        <Formik
                            initialValues={{ min: '', max: '' }}
                            onSubmit={props?.onSubmit || (() => {})}
                        >
                            {children}
                        </Formik>
                    </>
                );
            },
        }
    );
};

describe('DropdownRangeInput', () => {
    it('DropdownRangeInput - renders min/max inputs / RangeOptions / Footer button', async () => {
        const { user } = renderDropdown();

        const dropdown = screen.getByRole('button', {
            name: 'Select price range',
        });
        expect(dropdown).toBeInTheDocument();

        await user.click(dropdown);

        expect(screen.getByPlaceholderText('From')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('To')).toBeInTheDocument();
        expect(screen.getByText('Submit')).toBeInTheDocument();
        expect(screen.getByTestId('options-list')).toBeInTheDocument();
    });

    it('DropdownRangeInput - should handle min/max changes', async () => {
        const { user } = renderDropdown();

        const dropdown = screen.getByRole('button', {
            name: 'Select price range',
        });

        await user.click(dropdown);

        // Implement test logic here
        const minInput = screen.getByPlaceholderText('From');
        const maxInput = screen.getByPlaceholderText('To');

        expect(minInput).toBeInTheDocument();
        expect(maxInput).toBeInTheDocument();

        await user.type(minInput, '1000');
        expect(minInput).toHaveValue('1.000');

        await user.click(maxInput);

        await user.type(maxInput, '5000');
        expect(maxInput).toHaveValue('5.000');
    });

    it('DropdownRangeInput - should call onSubmit when focusing max input', async () => {
        const onSubmit = vi.fn();
        const { user } = renderDropdown({
            onSubmit,
        });

        const dropdown = screen.getByRole('button', {
            name: 'Select price range',
        });

        await user.click(dropdown);

        const maxInput = screen.getByPlaceholderText('To');
        expect(maxInput).toBeInTheDocument();

        await user.click(maxInput);

        expect(onSubmit).toHaveBeenCalled();
    });

    it('DropdownRangeInput - should filter options based on min/max values', async () => {
        const onSubmit = vi.fn();
        const { user } = renderDropdown({
            onSubmit,
        });

        const dropdown = screen.getByRole('button', {
            name: 'Select price range',
        });

        await user.click(dropdown);

        const minInput = screen.getByPlaceholderText('From');
        const maxInput = screen.getByPlaceholderText('To');
        expect(minInput).toBeInTheDocument();
        expect(maxInput).toBeInTheDocument();

        // Simulate user input for min and max values
        await user.type(minInput, '145000');

        expect(minInput).toHaveValue('145.000');

        await user.click(maxInput);

        expect(onSubmit).toHaveBeenCalled();

        const list = await screen.findByTestId('options-list');
        const options = await within(list).findAllByTestId('range-option');
        expect(options).toHaveLength(34); // Assuming there are 34 options <= 145000
    });
    it('DropdownRangeInput - should switch min/max when onBluring the input if the values of min > max', async () => {
        const onSubmit = vi.fn();
        const { user } = renderDropdown({
            onSubmit,
        });

        const dropdown = screen.getByRole('button', {
            name: 'Select price range',
        });
        await user.click(dropdown);
        const minInput = screen.getByPlaceholderText('From');
        const maxInput = screen.getByPlaceholderText('To');
        expect(minInput).toBeInTheDocument();
        expect(maxInput).toBeInTheDocument();

        // Simulate user input for min and max values
        await user.type(minInput, '5000');
        expect(minInput).toHaveValue('5.000');
        await user.type(maxInput, '2000');
        expect(maxInput).toHaveValue('2.000');

        // Simulate blur on max input
        await user.click(minInput);

        expect(minInput).toHaveValue('2.000');
        expect(maxInput).toHaveValue('5.000');
    });
    it('DropdownRangeInput - should submit when min has a value and then max value is selected from range list', async () => {
        const onSubmit = vi.fn();

        const { user } = renderDropdown({
            onSubmit,
        });

        const dropdown = screen.getByRole('button', {
            name: 'Select price range',
        });
        expect(dropdown).toBeInTheDocument();

        await user.click(dropdown);

        const minInput = screen.getByPlaceholderText('From');
        const maxInput = screen.getByPlaceholderText('To');
        expect(minInput).toBeInTheDocument();
        expect(maxInput).toBeInTheDocument();

        // Simulate user input for min value
        await user.type(minInput, '1000');
        expect(minInput).toHaveValue('1.000');

        // Switch to max input
        await user.click(maxInput);

        // Select a max value from the options list
        const list = await screen.findByTestId('options-list');
        const options = await within(list).findAllByTestId('range-option');

        expect(options).toHaveLength(DEFAULT_PRICE_OPTIONS.length);

        const maxOption = options[options.length - 1];

        expect(maxOption).toBeInTheDocument();

        await user.click(maxOption!);

        await waitFor(() => {
            expect(onSubmit).toHaveBeenCalled();
        });

        expect(
            screen.queryByRole('button', { name: 'Submit' })
        ).not.toBeInTheDocument();
    });

    it('DropdownRangeInput - should submit and close dropdown when entering manualy the inputs and confirming with the button', async () => {
        const onSubmit = vi.fn();

        const { user } = renderDropdown({
            onSubmit,
        });

        const dropdown = screen.getByRole('button', {
            name: 'Select price range',
        });
        expect(dropdown).toBeInTheDocument();

        await user.click(dropdown);

        const minInput = screen.getByPlaceholderText('From');
        const maxInput = screen.getByPlaceholderText('To');
        expect(minInput).toBeInTheDocument();
        expect(maxInput).toBeInTheDocument();

        // Simulate user input for min value
        await user.type(minInput, '1000');
        expect(minInput).toHaveValue('1.000');

        // Switch to max input
        await user.click(maxInput);

        await user.type(maxInput, '5000');
        expect(maxInput).toHaveValue('5.000');

        // Click the submit button
        const submitButton = screen.getByTestId('dropdown-submit-button');

        await user.click(submitButton);

        await waitFor(() => {
            expect(onSubmit).toHaveBeenCalled();
        });

        expect(
            screen.queryByRole('button', { name: 'Submit' })
        ).not.toBeInTheDocument();
    });

    it('DropdownRangeInput - should display formatted min/max values in button after submit', async () => {
        const onSubmit = vi.fn();
        const { user } = renderDropdown({
            onSubmit,
            formatter: ({ min, max }) => `€${min} - €${max}`,
        });

        const dropdown = screen.getByRole('button', {
            name: 'Select price range',
        });
        await user.click(dropdown);

        const minInput = screen.getByPlaceholderText('From');
        const maxInput = screen.getByPlaceholderText('To');

        await user.type(minInput, '1000');
        await user.type(maxInput, '5000');

        const submitButton = screen.getByTestId('dropdown-submit-button');
        await user.click(submitButton);

        await waitFor(() => {
            expect(
                screen.getByRole('button', { name: '€1000 - €5000' })
            ).toBeInTheDocument();
        });
    });

    it('DropdownRangeInput - should handle empty min value with max value', async () => {
        const onSubmit = vi.fn();
        const { user } = renderDropdown({ onSubmit });

        const dropdown = screen.getByRole('button', {
            name: 'Select price range',
        });
        await user.click(dropdown);

        const maxInput = screen.getByPlaceholderText('To');
        await user.type(maxInput, '5000');

        const submitButton = screen.getByTestId('dropdown-submit-button');
        await user.click(submitButton);

        await waitFor(() => {
            const buttonText = screen.getByRole('button', {
                name: 'Select price range: - 5000',
            });
            expect(buttonText).toBeInTheDocument();
        });
    });

    it('DropdownRangeInput - should handle empty max value with min value', async () => {
        const onSubmit = vi.fn();
        const { user } = renderDropdown({ onSubmit });

        const dropdown = screen.getByRole('button', {
            name: 'Select price range',
        });
        await user.click(dropdown);

        const minInput = screen.getByPlaceholderText('From');
        await user.type(minInput, '1000');

        const submitButton = screen.getByTestId('dropdown-submit-button');
        await user.click(submitButton);

        await waitFor(() => {
            const buttonText = screen.getByRole('button', {
                name: 'Select price range: 1000 -',
            });

            expect(buttonText).toBeInTheDocument();
        });
    });

    it('DropdownRangeInput - should submit values when clicking outside', async () => {
        const onSubmit = vi.fn();
        const { user } = renderDropdown({ onSubmit });

        const dropdown = screen.getByRole('button', {
            name: 'Select price range',
        });
        await user.click(dropdown);

        const minInput = screen.getByPlaceholderText('From');
        await user.type(minInput, '1000');

        const outsideElement = screen.getByTestId('outside');
        await user.click(outsideElement);

        await waitFor(() => {
            expect(
                screen.queryByPlaceholderText('From')
            ).not.toBeInTheDocument();
        });

        expect(onSubmit).toHaveBeenCalled();

        // Reopen and check if input is filled
        await user.click(dropdown);
        expect(screen.getByPlaceholderText('From')).toHaveValue('1.000');
    });
});
