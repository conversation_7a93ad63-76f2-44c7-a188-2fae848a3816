import { FC, useEffect, useRef } from 'react';
import { useFormikContext } from 'formik';
import classNames from 'classnames';
import { ensureVisible, Align } from '@pepita/dom-ensure-visible';
import { DropdownRangeInputOption, RangeMode } from './DropdownRangeInput';
import { useContentDropdown } from '../useContentDropdown';

type RangeOptionsListProps = {
    options: DropdownRangeInputOption[];
    mode: RangeMode;
    minInputName: string;
    maxInputName: string;
    setMode: (mode: RangeMode) => void;
    onSubmit: () => void;
};

export const RangeOptionsList: FC<RangeOptionsListProps> = ({
    options,
    mode,
    minInputName,
    maxInputName,
    setMode,
    onSubmit,
}) => {
    const { setFieldValue, values } = useFormikContext<Record<string, any>>();
    const { setIsOpen } = useContentDropdown();

    const listRef = useRef<HTMLDivElement>(null);
    const selectedElementRef = useRef<HTMLLIElement>(null);

    let currentValue = values[mode === 'min' ? minInputName : maxInputName];

    // need to check and fix field order because setFieldValue is not async, so checks from 'checkAndSwitchValues' that happen right before dropdown closing can fail.
    const onOptionClick = (value: DropdownRangeInputOption['value']) => () => {
        const numericCurrentValue = parseInt(value);
        let numericOtherValue: number;
        if (mode === 'min') {
            numericOtherValue = parseInt(values[maxInputName]);

            if (
                !Number.isNaN(numericCurrentValue) &&
                !Number.isNaN(numericOtherValue) &&
                numericCurrentValue > numericOtherValue
            ) {
                setFieldValue(minInputName, values[maxInputName]);
                setFieldValue(maxInputName, value);
            } else {
                setFieldValue(minInputName, value);
            }
            setMode('max');
        } else {
            numericOtherValue = parseInt(values[minInputName]);

            if (
                !Number.isNaN(numericCurrentValue) &&
                !Number.isNaN(numericOtherValue) &&
                numericCurrentValue < numericOtherValue
            ) {
                setFieldValue(maxInputName, values[minInputName]);
                setFieldValue(minInputName, value);
            } else {
                setFieldValue(maxInputName, value);
            }
            setIsOpen(false);
            onSubmit();
            setMode('min');
        }
    };

    useEffect(() => {
        if (selectedElementRef?.current && listRef?.current) {
            ensureVisible(selectedElementRef.current, listRef.current, {
                force: true,
                align: Align.Center,
            });
        }
    }, [mode, currentValue]);

    return (
        <div
            ref={listRef}
            className={classNames([
                'gx-range-input__list',
                {
                    'gx-range-input__list--right': mode === 'max',
                },
            ])}
        >
            <ul data-testid="options-list">
                {options.map(({ label, value }) => (
                    <li
                        data-testid="range-option"
                        className={classNames([
                            'gx-range-input__listItem',
                            {
                                'is-selected': currentValue === value,
                            },
                        ])}
                        key={`range-option-${label}-${value}`}
                        onClick={onOptionClick(value)}
                        ref={value === currentValue ? selectedElementRef : null}
                    >
                        {label}
                    </li>
                ))}
            </ul>
        </div>
    );
};
