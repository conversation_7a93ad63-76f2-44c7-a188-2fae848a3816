import { parseIntNumberWithPoints } from 'gtx-react/utils/parseIntWithPoints';
import { RangeMode } from './DropdownRangeInput';
import { trans } from '@pepita-i18n/babelfish';

export const checkAndSwitchValues = ({
    setFieldValue,
    minValue,
    maxValue,
    minInputName,
    maxInputName,
}: {
    setFieldValue: (field: string, value: any, shouldValidate?: boolean) => void;
    minValue: string;
    maxValue: string;
    minInputName: string;
    maxInputName: string;
}) => {
    const numericMin = parseIntNumberWithPoints(minValue);
    const numericMax = parseIntNumberWithPoints(maxValue);

    if (!Number.isNaN(numericMin) && !Number.isNaN(numericMax) && numericMin > numericMax) {
        setFieldValue(maxInputName, minValue);
        setFieldValue(minInputName, maxValue);
    }
};

export const filterRangeOptions = ({
    options,
    minValue,
    maxValue,
    mode,
}: {
    options: { label: string; value: string }[];
    minValue?: string;
    maxValue?: string;
    mode: RangeMode;
}) => {
    return options.filter((option) => {
        const numericOptionValue = Number(option.value);
        if (option.value === '') {
            return true; // "Indifferent" option should always be included
        }

        const parsedMinValue = minValue ? parseIntNumberWithPoints(minValue) : null;
        const parsedMaxValue = maxValue ? parseIntNumberWithPoints(maxValue) : null;

        if (mode === 'min' && parsedMaxValue !== null) {
            return numericOptionValue <= parsedMaxValue;
        }

        if (mode === 'max' && parsedMinValue !== null) {
            return numericOptionValue >= parsedMinValue;
        }

        return true;
    });
};

export const buttonContent = ({
    minValue,
    maxValue,
    placeholder,
    formatter,
}: {
    minValue: string;
    maxValue: string;
    placeholder: string;
    formatter?: (range: { min: string; max: string }) => string;
}) => {
    if (!minValue && !maxValue) {
        return placeholder;
    }

    if (typeof formatter === 'function') {
        return formatter({ min: minValue, max: maxValue });
    }

    return `${placeholder}: ${minValue} - ${maxValue}`;
};

type RangeFormatterMode = 'minOnly' | 'maxOnly' | 'range' | 'equal';

export type RangeFilter = {
    min: string;
    max: string;
};

export const priceRangeFormatter = ({ min, max }: RangeFilter): string => {
    let mode: RangeFormatterMode;
    let prefix = '';
    let suffix = '';

    if (min && !max) {
        mode = 'minOnly';
    } else if (!min && max) {
        mode = 'maxOnly';
    } else if (min === max) {
        mode = 'equal';
    } else {
        mode = 'range';
    }

    switch (mode) {
        case 'minOnly':
            prefix = `${trans('label.starting_from').toLowerCase()} `;
            break;
        case 'maxOnly':
            prefix = `${trans('label.until').toLowerCase()} `;
            break;
        case 'range':
            prefix = `${trans('label.from')} `;
            suffix = ` ${trans('label.to')} `;
            break;
        default:
            break;
    }

    const numericMin = min ? parseIntNumberWithPoints(min) : 0;
    const numericMax = max ? parseIntNumberWithPoints(max) : 0;

    const formattedMin = numericMin ? numToCompactNotation(numericMin) : '';
    const formattedMax = numericMax ? numToCompactNotation(numericMax) : '';

    return `${prefix}${formattedMin}${suffix}${mode !== 'equal' ? formattedMax : ''}`;
};

const numToCompactNotation = (value: number): string => {
    const COUNT_FORMATS = [
        {
            // 0 - 999
            letter: '',
            limit: 1e3,
        },
        {
            // 1,000 - 999,999
            letter: 'K',
            limit: 1e6,
        },
        {
            // 1,000,000 - 999,999,999
            letter: 'M',
            limit: 1e9,
        },
        {
            // 1,000,000,000 - 999,999,999,999
            letter: 'B',
            limit: 1e12,
        },
        {
            // 1,000,000,000,000 - 999,999,999,999,999
            letter: 'T',
            limit: 1e15,
        },
    ];

    const format = COUNT_FORMATS.find((format) => value < format.limit);
    if (!format) {
        return value.toString();
    }

    value = (1000 * value) / format.limit;
    value = Math.round(value * 10) / 10; // keep one decimal number, only if needed

    return value + format.letter;
};

/**
 * Formats a price number with thousands separator (dot format for European style)
 * @param price The price number to format
 * @returns Formatted price string with dots as thousands separators
 */
const formatPrice = (price: number): string => {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
};

/**
 * Generates an array of price options for a dropdown, formatted with currency.
 * @param {Object} params - Parameters for generating price options.
 * @param {number} params.min - Minimum price value.
 * @param {number} params.max - Maximum price value.
 * @param {number} params.step - Step value for generating price increments.
 * @param {string} [params.currency='€'] - Currency symbol to append to
 * @returns {Array<{ label: string; value: string }>} - Array of price options, each with a label and value.
 */
export const generatePriceOptions = ({
    min,
    max,
    step,
    currency = '€',
}: {
    min: number;
    max: number;
    step: number;
    currency?: string;
}): Array<{ label: string; value: string }> => {
    if (min < 0 || max <= min || step <= 0) {
        return [];
    }

    const options: Array<{ label: string; value: string }> = [];

    for (let price = min; price <= max; price += step) {
        options.push({
            label: `${formatPrice(price)} ${currency}`,
            value: price.toString(),
        });
    }

    return options;
};
