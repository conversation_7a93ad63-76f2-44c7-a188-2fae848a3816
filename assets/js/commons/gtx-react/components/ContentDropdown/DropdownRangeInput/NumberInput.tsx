import React, { forwardRef } from 'react';
import { useField } from 'formik';
import { NumericFormat } from 'react-number-format';

export type GxFkNumberInputProps =
    React.InputHTMLAttributes<HTMLInputElement> & {
        name: string;
        placeholder?: string;
    };

export const GxFkNumberInput = forwardRef<
    HTMLInputElement,
    GxFkNumberInputProps
>((props, ref) => {
    const [fieldProps] = useField(props.name);
    const { onChange, ...restFieldProps } = fieldProps;

    return (
        <NumberInput
            {...restFieldProps}
            {...props}
            onChange={(value) => {
                onChange({ target: { value, name: props.name } });
            }}
            ref={ref}
        />
    );
});

export type NumberInputProps = React.InputHTMLAttributes<HTMLInputElement> & {
    name: string;
    type?: string;
    allowNegative?: boolean;
    decimalSeparator?: string;
    thousandSeparator?: string;
    decimalScale?: number;
    thousandsGroupStyle?: 'thousand' | 'lakh' | 'wan';
    fixedDecimalScale?: boolean;
    value?: string | number;
    placeholder?: string;
    onChange?: (value: string) => void;
};

export const NumberInput = forwardRef<HTMLInputElement, NumberInputProps>(
    (
        {
            name,
            type = 'text',
            allowNegative = false,
            decimalSeparator = ',',
            thousandSeparator = '.',
            decimalScale = 0,
            thousandsGroupStyle = 'thousand',
            fixedDecimalScale = true,
            placeholder,
            value,
            onChange,
            ...props
        },
        ref
    ) => {
        return (
            <NumericFormat
                {...props}
                className="gx-input"
                name={name}
                getInputRef={ref}
                type="tel"
                defaultValue={undefined}
                allowNegative={allowNegative}
                decimalSeparator={decimalSeparator}
                thousandSeparator={thousandSeparator}
                decimalScale={decimalScale}
                thousandsGroupStyle={thousandsGroupStyle}
                placeholder={placeholder}
                fixedDecimalScale={fixedDecimalScale}
                value={value}
                onValueChange={(values) => {
                    if (onChange) {
                        onChange(values.value);
                    }
                }}
            />
        );
    }
);
