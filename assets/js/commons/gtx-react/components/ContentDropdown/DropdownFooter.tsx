import React, { <PERSON> } from 'react';
import { Button } from '@gx-design/button';
import { useContentDropdown } from './useContentDropdown';

type DropdownFooterProps = {
    onSubmit: () => void;
    submitLabel: string;
};
export const DropdownFooter: FC<DropdownFooterProps> = ({
    onSubmit,
    submitLabel,
}) => {
    const { setIsOpen } = useContentDropdown();

    const onSubmitClick = () => {
        setIsOpen(false);
        onSubmit();
    };

    return (
        <div className="gx-range-input__footer">
            <Button
                variant="accent"
                onClick={onSubmitClick}
                size="fullWidth"
                data-testid="dropdown-submit-button"
            >
                {submitLabel}
            </Button>
        </div>
    );
};
