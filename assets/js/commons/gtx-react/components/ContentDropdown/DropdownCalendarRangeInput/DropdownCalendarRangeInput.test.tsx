import {
    fireEvent,
    render,
    screen,
} from '#tests/react/testing-library-enhanced';
import { Formik } from 'formik';
import { describe, expect, it, vi } from 'vitest';
import {
    DropdownCalendarRangeInput,
    DropdownCalendarRangeInputProps,
} from './DropdownCalendarRangeInput';
import { createValidationSchema } from './utils';

describe('DropdownCalendarRangeInput', () => {
    const defaultProps: DropdownCalendarRangeInputProps = {
        startName: 'startDate',
        endName: 'endDate',
        startPlaceholder: 'Start date',
        endPlaceholder: 'End date',
        submitLabel: 'Apply',
        buttonPlaceholder: 'Select dates',
    };

    const renderComponent = (
        props: Partial<DropdownCalendarRangeInputProps> = {}
    ) => {
        const onSubmitMock = vi.fn();

        return {
            onSubmitMock,
            ...render(
                <DropdownCalendarRangeInput {...defaultProps} {...props} />,
                {
                    wrapper: ({ children }) => (
                        <Formik
                            enableReinitialize
                            initialValues={{
                                startDate: '01/01/2023',
                                endDate: '01/02/2023',
                            }}
                            validationSchema={createValidationSchema({
                                from: 'startDate',
                                to: 'endDate',
                            })}
                            onSubmit={onSubmitMock}
                        >
                            {children}
                        </Formik>
                    ),
                }
            ),
        };
    };

    it('renders without crashing', () => {
        renderComponent();
        expect(
            screen.getByText('Select dates: 01/01/2023 - 01/02/2023')
        ).toBeInTheDocument();
    });

    it('Case 1: selecting range dates', async () => {
        const { user, onSubmitMock } = renderComponent();
        const button = screen.getByText(
            'Select dates: 01/01/2023 - 01/02/2023'
        );
        await user.click(button);
        const startDateInput = screen.getByPlaceholderText('Start date');
        const endDateInput = screen.getByPlaceholderText('End date');

        await user.click(screen.getByLabelText('2 gennaio 2023'));

        expect(onSubmitMock).toHaveBeenCalledWith(
            expect.objectContaining({
                startDate: '02/01/2023',
                endDate: '',
            }),
            expect.anything()
        );

        expect(startDateInput).toHaveValue('02/01/2023');
        expect(endDateInput).toHaveValue('');

        await user.click(screen.getByLabelText('3 gennaio 2023'));
        // dropdown should close
        expect(startDateInput).not.toBeInTheDocument();
        expect(endDateInput).not.toBeInTheDocument();

        expect(onSubmitMock).toHaveBeenCalledWith(
            expect.objectContaining({
                startDate: '02/01/2023',
                endDate: '03/01/2023',
            }),
            expect.anything()
        );
    });

    // This test is not working, I don't know why, we need to investigate
    it('Case 2: selecting range dates from input', async () => {
        const { user, onSubmitMock, container } = renderComponent();
        const button = screen.getByText(
            'Select dates: 01/01/2023 - 01/02/2023'
        );
        await user.click(button);

        const startDateInput = screen.getByTestId('start-input');
        const endDateInput = screen.getByTestId('end-input');

        fireEvent.change(startDateInput, { target: { value: '03/01/2023' } });
        fireEvent.change(endDateInput, { target: { value: '04/01/2023' } });

        await user.click(container);

        expect(onSubmitMock).toHaveBeenCalledWith(
            expect.objectContaining({
                startDate: '03/01/2023',
                endDate: '04/01/2023',
            }),
            expect.anything()
        );
    });
});
