// File: assets/js/commons/gtx-react/components/ContentDropdown/DropdownCalendarRangeInput/CalendarRangeInput.test.tsx

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

import CalendarRangeInput from './CalendarRangeInput';
import { render, screen } from '#tests/react/testing-library-enhanced';

describe('CalendarRangeInput', () => {
    const locale = 'it-IT';

    it('calls onSelectStart and onChange when a start date is selected', async () => {
        const onSelectStart = vi.fn();
        const onSelectEnd = vi.fn();
        const onChange = vi.fn();
        const date = new Date('2025-07-01T00:00:00.000Z');

        vi.useFakeTimers({ toFake: ['Date'] });
        vi.setSystemTime(date);

        const { user } = render(
            <CalendarRangeInput
                startValue={null}
                endValue={null}
                onSelectStart={onSelectStart}
                onSelectEnd={onSelectEnd}
                onChange={onChange}
                locale={locale}
            />
        );

        // Simulate selecting a start date
        // react-calendar renders days as buttons with aria-labels
        const dayButton = screen.getByLabelText('1 luglio 2025'); // e.g., "1 January 2023"
        await user.click(dayButton);

        expect(onSelectStart).toHaveBeenCalled();
        expect(onChange).toHaveBeenCalled();
        expect(onSelectEnd).not.toHaveBeenCalled();
    });

    it('calls onSelectEnd and onChange when an end date is selected', async () => {
        const onSelectStart = vi.fn();
        const onSelectEnd = vi.fn();
        const onChange = vi.fn();

        const { user } = render(
            <CalendarRangeInput
                startValue={new Date(2023, 0, 1)}
                endValue={null}
                onSelectStart={onSelectStart}
                onSelectEnd={onSelectEnd}
                onChange={onChange}
                locale={locale}
            />
        );

        // Simulate selecting an end date
        await user.click(screen.getByLabelText('2 gennaio 2023'));
        await user.click(screen.getByLabelText('3 gennaio 2023'));

        expect(onSelectEnd).toHaveBeenCalled();
        expect(onChange).toHaveBeenCalledWith(['02/01/2023', '03/01/2023']);
    });

    it('swaps start and end if end is before start', async () => {
        const onSelectStart = vi.fn();
        const onSelectEnd = vi.fn();
        const onChange = vi.fn();

        const { user } = render(
            <CalendarRangeInput
                startValue={new Date(2023, 0, 10)}
                endValue={null}
                onSelectStart={onSelectStart}
                onSelectEnd={onSelectEnd}
                onChange={onChange}
                locale={locale}
            />
        );

        // Simulate selecting an end date before the start date
        await user.click(screen.getByLabelText('5 gennaio 2023'));
        await user.click(screen.getByLabelText('3 gennaio 2023'));

        // onChange should be called with swapped values
        expect(onChange).toHaveBeenCalledWith(
            expect.arrayContaining(['03/01/2023', '05/01/2023'])
        );
    });
});
