import { isAfter, isValid } from 'date-fns';
import { parseDate } from 'gtx-react/components/DatePicker';
import * as yup from 'yup';

/**
 * create validation schema for date range
 * @returns
 */
export const createValidationSchema = ({ from, to }: { from: string; to: string }) =>
    yup.object().shape({
        [from]: yup
            .string()
            .test('date-format', 'La data deve essere nel formato GG/MM/AAAA', (value) => {
                if (!value) {
                    return true;
                } // opzionale
                const parsedDate = parseDate(value);
                return isValid(parsedDate);
            })
            .nullable(),

        [to]: yup
            .string()
            .test('date-format', 'La data deve essere nel formato GG/MM/AAAA', (value) => {
                if (!value) {
                    return true;
                } // opzionale
                const parsedDate = parseDate(value);
                return isValid(parsedDate);
            })
            .nullable(),
    });

export const buttonContent = ({
    minValue,
    maxValue,
    placeholder,
    formatter,
}: {
    minValue: string;
    maxValue: string;
    placeholder: string;
    formatter?: (range: { start: string; end: string }) => string;
}) => {
    if (typeof formatter === 'function') {
        return formatter({ start: minValue, end: maxValue });
    }

    if (!minValue && !maxValue) {
        return placeholder;
    }

    return `${placeholder}: ${minValue} - ${maxValue}`;
};

export const checkAndSwitchValues = ({
    setFieldValue,
    startValue,
    endValue,
    startName,
    endName,
}: {
    setFieldValue: (field: string, value: any, shouldValidate?: boolean) => void;
    startValue: string;
    endValue: string;
    startName: string;
    endName: string;
}) => {
    const startDate = parseDate(startValue);
    const endDate = parseDate(endValue);

    if (startDate && endDate && isAfter(startDate, endDate)) {
        setFieldValue(endName, startValue);
        setFieldValue(startName, endValue);
    }
};

export const isInvalidDate = ({ value }: { value: string }) => {
    const date = parseDate(value);
    return !isValid(date);
};
