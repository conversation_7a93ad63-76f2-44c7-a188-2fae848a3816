import { IconProps } from '@gx-design/icon';
import { isValid } from 'date-fns';
import { useFormikContext } from 'formik';
import { parseDate } from 'gtx-react/components/DatePicker';
import { PatternInput } from 'gtx-react/components/DatePicker/PatternFormikInput';
import { getAgencyLanguage } from 'lib/languages';
import { FC, useMemo, useRef, useState } from 'react';
import { ContentDropdown } from '../ContentDropdown';
import { ContentDropdownContext } from '../useContentDropdown';
import CalendarRangeInput from './CalendarRangeInput';
import { buttonContent, checkAndSwitchValues, isInvalidDate } from './utils';
import { Button } from '@gx-design/button';
import clsx from 'clsx';
import { Icon } from '@gx-design/icon';

export type RangeFilter = {
    start: string;
    end: string;
};

export type DropdownCalendarRangeInputProps = {
    startName: string;
    endName: string;
    startPlaceholder?: string;
    endPlaceholder?: string;
    submitLabel: string;
    selected?: boolean;
    formatter?: (range: RangeFilter) => string;
    icon?: IconProps['name'];
    buttonPlaceholder: string;
    locale?: string;
    disabled?: boolean;
    position?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
    variant?: 'chip' | 'default';
    buttonClassName?: string;
};

const getValidDate = (date: Date | null) => {
    if (isValid(date)) {
        return date;
    }
    return null;
};

export const DropdownCalendarRangeInput: FC<
    DropdownCalendarRangeInputProps
> = ({
    locale = getAgencyLanguage(),
    startName,
    endName,
    startPlaceholder,
    endPlaceholder,
    submitLabel,
    selected,
    formatter,
    disabled,
    icon,
    buttonPlaceholder,
    variant = 'default',
    position,
    buttonClassName = '',
}) => {
    const { values, setFieldValue, submitForm, getFieldProps } =
        useFormikContext<Record<string, any>>();

    const endInputRef = useRef<HTMLInputElement>(null);
    const startInputRef = useRef<HTMLInputElement>(null);

    const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
    const [mode, setMode] = useState<'end' | 'start'>('start');

    const startValue = getValidDate(parseDate(values[startName]));
    const endValue = getValidDate(parseDate(values[endName]));

    const getButtonContent = useMemo(
        () =>
            buttonContent({
                minValue: values[startName],
                maxValue: values[endName],
                placeholder: buttonPlaceholder,
                formatter,
            }),
        [values, startName, endName, buttonPlaceholder, formatter]
    );

    const checkAndFixRange = () =>
        checkAndSwitchValues({
            setFieldValue,
            startValue: values[startName],
            endValue: values[endName],
            startName,
            endName,
        });

    const onSubmit = () => {
        checkAndFixRange();
        submitForm();
    };

    const { onBlur: startInputOnBlur, ...startInputProps } =
        getFieldProps(startName);
    const { onBlur: endInputOnBlur, ...endInputProps } = getFieldProps(endName);

    return (
        <ContentDropdownContext.Provider
            value={{
                isOpen: isDropdownOpen,
                setIsOpen: setIsDropdownOpen,
            }}
        >
            <ContentDropdown
                position={position}
                renderAction={({ ref }) => (
                    <Button
                        disabled={disabled}
                        ref={ref}
                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                        variant={variant}
                        className={clsx(buttonClassName, {
                            'is-open': isDropdownOpen,
                            'is-selected': selected,
                        })}
                        type="button"
                    >
                        <Icon
                            className="gx-range-inputControl__icon"
                            name={icon as IconProps['name']}
                        />
                        <span>{getButtonContent}</span>
                        <Icon
                            className="gx-range-inputControl__caret"
                            name="arrow-down"
                        />
                    </Button>
                )}
                onClickOutside={onSubmit}
            >
                <div className="gx-range-input__inputContainer">
                    <PatternInput
                        data-testid="start-input"
                        ref={startInputRef}
                        {...startInputProps}
                        format="##/##/####"
                        name={startName}
                        placeholder={startPlaceholder}
                        onFocus={() => setMode('start')}
                        onBlur={(e) => {
                            startInputOnBlur(e);
                            if (isInvalidDate({ value: e.target.value })) {
                                return setFieldValue(startName, '');
                            }
                            checkAndFixRange();
                        }}
                        autoFocus={!startValue}
                        type="text"
                    />

                    <PatternInput
                        data-testid="end-input"
                        ref={endInputRef}
                        {...endInputProps}
                        onFocus={() => setMode('end')}
                        format="##/##/####"
                        type="text"
                        placeholder={endPlaceholder}
                        autoFocus={!!startValue && !endValue}
                        onBlur={(e) => {
                            endInputOnBlur(e);
                            if (isInvalidDate({ value: e.target.value })) {
                                return setFieldValue(endName, '');
                            }

                            checkAndFixRange();
                        }}
                    />
                </div>
                <div className="react-calendar-range-wrapper">
                    <CalendarRangeInput
                        locale={locale}
                        endValue={endValue}
                        startValue={startValue}
                        onChange={([start, end]) => {
                            if (start && !end) {
                                if (mode === 'end') {
                                    startInputRef.current?.focus();

                                    setMode('start');
                                    setFieldValue(endName, start);
                                    setFieldValue(startName, '');
                                } else {
                                    endInputRef.current?.focus();

                                    setMode('end');
                                    setFieldValue(startName, start);
                                    setFieldValue(endName, end);
                                }
                            }

                            if (start && end) {
                                setMode('start');
                                setFieldValue(startName, start);
                                setFieldValue(endName, end);
                            }

                            submitForm();
                        }}
                        onSelectEnd={() => {
                            setIsDropdownOpen(false);
                        }}
                        onSelectStart={() => endInputRef.current?.focus()}
                    />
                </div>
            </ContentDropdown>
        </ContentDropdownContext.Provider>
    );
};
