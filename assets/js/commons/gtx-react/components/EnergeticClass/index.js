import React, { Fragment } from 'react';

export const ENERGY_TIERS = ['a4', 'a3', 'a2', 'a1', 'b', 'c', 'd', 'e', 'f', 'g'];
export const OLD_ENERGY_TIERS = ['a+', 'a', 'b', 'c', 'd', 'e', 'f', 'g'];
const ENERGY_CONSUMPTION_UNIT = 'kWh';

let getSvgStyle = () => `
    .cls-1 {
        fill: #bcbec0;
    }
    .cls-2,
    .cls-3 {
        font-size: 7.9px;
    }
    .cls-2,
    .cls-20,
    .cls-21 {
        font-family: Arial-BoldMT, Arial;
        font-weight: 700;
    }
    .cls-16,
    .cls-19,
    .cls-3 {
        font-family: ArialMT, Arial;
    }
    .cls-4 {
        letter-spacing: -0.02em;
    }
    .tier-a4 {
        fill: #1a6800;
    }
    .tier-a3 {
        fill: #006900;
    }
    .tier-a2 {
        fill: #009d00;
    }
    .tier-a1 {
        fill: #00d100;
    }
    .tier-b {
        fill: #8acf00;
    }
    .tier-c {
        fill: #e5d400;
    }
    .tier-d {
        fill: #ffbe00;
    }
    .tier-e {
        fill: #f56300;
    }
    .tier-f {
        fill: #f23100;
    }
    .tier-g {
        fill: #d10000;
    }
    .cls-15 {
        fill: #de1900;
    }
    .cls-16 {
        font-size: 6.58px;
    }
    .cls-17 {
        letter-spacing: -0.02em;
    }
    .cls-18 {
        letter-spacing: 0em;
    }
    .cls-19 {
        font-size: 6px;
    }
    .cls-20 {
        font-size: 9px;
    }
    .cls-21 {
        font-size: 23.71px;
        fill: #fff;
    }
    .old-tier-aplus {
        fill: #005200;
    }
    .old-tier-a {
        fill: #006900;
    }
    .old-tier-b {
        fill: #009d00;
    }
    .old-tier-c {
        fill: #00d100;
    }
    .old-tier-d {
        fill: #ffbe00;
    }
    .old-tier-e {
        fill: #f56300;
    }
    .old-tier-f {
        fill: #f23100;
    }
    .old-tier-g {
        fill: #d10000;
    }
`;

export let EnergeticClass = ({ old, tier, epglRenValue, epglRenUnit, epglNrenValue, epglNrenUnit, epiValue, epiUnit, ...props }) => (
    <svg viewBox="0 0 131.16 160" {...props}>
        <defs>
            <style>{getSvgStyle()}</style>
        </defs>
        {!old && epglRenValue ? (
            <g id="nuovi_dati">
                <g id="ep">
                    <text className="cls-2" transform="translate(38.86 146.33)">
                        {epglRenValue} {epglRenUnit}
                    </text>
                    <text className="cls-3" transform="translate(0.32 146.33)">
                        E<tspan className="cls-4" x="5.27" y="0">
                        P
                    </tspan>
                        <tspan x="10.4" y="0">
                            gl, ren
                        </tspan>
                    </text>
                </g>
            </g>
        ) : null}
        {old ? <APEVecchio /> : <APENuovo />}
        <ClassLabel
            tier={tier}
            old={old}
            tierClassName={`${old ? 'old-' : ''}tier-${tier === 'a+' ? 'aplus' : tier}`}
            epglNrenValue={epglNrenValue}
            epglNrenUnit={epglNrenUnit}
            epiValue={epiValue}
            epiUnit={epiUnit}
        />
    </svg>
);

const epSeasonMouth = val => {
    switch (val){
        case 1:
            return (
                <Fragment>
                    <path d="M12,24A12,12,0,1,1,24,12,12,12,0,0,1,12,24ZM12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Z" />
                    <path d="M8,12.87c-1.37,0-2.44-1.54-2.44-3.51S6.59,5.85,8,5.85s2.45,1.54,2.45,3.51S9.34,12.87,8,12.87ZM8,6.81c-.8,0-1.48,1.17-1.48,2.55S7.16,11.91,8,11.91s1.49-1.17,1.49-2.55S8.77,6.81,8,6.81Z" />
                    <path d="M16,12.87c-1.38,0-2.45-1.54-2.45-3.51S14.66,5.85,16,5.85s2.44,1.54,2.44,3.51S17.41,12.87,16,12.87Zm0-6.06c-.81,0-1.49,1.17-1.49,2.55s.68,2.55,1.49,2.55,1.48-1.17,1.48-2.55S16.84,6.81,16,6.81Z" />
                    <path d="M12,19a6.51,6.51,0,0,1-5.36-2.83l.79-.54a5.54,5.54,0,0,0,9.14,0l.79.54A6.51,6.51,0,0,1,12,19Z" />
                </Fragment>
            );
        case 2:
            return (
                <Fragment>
                    <path d="M12,24A12,12,0,1,1,24,12,12,12,0,0,1,12,24ZM12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Z" />
                    <path d="M8,12.87c-1.37,0-2.44-1.54-2.44-3.51S6.59,5.85,8,5.85s2.45,1.54,2.45,3.51S9.34,12.87,8,12.87ZM8,6.81c-.8,0-1.48,1.17-1.48,2.55S7.16,11.91,8,11.91s1.49-1.17,1.49-2.55S8.77,6.81,8,6.81Z" />
                    <path d="M16,12.87c-1.38,0-2.45-1.54-2.45-3.51S14.66,5.85,16,5.85s2.44,1.54,2.44,3.51S17.41,12.87,16,12.87Zm0-6.06c-.81,0-1.49,1.17-1.49,2.55s.68,2.55,1.49,2.55,1.48-1.17,1.48-2.55S16.84,6.81,16,6.81Z" />
                    <rect x="6.28" y="16.69" width="11.7" height="0.96" />
                </Fragment>
            );
        case 3:
            return (
                <Fragment>
                    <path d="M12,24A12,12,0,1,1,24,12,12,12,0,0,1,12,24ZM12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Z" />
                    <path d="M8,12.87c-1.37,0-2.44-1.54-2.44-3.51S6.59,5.85,8,5.85s2.45,1.54,2.45,3.51S9.34,12.87,8,12.87ZM8,6.81c-.8,0-1.48,1.17-1.48,2.55S7.16,11.91,8,11.91s1.49-1.17,1.49-2.55S8.77,6.81,8,6.81Z" />
                    <path d="M16,12.87c-1.38,0-2.45-1.54-2.45-3.51S14.66,5.85,16,5.85s2.44,1.54,2.44,3.51S17.41,12.87,16,12.87Zm0-6.06c-.81,0-1.49,1.17-1.49,2.55s.68,2.55,1.49,2.55,1.48-1.17,1.48-2.55S16.84,6.81,16,6.81Z" />
                    <path d="M16.57,18.75a5.54,5.54,0,0,0-9.14,0l-.79-.54a6.49,6.49,0,0,1,10.72,0Z" />
                </Fragment>
            );
    }
}

export let EnergeticClassFabbricato = ({winterValue, summerValue}) => (
    <div className="ep-season">
        <div className="ep-season__title">Fabbricato</div>
        <div className="ep-season__box">
            <div className="season season--winter">
                <svg
                    id="ep-winter-value"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                >
                    {epSeasonMouth(winterValue)}
                </svg>
                <div>EP invernale</div>
            </div>
            <div className="season season--summer">
                <svg
                    id="ep-summer-value"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                >
                    {epSeasonMouth(summerValue)}
                </svg>
                <div>EP estiva</div>
            </div>
        </div>
    </div>
);

let APEVecchio = () => (
        <g id="chart">
        <polygon
            className="tier old-tier-aplus"
            points="0.32 13.63 11.48 13.63 17.33 17.84 11.48 22.05 0.32 22.05 0.32 13.63"
        />
        <polygon
            className="tier old-tier-a"
            points="0.32 23.78 18.07 23.78 23.92 27.99 18.07 32.2 0.32 32.2 0.32 23.78"
        />
        <polygon
            className="tier old-tier-b"
            points="0.32 33.93 24.8 33.93 30.65 38.14 24.8 42.35 0.32 42.35 0.32 33.93"
        />
        <polygon
            className="tier old-tier-c"
            points="0.32 44.08 32 44.08 37.85 48.29 32 52.5 0.32 52.5 0.32 44.08"
        />
        <polygon
            className="tier old-tier-d"
            points="0.32 54.22 38.39 54.22 44.24 58.44 38.39 62.65 0.32 62.65 0.32 54.22"
        />
        <polygon
            className="tier old-tier-e"
            points="0.32 64.37 45.18 64.37 51.04 68.58 45.18 72.8 0.32 72.8 0.32 64.37"
        />
        <polygon
            className="tier old-tier-f"
            points="0.32 74.52 51.84 74.52 57.7 78.73 51.84 82.95 0.32 82.95 0.32 74.52"
        />
        <polygon
            className="tier old-tier-g"
            points="0.32 84.67 58.64 84.67 64.49 88.88 58.64 93.09 0.32 93.09 0.32 84.67"
        />
        <path
            className="cls-21"
            d="M2.66,19.9l1.52-4h.56l1.62,4h-.6L5.3,18.7H3.65l-.44,1.2ZM3.8,18.27H5.14l-.41-1.09c-.13-.34-.22-.61-.28-.82a5.31,5.31,0,0,1-.22.75Z"
        />
        <path
            className="cls-21"
            d="M7.74,19.26V18.17H6.66v-.45H7.74V16.64H8.2v1.08H9.28v.45H8.2v1.09Z"
        />
        <path
            className="cls-21"
            d="M2.66,30.05l1.52-4h.56l1.62,4h-.6l-.46-1.2H3.65l-.44,1.2ZM3.8,28.42H5.14l-.41-1.09c-.13-.33-.22-.61-.28-.82a5.31,5.31,0,0,1-.22.75Z"
        />
        <path
            className="cls-21"
            d="M3.07,40.2v-4H4.55a1.82,1.82,0,0,1,.73.12.91.91,0,0,1,.43.37,1,1,0,0,1,.16.52.92.92,0,0,1-.14.48.94.94,0,0,1-.42.36,1.05,1.05,0,0,1,.56.36,1,1,0,0,1,.19.6,1.14,1.14,0,0,1-.12.52,1,1,0,0,1-.29.37,1.22,1.22,0,0,1-.43.19,2.41,2.41,0,0,1-.64.07Zm.52-2.3h.86a2,2,0,0,0,.5,0,.59.59,0,0,0,.3-.2.56.56,0,0,0,.1-.35A.64.64,0,0,0,5.26,37,.56.56,0,0,0,5,36.76a2.74,2.74,0,0,0-.61,0H3.59Zm0,1.83h1a1.48,1.48,0,0,0,.35,0,.81.81,0,0,0,.31-.1.76.76,0,0,0,.2-.23.67.67,0,0,0,.08-.33.62.62,0,0,0-.12-.38.56.56,0,0,0-.31-.23,1.79,1.79,0,0,0-.58-.07H3.59Z"
        />
        <path
            className="cls-21"
            d="M5.92,49l.52.13a1.82,1.82,0,0,1-.59,1,1.69,1.69,0,0,1-1,.33,1.82,1.82,0,0,1-1-.26,1.58,1.58,0,0,1-.61-.75,2.67,2.67,0,0,1-.21-1.06,2.45,2.45,0,0,1,.23-1.08,1.64,1.64,0,0,1,.68-.7,2,2,0,0,1,1-.24,1.65,1.65,0,0,1,1,.3,1.54,1.54,0,0,1,.56.85l-.51.13a1.3,1.3,0,0,0-.4-.63,1,1,0,0,0-.66-.2A1.27,1.27,0,0,0,4,47a1.18,1.18,0,0,0-.43.59,2.36,2.36,0,0,0-.12.75,2.4,2.4,0,0,0,.15.89,1.06,1.06,0,0,0,.45.56,1.34,1.34,0,0,0,.68.19,1.11,1.11,0,0,0,.74-.26A1.32,1.32,0,0,0,5.92,49Z"
        />
        <path
            className="cls-21"
            d="M3.09,60.5v-4H4.45a4.08,4.08,0,0,1,.71.05,1.38,1.38,0,0,1,.58.29,1.66,1.66,0,0,1,.47.68,2.69,2.69,0,0,1,.15.94,2.83,2.83,0,0,1-.1.8,1.89,1.89,0,0,1-.27.57,1.37,1.37,0,0,1-.36.36,1.31,1.31,0,0,1-.48.2,2.38,2.38,0,0,1-.63.07ZM3.61,60h.85A2,2,0,0,0,5.07,60a.9.9,0,0,0,.36-.21,1.27,1.27,0,0,0,.29-.5,2.41,2.41,0,0,0,.1-.76,1.93,1.93,0,0,0-.2-1,1,1,0,0,0-.5-.44A1.86,1.86,0,0,0,4.45,57H3.61Z"
        />
        <path className="cls-21" d="M3.1,70.65v-4H6v.46H3.62v1.22h2.2v.46H3.62v1.35H6.06v.47Z" />
        <path className="cls-21" d="M3.12,80.8v-4H5.79v.46H3.64v1.23H5.5V79H3.64v1.8Z" />
        <path
            className="cls-21"
            d="M4.94,89.39v-.46H6.62v1.47a3,3,0,0,1-.8.47A2.4,2.4,0,0,1,5,91a2.21,2.21,0,0,1-1.06-.25A1.57,1.57,0,0,1,3.2,90,2.3,2.3,0,0,1,3,89a2.43,2.43,0,0,1,.24-1.07,1.58,1.58,0,0,1,.69-.75,2.26,2.26,0,0,1,1.05-.24,1.94,1.94,0,0,1,.78.14,1.22,1.22,0,0,1,.54.39,1.73,1.73,0,0,1,.3.64l-.47.13a1.37,1.37,0,0,0-.22-.47.92.92,0,0,0-.38-.28,1.42,1.42,0,0,0-.55-.1,1.49,1.49,0,0,0-.62.11,1.15,1.15,0,0,0-.42.28,1.28,1.28,0,0,0-.25.4A2.06,2.06,0,0,0,3.5,89a2,2,0,0,0,.18.89,1.18,1.18,0,0,0,.53.53,1.71,1.71,0,0,0,.74.17,1.74,1.74,0,0,0,.67-.13,1.81,1.81,0,0,0,.49-.28v-.74Z"
        />
        <g id="chart-2" data-name="chart">
            <path d="M11,5.73V1.2h1.7a3.74,3.74,0,0,1,.69.05,1.17,1.17,0,0,1,.56.21,1.21,1.21,0,0,1,.37.43,1.44,1.44,0,0,1,.13.62,1.38,1.38,0,0,1-.37,1,1.72,1.72,0,0,1-1.32.4H11.57V5.73Zm.6-2.38h1.17a1.26,1.26,0,0,0,.82-.21.78.78,0,0,0,.24-.61A.81.81,0,0,0,13.66,2a.64.64,0,0,0-.38-.26,2.33,2.33,0,0,0-.56,0H11.57Z" />
            <path d="M15.12,1.84V1.2h.55v.64Zm0,3.89V2.45h.55V5.73Z" />
            <path d="M18.67,5.73V5.25a1.21,1.21,0,0,1-1,.55,1.26,1.26,0,0,1-.55-.11.91.91,0,0,1-.37-.28A1,1,0,0,1,16.54,5a3.19,3.19,0,0,1,0-.52v-2h.55V4.27a2.48,2.48,0,0,0,0,.58.52.52,0,0,0,.22.35.7.7,0,0,0,.42.12,1,1,0,0,0,.47-.12.8.8,0,0,0,.31-.35,1.78,1.78,0,0,0,.09-.64V2.45h.55V5.73ZM18.35,2H17.9l-.71-.86h.74Z" />
            <path d="M24,4.67l.57.07a1.38,1.38,0,0,1-.5.78,1.5,1.5,0,0,1-.94.28A1.48,1.48,0,0,1,22,5.36a1.74,1.74,0,0,1-.42-1.24A1.86,1.86,0,0,1,22,2.83a1.46,1.46,0,0,1,1.11-.45,1.42,1.42,0,0,1,1.07.44,1.78,1.78,0,0,1,.42,1.26.86.86,0,0,1,0,.15H22.18a1.23,1.23,0,0,0,.31.83.91.91,0,0,0,.68.29.89.89,0,0,0,.52-.16A1.09,1.09,0,0,0,24,4.67Zm-1.83-.9H24a1.06,1.06,0,0,0-.21-.62.87.87,0,0,0-.69-.32.88.88,0,0,0-.64.26A1,1,0,0,0,22.21,3.77Z" />
            <path d="M25.44,5.73V2.88H25V2.45h.49V2.1a1.5,1.5,0,0,1,.06-.49.73.73,0,0,1,.28-.35,1,1,0,0,1,.57-.13,2.52,2.52,0,0,1,.52.05l-.09.49-.32,0a.5.5,0,0,0-.36.1.61.61,0,0,0-.11.41v.3h.64v.43H26V5.73Z" />
            <path d="M27.08,5.73V2.88h-.49V2.45h.49V2.1a1.5,1.5,0,0,1,.06-.49.73.73,0,0,1,.28-.35A1,1,0,0,1,28,1.13a2.46,2.46,0,0,1,.52.05l-.09.49-.32,0a.5.5,0,0,0-.36.1.61.61,0,0,0-.11.41v.3h.64v.43h-.64V5.73Z" />
            <path d="M28.71,1.84V1.2h.55v.64Zm0,3.89V2.45h.55V5.73Z" />
            <path d="M32.25,4.53l.54.07a1.36,1.36,0,0,1-.45.88,1.32,1.32,0,0,1-.91.32,1.37,1.37,0,0,1-1.08-.44,1.78,1.78,0,0,1-.41-1.26,2.27,2.27,0,0,1,.18-.93,1.18,1.18,0,0,1,.53-.59,1.51,1.51,0,0,1,.78-.2,1.38,1.38,0,0,1,.88.27,1.23,1.23,0,0,1,.43.76l-.54.09a.9.9,0,0,0-.27-.5.71.71,0,0,0-.47-.17.87.87,0,0,0-.69.3,1.47,1.47,0,0,0-.26,1,1.47,1.47,0,0,0,.25,1,.83.83,0,0,0,.66.3.81.81,0,0,0,.55-.2A1,1,0,0,0,32.25,4.53Z" />
            <path d="M33.27,1.84V1.2h.56v.64Zm0,3.89V2.45h.56V5.73Z" />
            <path d="M36.92,4.67l.57.07a1.38,1.38,0,0,1-.5.78,1.71,1.71,0,0,1-2.08-.16,1.74,1.74,0,0,1-.42-1.24,1.81,1.81,0,0,1,.43-1.29A1.44,1.44,0,0,1,36,2.38a1.4,1.4,0,0,1,1.07.44,1.78,1.78,0,0,1,.42,1.26v.15H35.06a1.28,1.28,0,0,0,.31.83.91.91,0,0,0,.68.29.92.92,0,0,0,.53-.16A1.15,1.15,0,0,0,36.92,4.67Zm-1.83-.9h1.83a1.06,1.06,0,0,0-.21-.62A.84.84,0,0,0,36,2.83a.91.91,0,0,0-.65.26A1,1,0,0,0,35.09,3.77Z" />
            <path d="M38.19,5.73V2.45h.5v.47a1.18,1.18,0,0,1,1-.54,1.42,1.42,0,0,1,.55.1.84.84,0,0,1,.37.28,1.08,1.08,0,0,1,.17.41,3.11,3.11,0,0,1,0,.54v2H40.3v-2a1.29,1.29,0,0,0-.07-.5A.51.51,0,0,0,40,3a.67.67,0,0,0-.38-.1.92.92,0,0,0-.62.22,1.13,1.13,0,0,0-.25.86V5.73Z" />
            <path d="M42.92,5.23l.08.49a2.37,2.37,0,0,1-.42,0,1,1,0,0,1-.47-.09.52.52,0,0,1-.23-.26,1.81,1.81,0,0,1-.07-.65V2.88H41.4V2.45h.41V1.64l.55-.34V2.45h.56v.43h-.56V4.8a.88.88,0,0,0,0,.3.18.18,0,0,0,.1.11.32.32,0,0,0,.19,0A1.15,1.15,0,0,0,42.92,5.23Z" />
            <path d="M45.71,4.67l.57.07a1.38,1.38,0,0,1-.5.78,1.5,1.5,0,0,1-.94.28,1.48,1.48,0,0,1-1.14-.44,1.74,1.74,0,0,1-.42-1.24,1.86,1.86,0,0,1,.42-1.29,1.48,1.48,0,0,1,1.11-.45,1.42,1.42,0,0,1,1.07.44,1.78,1.78,0,0,1,.42,1.26v.15H43.85a1.23,1.23,0,0,0,.31.83.91.91,0,0,0,.68.29.87.87,0,0,0,.52-.16A1.09,1.09,0,0,0,45.71,4.67Zm-1.83-.9h1.83a1.06,1.06,0,0,0-.21-.62.85.85,0,0,0-.69-.32.88.88,0,0,0-.64.26A1,1,0,0,0,43.88,3.77Z" />
            <polygon
                className="old-tier-aplus"
                points="7.56 3.02 4.54 3.02 4.54 0 3.34 0 3.34 3.02 0.32 3.02 0.32 4.22 3.34 4.22 3.34 7.24 4.54 7.24 4.54 4.22 7.56 4.22 7.56 3.02"
            />
            <path d="M11,104.69v-4.53h.9l1.07,3.21c.1.29.17.52.22.67,0-.17.13-.41.24-.73l1.08-3.15h.81v4.53h-.58V100.9l-1.31,3.79h-.54l-1.31-3.86v3.86Z" />
            <path d="M18.41,103.63l.57.07a1.38,1.38,0,0,1-.5.78,1.71,1.71,0,0,1-2.08-.16,1.74,1.74,0,0,1-.42-1.24,1.84,1.84,0,0,1,.42-1.29,1.48,1.48,0,0,1,1.11-.46,1.37,1.37,0,0,1,1.07.45A1.78,1.78,0,0,1,19,103v.15H16.55a1.23,1.23,0,0,0,.31.83.9.9,0,0,0,.68.28.86.86,0,0,0,.53-.16A1.06,1.06,0,0,0,18.41,103.63Zm-1.83-.9h1.83a1.06,1.06,0,0,0-.21-.62.84.84,0,0,0-.68-.32.91.91,0,0,0-.65.26A1,1,0,0,0,16.58,102.73Z" />
            <path d="M19.68,104.69v-3.28h.5v.46a1.2,1.2,0,0,1,1-.54,1.42,1.42,0,0,1,.55.11.91.91,0,0,1,.37.28,1.08,1.08,0,0,1,.17.41,3.22,3.22,0,0,1,0,.54v2h-.55v-2a1.29,1.29,0,0,0-.07-.5.47.47,0,0,0-.23-.27.7.7,0,0,0-.38-.1.92.92,0,0,0-.62.22,1.13,1.13,0,0,0-.25.86v1.79Z" />
            <path d="M23,103.05a1.71,1.71,0,0,1,.5-1.35,1.57,1.57,0,0,1,1-.37,1.45,1.45,0,0,1,1.1.45,1.65,1.65,0,0,1,.43,1.22,2.14,2.14,0,0,1-.19,1,1.35,1.35,0,0,1-.55.56,1.66,1.66,0,0,1-.79.2,1.51,1.51,0,0,1-1.12-.44A1.77,1.77,0,0,1,23,103.05Zm.57,0a1.41,1.41,0,0,0,.27.94.9.9,0,0,0,.7.31.86.86,0,0,0,.68-.31,1.39,1.39,0,0,0,.28-1,1.32,1.32,0,0,0-.28-.92.87.87,0,0,0-.68-.32.91.91,0,0,0-.7.32A1.39,1.39,0,0,0,23.56,103.05Z" />
            <path d="M30.71,103.63l.58.07a1.35,1.35,0,0,1-.51.78,1.48,1.48,0,0,1-.94.28,1.51,1.51,0,0,1-1.14-.44,1.74,1.74,0,0,1-.42-1.24,1.8,1.8,0,0,1,.43-1.29,1.45,1.45,0,0,1,1.1-.46,1.41,1.41,0,0,1,1.08.45A1.77,1.77,0,0,1,31.3,103v.15H28.86a1.28,1.28,0,0,0,.3.83.9.9,0,0,0,.69.28.85.85,0,0,0,.52-.16A1,1,0,0,0,30.71,103.63Zm-1.82-.9h1.83a1.22,1.22,0,0,0-.21-.62.91.91,0,0,0-1.33-.06A1,1,0,0,0,28.89,102.73Z" />
            <path d="M32.12,104.69v-2.85h-.5v-.43h.5v-.35a1.93,1.93,0,0,1,.05-.49.75.75,0,0,1,.29-.35,1,1,0,0,1,.56-.14,2.52,2.52,0,0,1,.52.06l-.08.48-.33,0a.5.5,0,0,0-.36.11.6.6,0,0,0-.1.41v.3h.64v.43h-.64v2.85Z" />
            <path d="M33.76,104.69v-2.85h-.49v-.43h.49v-.35a1.5,1.5,0,0,1,.06-.49.68.68,0,0,1,.28-.35,1,1,0,0,1,.57-.14,2.43,2.43,0,0,1,.51.06l-.08.48a1.84,1.84,0,0,0-.33,0,.49.49,0,0,0-.35.11.59.59,0,0,0-.11.41v.3H35v.43h-.64v2.85Z" />
            <path d="M35.38,100.8v-.64h.56v.64Zm0,3.89v-3.28h.56v3.28Z" />
            <path d="M38.92,103.49l.55.07a1.4,1.4,0,0,1-.46.88,1.29,1.29,0,0,1-.9.32,1.37,1.37,0,0,1-1.08-.44,1.74,1.74,0,0,1-.41-1.26,2.27,2.27,0,0,1,.17-.93,1.31,1.31,0,0,1,.54-.6,1.62,1.62,0,0,1,.78-.2,1.28,1.28,0,0,1,.87.28,1.2,1.2,0,0,1,.44.76l-.54.09a.9.9,0,0,0-.27-.5.74.74,0,0,0-.48-.17.85.85,0,0,0-.68.3,1.4,1.4,0,0,0-.26,1,1.44,1.44,0,0,0,.25,1,.8.8,0,0,0,.66.3.76.76,0,0,0,.55-.2A1,1,0,0,0,38.92,103.49Z" />
            <path d="M40,100.8v-.64h.56v.64Zm0,3.89v-3.28h.56v3.28Z" />
            <path d="M43.6,103.63l.57.07a1.38,1.38,0,0,1-.5.78,1.71,1.71,0,0,1-2.08-.16,1.74,1.74,0,0,1-.42-1.24,1.84,1.84,0,0,1,.42-1.29,1.47,1.47,0,0,1,1.11-.46,1.39,1.39,0,0,1,1.07.45,1.78,1.78,0,0,1,.42,1.26.92.92,0,0,1,0,.15H41.74a1.23,1.23,0,0,0,.31.83.9.9,0,0,0,.68.28.83.83,0,0,0,.52-.16A1,1,0,0,0,43.6,103.63Zm-1.83-.9H43.6a1.06,1.06,0,0,0-.21-.62.86.86,0,0,0-.69-.32.88.88,0,0,0-.64.26A1,1,0,0,0,41.77,102.73Z" />
            <path d="M44.87,104.69v-3.28h.5v.46a1.2,1.2,0,0,1,1-.54,1.42,1.42,0,0,1,.55.11.91.91,0,0,1,.37.28,1.08,1.08,0,0,1,.17.41,3.22,3.22,0,0,1,0,.54v2H47v-2a1.52,1.52,0,0,0-.07-.5.47.47,0,0,0-.23-.27.71.71,0,0,0-.39-.1.9.9,0,0,0-.61.22,1.13,1.13,0,0,0-.26.86v1.79Z" />
            <path d="M49.6,104.19l.08.49a2.37,2.37,0,0,1-.42,0,.89.89,0,0,1-.47-.1.53.53,0,0,1-.24-.25,2.19,2.19,0,0,1-.06-.65v-1.89h-.41v-.43h.41v-.81l.55-.34v1.15h.56v.43H49v1.92a.88.88,0,0,0,0,.3.2.2,0,0,0,.09.11.36.36,0,0,0,.19,0A1.24,1.24,0,0,0,49.6,104.19Z" />
            <path d="M52.38,103.63l.58.07a1.38,1.38,0,0,1-.5.78,1.71,1.71,0,0,1-2.08-.16,1.74,1.74,0,0,1-.42-1.24,1.84,1.84,0,0,1,.42-1.29,1.47,1.47,0,0,1,1.11-.46,1.4,1.4,0,0,1,1.07.45A1.78,1.78,0,0,1,53,103a.92.92,0,0,1,0,.15H50.53a1.28,1.28,0,0,0,.3.83.94.94,0,0,0,.69.28.83.83,0,0,0,.52-.16A.94.94,0,0,0,52.38,103.63Zm-1.82-.9h1.83a1.06,1.06,0,0,0-.21-.62.87.87,0,0,0-.69-.32.88.88,0,0,0-.64.26A1,1,0,0,0,50.56,102.73Z" />
            <rect className="old-tier-g" x="0.32" y="101.98" width="7.24" height="1.21" />
        </g>
    </g>
);

let APENuovo = () => (
    <g id="chart">
            <g className="tier tier-a4">
                <path
                    className="tier-a4"
                    d="M4.45,16.36a4.25,4.25,0,0,1-.22.75L3.8,18.27H5.14l-.41-1.09C4.6,16.84,4.51,16.57,4.45,16.36Z"
                />
                <polygon className="tier-a4" points="8.14 18.51 8.14 16.72 6.9 18.51 8.14 18.51" />
                <path
                    className="tier-a4"
                    d="M11.48,13.63H.32v8.43H11.48l5.85-4.22ZM5.76,19.9,5.3,18.7H3.65l-.44,1.2H2.66l1.52-4h.56l1.62,4ZM9.16,19H8.63v.95H8.14V19H6.42v-.44l1.81-2.57h.4v2.57h.53Z"
                />
            </g>
            <g className="tier tier-a3">
                <path
                    className="tier-a3"
                    d="M4.45,26.51a4.25,4.25,0,0,1-.22.75L3.8,28.42H5.14l-.41-1.09C4.6,27,4.51,26.72,4.45,26.51Z"
                />
                <path
                    className="tier-a3"
                    d="M18.07,23.78H.32V32.2H18.07L23.92,28ZM5.76,30.05l-.46-1.2H3.65l-.44,1.2H2.66l1.52-4h.56l1.62,4Zm3-.29a1.33,1.33,0,0,1-1,.36A1.23,1.23,0,0,1,7,29.81a1.2,1.2,0,0,1-.39-.8l.48-.07a1.09,1.09,0,0,0,.29.6.69.69,0,0,0,.49.18.78.78,0,0,0,.58-.24.82.82,0,0,0,.23-.59.75.75,0,0,0-.77-.77,1.34,1.34,0,0,0-.35.06l.06-.43h.08a1,1,0,0,0,.56-.16.56.56,0,0,0,.24-.5.59.59,0,0,0-.18-.44.62.62,0,0,0-.46-.18.68.68,0,0,0-.48.18,1,1,0,0,0-.24.54l-.49-.09A1.29,1.29,0,0,1,7,26.34a1.16,1.16,0,0,1,.79-.26,1.31,1.31,0,0,1,.59.13,1.11,1.11,0,0,1,.42.38A.94.94,0,0,1,9,27.1a.83.83,0,0,1-.13.47,1.07,1.07,0,0,1-.41.34.89.89,0,0,1,.54.33,1,1,0,0,1,.2.64A1.17,1.17,0,0,1,8.8,29.76Z"
                />
            </g>
            <g className="tier tier-a2">
                <path
                    className="tier-a2"
                    d="M4.45,36.66a4.25,4.25,0,0,1-.22.75L3.8,38.57H5.14l-.41-1.09C4.6,37.14,4.51,36.87,4.45,36.66Z"
                />
                <path
                    className="tier-a2"
                    d="M24.8,33.93H.32v8.42H24.8l5.85-4.21Zm-19,6.27L5.3,39H3.65l-.44,1.2H2.66l1.52-4h.56l1.62,4Zm3.38,0H6.52a1,1,0,0,1,.06-.34,1.67,1.67,0,0,1,.32-.53,4.69,4.69,0,0,1,.63-.59,6.74,6.74,0,0,0,.88-.84,1,1,0,0,0,.22-.59.7.7,0,0,0-.2-.49.76.76,0,0,0-.54-.19.75.75,0,0,0-.56.21.78.78,0,0,0-.22.58l-.5-.05A1.26,1.26,0,0,1,7,36.52a1.29,1.29,0,0,1,.9-.3,1.25,1.25,0,0,1,.9.32,1,1,0,0,1,.33.78,1.27,1.27,0,0,1-.09.47,2.07,2.07,0,0,1-.33.48A8.23,8.23,0,0,1,8,39a6.28,6.28,0,0,0-.56.5,1.51,1.51,0,0,0-.2.26H9.14Z"
                />
            </g>
            <g className="tier tier-a1">
                <path
                    className="tier-a1"
                    d="M4.45,46.8a4.12,4.12,0,0,1-.22.76L3.8,48.72H5.14l-.41-1.1A8.06,8.06,0,0,1,4.45,46.8Z"
                />
                <path
                    className="tier-a1"
                    d="M32,44.08H.32V52.5H32l5.86-4.21ZM5.76,50.35l-.46-1.2H3.65l-.44,1.2H2.66l1.52-4h.56l1.62,4Zm2.65,0H7.93v-3.1a2.61,2.61,0,0,1-.46.33,3.08,3.08,0,0,1-.51.26v-.47a3,3,0,0,0,.71-.47,1.79,1.79,0,0,0,.43-.53h.31Z"
                />
            </g>
            <g className="tier tier-b">
                <path
                    className="tier-b"
                    d="M5.09,58.74a1.79,1.79,0,0,0-.58-.07H3.59V60h1l.36,0a.82.82,0,0,0,.3-.11.54.54,0,0,0,.2-.22.67.67,0,0,0,.08-.33A.65.65,0,0,0,5.4,59,.59.59,0,0,0,5.09,58.74Z"
                />
                <path
                    className="tier-b"
                    d="M5,58.16a.59.59,0,0,0,.3-.2.56.56,0,0,0,.1-.35.64.64,0,0,0-.09-.34A.56.56,0,0,0,5,57.06,2.24,2.24,0,0,0,4.38,57H3.59V58.2h.86A2,2,0,0,0,5,58.16Z"
                />
                <path
                    className="tier-b"
                    d="M38.39,54.22H.32v8.43H38.39l5.85-4.21ZM5.94,59.87a1,1,0,0,1-.29.37,1.22,1.22,0,0,1-.43.19,2.41,2.41,0,0,1-.64.07H3.07v-4H4.55a1.82,1.82,0,0,1,.73.12.87.87,0,0,1,.43.37.93.93,0,0,1,.16.52.92.92,0,0,1-.14.48.94.94,0,0,1-.42.36,1.05,1.05,0,0,1,.56.36,1,1,0,0,1,.19.6A1.14,1.14,0,0,1,5.94,59.87Z"
                />
            </g>
            <g className="tier tier-c">
                <path
                    className="tier-c"
                    d="M45.18,64.37H.32V72.8H45.18L51,68.58Zm-39.33,6a1.63,1.63,0,0,1-1,.34,1.82,1.82,0,0,1-1-.26,1.58,1.58,0,0,1-.61-.75,2.71,2.71,0,0,1-.21-1.06,2.29,2.29,0,0,1,.24-1.08,1.57,1.57,0,0,1,.67-.7,2,2,0,0,1,1-.24,1.63,1.63,0,0,1,1,.3,1.54,1.54,0,0,1,.56.85l-.51.12a1.4,1.4,0,0,0-.4-.63,1.09,1.09,0,0,0-.66-.19,1.22,1.22,0,0,0-.76.22,1.16,1.16,0,0,0-.44.58,2.6,2.6,0,0,0,0,1.65,1.06,1.06,0,0,0,.45.56,1.37,1.37,0,0,0,.68.19A1.11,1.11,0,0,0,5.5,70a1.32,1.32,0,0,0,.42-.75l.52.13A1.76,1.76,0,0,1,5.85,70.37Z"
                />
            </g>
            <g className="tier tier-d">
                <path
                    className="tier-d"
                    d="M51.84,74.52H.32V83H51.84l5.86-4.22ZM6.26,79.59a1.84,1.84,0,0,1-.27.58,1.54,1.54,0,0,1-.36.36,1.55,1.55,0,0,1-.47.2,3.06,3.06,0,0,1-.64.07H3.09v-4H4.46a4,4,0,0,1,.7,0,1.38,1.38,0,0,1,.58.29,1.54,1.54,0,0,1,.47.67,2.79,2.79,0,0,1,.15,1A2.71,2.71,0,0,1,6.26,79.59Z"
                />
                <path
                    className="tier-d"
                    d="M5.12,77.39a1.81,1.81,0,0,0-.67-.09H3.62v3h.84a2,2,0,0,0,.61-.07.9.9,0,0,0,.36-.21,1.27,1.27,0,0,0,.29-.5,2.41,2.41,0,0,0,.1-.76,1.81,1.81,0,0,0-.2-1A1,1,0,0,0,5.12,77.39Z"
                />
            </g>
            <g className="tier tier-e">
                <path
                    className="tier-e"
                    d="M58.64,84.67H.32v8.42H58.64l5.86-4.21ZM6.06,91h-3V87H6v.46H3.63v1.22H5.82v.46H3.63v1.35H6.06Z"
                />
            </g>
            <g className="tier f">
                <path
                    className="tier-f"
                    d="M65.3,94.82H.32v8.42h65L71.15,99ZM5.79,97.6H3.64v1.23H5.5v.46H3.64v1.8H3.12v-4H5.79Z"
                />
            </g>
            <g className="tier g">
                <path
                    className="tier-g"
                    d="M71.55,105H.32v8.42H71.55l5.86-4.21ZM6.62,110.69a2.71,2.71,0,0,1-.8.46,2.2,2.2,0,0,1-.84.16,2.21,2.21,0,0,1-1.06-.25,1.67,1.67,0,0,1-.72-.72A2.33,2.33,0,0,1,3,109.28a2.51,2.51,0,0,1,.24-1.08,1.57,1.57,0,0,1,.7-.74,2.19,2.19,0,0,1,1-.25,2.13,2.13,0,0,1,.78.14,1.4,1.4,0,0,1,.54.39,1.78,1.78,0,0,1,.3.65l-.47.13a1.53,1.53,0,0,0-.22-.48,1,1,0,0,0-.38-.27,1.38,1.38,0,0,0-.55-.11,1.67,1.67,0,0,0-.62.11,1.31,1.31,0,0,0-.42.29,1.36,1.36,0,0,0-.25.39,2.11,2.11,0,0,0-.15.8,1.91,1.91,0,0,0,.18.88,1.14,1.14,0,0,0,.53.53,1.59,1.59,0,0,0,.74.18,2,2,0,0,0,.67-.13,1.81,1.81,0,0,0,.49-.28v-.74H4.94v-.47H6.62Z"
                />
            </g>
            <path d="M11,5.73V1.2h1.7a3.74,3.74,0,0,1,.69.05,1.17,1.17,0,0,1,.56.21,1.21,1.21,0,0,1,.37.43,1.44,1.44,0,0,1,.13.62,1.38,1.38,0,0,1-.37,1,1.72,1.72,0,0,1-1.32.4H11.57V5.73Zm.6-2.38h1.17a1.26,1.26,0,0,0,.82-.21.78.78,0,0,0,.24-.61A.81.81,0,0,0,13.66,2a.64.64,0,0,0-.38-.26,2.33,2.33,0,0,0-.56,0H11.57Z" />
            <path d="M15.12,1.84V1.2h.55v.64Zm0,3.89V2.45h.55V5.73Z" />
            <path d="M18.67,5.73V5.25a1.21,1.21,0,0,1-1,.55,1.26,1.26,0,0,1-.55-.11.91.91,0,0,1-.37-.28A1,1,0,0,1,16.54,5a3.19,3.19,0,0,1,0-.52v-2h.55V4.27a2.48,2.48,0,0,0,0,.58.52.52,0,0,0,.22.35.7.7,0,0,0,.42.12,1,1,0,0,0,.47-.12.8.8,0,0,0,.31-.35,1.78,1.78,0,0,0,.09-.64V2.45h.55V5.73ZM18.35,2H17.9l-.71-.86h.74Z" />
            <path d="M24,4.67l.57.07a1.38,1.38,0,0,1-.5.78,1.5,1.5,0,0,1-.94.28A1.48,1.48,0,0,1,22,5.36a1.74,1.74,0,0,1-.42-1.24A1.86,1.86,0,0,1,22,2.83a1.46,1.46,0,0,1,1.11-.45,1.42,1.42,0,0,1,1.07.44,1.78,1.78,0,0,1,.42,1.26.86.86,0,0,1,0,.15H22.18a1.23,1.23,0,0,0,.31.83.91.91,0,0,0,.68.29.89.89,0,0,0,.52-.16A1.09,1.09,0,0,0,24,4.67Zm-1.83-.9H24a1.06,1.06,0,0,0-.21-.62.87.87,0,0,0-.69-.32.88.88,0,0,0-.64.26A1,1,0,0,0,22.21,3.77Z" />
            <path d="M25.44,5.73V2.88H25V2.45h.49V2.1a1.5,1.5,0,0,1,.06-.49.73.73,0,0,1,.28-.35,1,1,0,0,1,.57-.13,2.52,2.52,0,0,1,.52.05l-.09.49-.32,0a.5.5,0,0,0-.36.1.61.61,0,0,0-.11.41v.3h.64v.43H26V5.73Z" />
            <path d="M27.08,5.73V2.88h-.49V2.45h.49V2.1a1.5,1.5,0,0,1,.06-.49.73.73,0,0,1,.28-.35A1,1,0,0,1,28,1.13a2.46,2.46,0,0,1,.52.05l-.09.49-.32,0a.5.5,0,0,0-.36.1.61.61,0,0,0-.11.41v.3h.64v.43h-.64V5.73Z" />
            <path d="M28.71,1.84V1.2h.55v.64Zm0,3.89V2.45h.55V5.73Z" />
            <path d="M32.25,4.53l.54.07a1.36,1.36,0,0,1-.45.88,1.32,1.32,0,0,1-.91.32,1.37,1.37,0,0,1-1.08-.44,1.78,1.78,0,0,1-.41-1.26,2.27,2.27,0,0,1,.18-.93,1.18,1.18,0,0,1,.53-.59,1.51,1.51,0,0,1,.78-.2,1.38,1.38,0,0,1,.88.27,1.23,1.23,0,0,1,.43.76l-.54.09a.9.9,0,0,0-.27-.5.71.71,0,0,0-.47-.17.87.87,0,0,0-.69.3,1.47,1.47,0,0,0-.26,1,1.47,1.47,0,0,0,.25,1,.83.83,0,0,0,.66.3.81.81,0,0,0,.55-.2A1,1,0,0,0,32.25,4.53Z" />
            <path d="M33.27,1.84V1.2h.56v.64Zm0,3.89V2.45h.56V5.73Z" />
            <path d="M36.92,4.67l.57.07a1.38,1.38,0,0,1-.5.78,1.71,1.71,0,0,1-2.08-.16,1.74,1.74,0,0,1-.42-1.24,1.81,1.81,0,0,1,.43-1.29A1.44,1.44,0,0,1,36,2.38a1.4,1.4,0,0,1,1.07.44,1.78,1.78,0,0,1,.42,1.26v.15H35.06a1.28,1.28,0,0,0,.31.83.91.91,0,0,0,.68.29.92.92,0,0,0,.53-.16A1.15,1.15,0,0,0,36.92,4.67Zm-1.83-.9h1.83a1.06,1.06,0,0,0-.21-.62A.84.84,0,0,0,36,2.83a.91.91,0,0,0-.65.26A1,1,0,0,0,35.09,3.77Z" />
            <path d="M38.19,5.73V2.45h.5v.47a1.18,1.18,0,0,1,1-.54,1.42,1.42,0,0,1,.55.1.84.84,0,0,1,.37.28,1.08,1.08,0,0,1,.17.41,3.11,3.11,0,0,1,0,.54v2H40.3v-2a1.29,1.29,0,0,0-.07-.5A.51.51,0,0,0,40,3a.67.67,0,0,0-.38-.1.92.92,0,0,0-.62.22,1.13,1.13,0,0,0-.25.86V5.73Z" />
            <path d="M42.92,5.23l.08.49a2.37,2.37,0,0,1-.42,0,1,1,0,0,1-.47-.09.52.52,0,0,1-.23-.26,1.81,1.81,0,0,1-.07-.65V2.88H41.4V2.45h.41V1.64l.55-.34V2.45h.56v.43h-.56V4.8a.88.88,0,0,0,0,.3.18.18,0,0,0,.1.11.32.32,0,0,0,.19,0A1.15,1.15,0,0,0,42.92,5.23Z" />
            <path d="M45.71,4.67l.57.07a1.38,1.38,0,0,1-.5.78,1.5,1.5,0,0,1-.94.28,1.48,1.48,0,0,1-1.14-.44,1.74,1.74,0,0,1-.42-1.24,1.86,1.86,0,0,1,.42-1.29,1.48,1.48,0,0,1,1.11-.45,1.42,1.42,0,0,1,1.07.44,1.78,1.78,0,0,1,.42,1.26v.15H43.85a1.23,1.23,0,0,0,.31.83.91.91,0,0,0,.68.29.87.87,0,0,0,.52-.16A1.09,1.09,0,0,0,45.71,4.67Zm-1.83-.9h1.83a1.06,1.06,0,0,0-.21-.62.85.85,0,0,0-.69-.32.88.88,0,0,0-.64.26A1,1,0,0,0,43.88,3.77Z" />
            <polygon
                className="tier-a3"
                points="7.56 3.02 4.54 3.02 4.54 0 3.34 0 3.34 3.02 0.32 3.02 0.32 4.22 3.34 4.22 3.34 7.24 4.54 7.24 4.54 4.22 7.56 4.22 7.56 3.02"
            />
            <path d="M11,123.69v-4.53h.9l1.07,3.21c.1.29.17.52.22.67,0-.17.13-.41.24-.73l1.08-3.15h.81v4.53h-.58V119.9l-1.31,3.79h-.54l-1.31-3.86v3.86Z" />
            <path d="M18.41,122.63l.57.07a1.38,1.38,0,0,1-.5.78,1.71,1.71,0,0,1-2.08-.16,1.74,1.74,0,0,1-.42-1.24,1.84,1.84,0,0,1,.42-1.29,1.48,1.48,0,0,1,1.11-.46,1.37,1.37,0,0,1,1.07.45A1.78,1.78,0,0,1,19,122v.15H16.55a1.23,1.23,0,0,0,.31.83.9.9,0,0,0,.68.28.86.86,0,0,0,.53-.16A1.06,1.06,0,0,0,18.41,122.63Zm-1.83-.9h1.83a1.06,1.06,0,0,0-.21-.62.84.84,0,0,0-.68-.32.91.91,0,0,0-.65.26A1,1,0,0,0,16.58,121.73Z" />
            <path d="M19.68,123.69v-3.28h.5v.46a1.2,1.2,0,0,1,1-.54,1.42,1.42,0,0,1,.55.11.91.91,0,0,1,.37.28,1.08,1.08,0,0,1,.17.41,3.22,3.22,0,0,1,0,.54v2h-.55v-2a1.29,1.29,0,0,0-.07-.5.47.47,0,0,0-.23-.27.7.7,0,0,0-.38-.1.92.92,0,0,0-.62.22,1.13,1.13,0,0,0-.25.86v1.79Z" />
            <path d="M23,122.05a1.71,1.71,0,0,1,.5-1.35,1.57,1.57,0,0,1,1-.37,1.45,1.45,0,0,1,1.1.45,1.65,1.65,0,0,1,.43,1.22,2.14,2.14,0,0,1-.19,1,1.35,1.35,0,0,1-.55.56,1.66,1.66,0,0,1-.79.2,1.51,1.51,0,0,1-1.12-.44A1.77,1.77,0,0,1,23,122.05Zm.57,0a1.41,1.41,0,0,0,.27.94.9.9,0,0,0,.7.31.86.86,0,0,0,.68-.31,1.39,1.39,0,0,0,.28-1,1.32,1.32,0,0,0-.28-.92.87.87,0,0,0-.68-.32.91.91,0,0,0-.7.32A1.39,1.39,0,0,0,23.56,122.05Z" />
            <path d="M30.71,122.63l.58.07a1.35,1.35,0,0,1-.51.78,1.48,1.48,0,0,1-.94.28,1.51,1.51,0,0,1-1.14-.44,1.74,1.74,0,0,1-.42-1.24,1.8,1.8,0,0,1,.43-1.29,1.45,1.45,0,0,1,1.1-.46,1.41,1.41,0,0,1,1.08.45A1.77,1.77,0,0,1,31.3,122v.15H28.86a1.28,1.28,0,0,0,.3.83.9.9,0,0,0,.69.28.85.85,0,0,0,.52-.16A1,1,0,0,0,30.71,122.63Zm-1.82-.9h1.83a1.22,1.22,0,0,0-.21-.62.91.91,0,0,0-1.33-.06A1,1,0,0,0,28.89,121.73Z" />
            <path d="M32.12,123.69v-2.85h-.5v-.43h.5v-.35a1.93,1.93,0,0,1,.05-.49.75.75,0,0,1,.29-.35,1,1,0,0,1,.56-.14,2.52,2.52,0,0,1,.52.06l-.08.48-.33,0a.5.5,0,0,0-.36.11.6.6,0,0,0-.1.41v.3h.64v.43h-.64v2.85Z" />
            <path d="M33.76,123.69v-2.85h-.49v-.43h.49v-.35a1.5,1.5,0,0,1,.06-.49.68.68,0,0,1,.28-.35,1,1,0,0,1,.57-.14,2.43,2.43,0,0,1,.51.06l-.08.48a1.84,1.84,0,0,0-.33,0,.49.49,0,0,0-.35.11.59.59,0,0,0-.11.41v.3H35v.43h-.64v2.85Z" />
            <path d="M35.38,119.8v-.64h.56v.64Zm0,3.89v-3.28h.56v3.28Z" />
            <path d="M38.92,122.49l.55.07a1.4,1.4,0,0,1-.46.88,1.29,1.29,0,0,1-.9.32,1.37,1.37,0,0,1-1.08-.44,1.74,1.74,0,0,1-.41-1.26,2.27,2.27,0,0,1,.17-.93,1.31,1.31,0,0,1,.54-.6,1.62,1.62,0,0,1,.78-.2,1.28,1.28,0,0,1,.87.28,1.2,1.2,0,0,1,.44.76l-.54.09a.9.9,0,0,0-.27-.5.74.74,0,0,0-.48-.17.85.85,0,0,0-.68.3,1.4,1.4,0,0,0-.26,1,1.44,1.44,0,0,0,.25,1,.8.8,0,0,0,.66.3.76.76,0,0,0,.55-.2A1,1,0,0,0,38.92,122.49Z" />
            <path d="M40,119.8v-.64h.56v.64Zm0,3.89v-3.28h.56v3.28Z" />
            <path d="M43.6,122.63l.57.07a1.38,1.38,0,0,1-.5.78,1.71,1.71,0,0,1-2.08-.16,1.74,1.74,0,0,1-.42-1.24,1.84,1.84,0,0,1,.42-1.29,1.47,1.47,0,0,1,1.11-.46,1.39,1.39,0,0,1,1.07.45,1.78,1.78,0,0,1,.42,1.26.92.92,0,0,1,0,.15H41.74a1.23,1.23,0,0,0,.31.83.9.9,0,0,0,.68.28.83.83,0,0,0,.52-.16A1,1,0,0,0,43.6,122.63Zm-1.83-.9H43.6a1.06,1.06,0,0,0-.21-.62.86.86,0,0,0-.69-.32.88.88,0,0,0-.64.26A1,1,0,0,0,41.77,121.73Z" />
            <path d="M44.87,123.69v-3.28h.5v.46a1.2,1.2,0,0,1,1-.54,1.42,1.42,0,0,1,.55.11.91.91,0,0,1,.37.28,1.08,1.08,0,0,1,.17.41,3.22,3.22,0,0,1,0,.54v2H47v-2a1.52,1.52,0,0,0-.07-.5.47.47,0,0,0-.23-.27.71.71,0,0,0-.39-.1.9.9,0,0,0-.61.22,1.13,1.13,0,0,0-.26.86v1.79Z" />
            <path d="M49.6,123.19l.08.49a2.37,2.37,0,0,1-.42,0,.89.89,0,0,1-.47-.1.53.53,0,0,1-.24-.25,2.19,2.19,0,0,1-.06-.65v-1.89h-.41v-.43h.41v-.81l.55-.34v1.15h.56v.43H49v1.92a.88.88,0,0,0,0,.3.2.2,0,0,0,.09.11.36.36,0,0,0,.19,0A1.24,1.24,0,0,0,49.6,123.19Z" />
            <path d="M52.38,122.63l.58.07a1.38,1.38,0,0,1-.5.78,1.71,1.71,0,0,1-2.08-.16,1.74,1.74,0,0,1-.42-1.24,1.84,1.84,0,0,1,.42-1.29,1.47,1.47,0,0,1,1.11-.46,1.4,1.4,0,0,1,1.07.45A1.78,1.78,0,0,1,53,122a.92.92,0,0,1,0,.15H50.53a1.28,1.28,0,0,0,.3.83.94.94,0,0,0,.69.28.83.83,0,0,0,.52-.16A.94.94,0,0,0,52.38,122.63Zm-1.82-.9h1.83a1.06,1.06,0,0,0-.21-.62.87.87,0,0,0-.69-.32.88.88,0,0,0-.64.26A1,1,0,0,0,50.56,121.73Z" />
            <rect className="cls-15" x="0.32" y="120.98" width="7.24" height="1.21" />
        </g>
);

let ClassLabel = ({ tier, old, tierClassName, epglNrenValue, epglNrenUnit, epiValue, epiUnit }) => {
    const formatEpiValue = (value) => {
        const position = value.indexOf(ENERGY_CONSUMPTION_UNIT);

        if (position >= 0) {
            return <>
                <tspan x="0" dy="0">{value.substr(0, position).trim()}</tspan>
                <tspan className="cls-19" x="0" dy="1.4em">{value.substr(position)}</tspan>
            </>;
        }

        return value;
    };

    return (
        <g id="label">
            <g id="bg">
                <path
                className={tierClassName}
                    d="M123.89,19a6.42,6.42,0,0,1-.23.83l-.47,1.26h1.45l-.44-1.19C124.06,19.53,124,19.24,123.89,19Z"
                />
                <path
                className={tierClassName}
                    d="M101.65,19h-1.36v1.42h1.22a1.91,1.91,0,0,0,.61-.08.64.64,0,0,0,.34-.26.73.73,0,0,0,.11-.38.65.65,0,0,0-.22-.51A1,1,0,0,0,101.65,19Z"
                />
                <path
                className={tierClassName}
                    d="M103.08,10.74c-.22-.6-.39-1.08-.5-1.47a9,9,0,0,1-.38,1.35l-.78,2.08h2.4Z"
                />
                <path
                className={tierClassName}
                    d="M82.82-.1V113.39h48.34V-.1Zm37.12,8.63h5.11v.83h-4.17v2.17h3.91v.83h-3.91v2.41h4.34v.83h-5.28Zm6,14.32h-.65l-.5-1.3H123l-.47,1.3h-.61l1.66-4.3h.61Zm-6-4.37a1.79,1.79,0,0,1,1.09.33,1.67,1.67,0,0,1,.61.92l-.56.13a1.31,1.31,0,0,0-.43-.68,1.18,1.18,0,0,0-.72-.21,1.38,1.38,0,0,0-.83.23,1.31,1.31,0,0,0-.47.64,2.57,2.57,0,0,0-.13.83,2.61,2.61,0,0,0,.16,1,1.22,1.22,0,0,0,.5.61,1.4,1.4,0,0,0,.73.2,1.19,1.19,0,0,0,.81-.28,1.4,1.4,0,0,0,.45-.81l.56.14a1.87,1.87,0,0,1-.64,1.07,1.81,1.81,0,0,1-1.14.37,2,2,0,0,1-1.13-.29,1.7,1.7,0,0,1-.66-.82,2.91,2.91,0,0,1-.23-1.15,2.52,2.52,0,0,1,.26-1.17,1.68,1.68,0,0,1,.73-.76A2.14,2.14,0,0,1,119.94,18.48Zm-2.39-5.38a1.37,1.37,0,0,0-.64-.42,12.73,12.73,0,0,0-1.25-.34,7.08,7.08,0,0,1-1.37-.44,1.89,1.89,0,0,1-.75-.66,1.58,1.58,0,0,1-.25-.88,1.8,1.8,0,0,1,.3-1,1.87,1.87,0,0,1,.89-.71,3.38,3.38,0,0,1,1.3-.24,3.5,3.5,0,0,1,1.38.25,2,2,0,0,1,.92.75,2.05,2.05,0,0,1,.35,1.11l-.89.07a1.46,1.46,0,0,0-.49-1,1.91,1.91,0,0,0-1.24-.35,2,2,0,0,0-1.23.31.94.94,0,0,0-.39.75.8.8,0,0,0,.27.63,3.73,3.73,0,0,0,1.41.51,9.05,9.05,0,0,1,1.57.45,2.18,2.18,0,0,1,.91.72,1.7,1.7,0,0,1,.29,1,1.91,1.91,0,0,1-.32,1.07,2,2,0,0,1-.93.77,3.12,3.12,0,0,1-1.36.28,4,4,0,0,1-1.61-.28,2.26,2.26,0,0,1-1-.84,2.35,2.35,0,0,1-.39-1.27l.88-.08a2,2,0,0,0,.29.87,1.56,1.56,0,0,0,.71.55,2.72,2.72,0,0,0,1.09.21,2.5,2.5,0,0,0,.94-.16,1.24,1.24,0,0,0,.61-.43,1,1,0,0,0,.2-.61A.9.9,0,0,0,117.55,13.1Zm-.48,5.45v4.3h-.57v-4.3Zm-4.66,0h3.41v.51H114.4v3.79h-.57V19.06h-1.42ZM111,13.1a1.39,1.39,0,0,0-.63-.42,12.38,12.38,0,0,0-1.26-.34,7.33,7.33,0,0,1-1.37-.44,1.89,1.89,0,0,1-.75-.66,1.58,1.58,0,0,1-.25-.88,1.8,1.8,0,0,1,.3-1,1.92,1.92,0,0,1,.89-.71,3.38,3.38,0,0,1,1.3-.24,3.5,3.5,0,0,1,1.38.25,2,2,0,0,1,.93.75,2.13,2.13,0,0,1,.34,1.11l-.89.07a1.42,1.42,0,0,0-.49-1,1.9,1.9,0,0,0-1.23-.35,2,2,0,0,0-1.24.31.94.94,0,0,0-.39.75.78.78,0,0,0,.28.63,3.73,3.73,0,0,0,1.41.51,8.87,8.87,0,0,1,1.56.45,2.12,2.12,0,0,1,.91.72,1.71,1.71,0,0,1,.3,1,1.92,1.92,0,0,1-.33,1.07,2.07,2.07,0,0,1-.92.77,3.2,3.2,0,0,1-1.37.28,4,4,0,0,1-1.61-.28,2.26,2.26,0,0,1-1-.84,2.35,2.35,0,0,1-.39-1.27l.88-.08a2,2,0,0,0,.29.87,1.62,1.62,0,0,0,.71.55,2.75,2.75,0,0,0,1.09.21,2.47,2.47,0,0,0,.94-.16,1.33,1.33,0,0,0,.62-.43,1.06,1.06,0,0,0,.2-.61A.91.91,0,0,0,111,13.1Zm.73,7.28v.5h-2.38v1.47H112v.5h-3.21v-4.3h3.11v.51h-2.54v1.32Zm-4.79-1.75a1.41,1.41,0,0,1,.6.42,1.87,1.87,0,0,1,.32.71l-.51.14a1.81,1.81,0,0,0-.24-.52,1,1,0,0,0-.41-.3,1.46,1.46,0,0,0-.6-.11,1.92,1.92,0,0,0-.67.11,1.31,1.31,0,0,0-.46.32,1.37,1.37,0,0,0-.27.42,2.29,2.29,0,0,0-.17.87,2.13,2.13,0,0,0,.2,1,1.3,1.3,0,0,0,.58.58,1.84,1.84,0,0,0,.8.18,1.78,1.78,0,0,0,.72-.14,1.67,1.67,0,0,0,.54-.3v-.8h-1.27v-.51h1.83v1.6a3.2,3.2,0,0,1-.87.5,2.63,2.63,0,0,1-.92.17,2.47,2.47,0,0,1-1.15-.27,1.86,1.86,0,0,1-.78-.79,2.53,2.53,0,0,1-.26-1.15,2.6,2.6,0,0,1,.26-1.17,1.79,1.79,0,0,1,.75-.81,2.46,2.46,0,0,1,1.14-.26A2.24,2.24,0,0,1,106.9,18.63Zm-4.8-10.1h1L106,15.6h-1.07l-.82-2.14h-3l-.77,2.14h-1Zm.19,12.58a3,3,0,0,1,.47.57l.75,1.17h-.72l-.56-.89c-.17-.26-.31-.46-.41-.59a1.3,1.3,0,0,0-.29-.29,1.19,1.19,0,0,0-.26-.12l-.32,0h-.66v1.91h-.57v-4.3h1.91a2.45,2.45,0,0,1,.87.12,1,1,0,0,1,.48.41,1.2,1.2,0,0,1,.18.65,1.08,1.08,0,0,1-.3.77,1.49,1.49,0,0,1-.91.4A1.44,1.44,0,0,1,102.29,21.11ZM94.63,8.53h.93v6.24h3.49v.83H94.63Zm4,11.85v.5H96.28v1.47h2.64v.5H95.71v-4.3h3.11v.51H96.28v1.32ZM94.2,18.55h.55v4.3h-.59L91.9,19.47v3.38h-.54v-4.3h.58l2.26,3.38Zm-6.51-8.47a2.88,2.88,0,0,1,1.2-1.24,3.45,3.45,0,0,1,1.71-.43A2.93,2.93,0,0,1,92.39,9a2.82,2.82,0,0,1,1,1.52l-.92.22a2.19,2.19,0,0,0-.71-1.13,1.94,1.94,0,0,0-1.18-.35,2.28,2.28,0,0,0-1.36.39,2,2,0,0,0-.77,1.05A4.17,4.17,0,0,0,88.23,12a4.41,4.41,0,0,0,.26,1.57,2,2,0,0,0,.82,1,2.31,2.31,0,0,0,1.2.33,2,2,0,0,0,1.33-.45,2.37,2.37,0,0,0,.74-1.35l.93.24a3.18,3.18,0,0,1-1.05,1.76,3,3,0,0,1-1.87.6,3.29,3.29,0,0,1-1.86-.47,2.86,2.86,0,0,1-1.09-1.34,4.81,4.81,0,0,1-.38-1.9A4.22,4.22,0,0,1,87.69,10.08Zm2.62,10.3v.5H87.93v1.47h2.64v.5H87.36v-4.3h3.11v.51H87.93v1.32Zm38,78.91H85.71V58.36h42.57Z"
                />
            </g>
            {old ? (
                <Fragment>
                    <text className="cls-16" transform="translate(90.74 68.37)">
                        EPi
                    </text>
                    <text className="cls-19" transform="translate(89.13 94.51)">
                        {epiUnit}
                    </text>
                    <text className="cls-20" transform="translate(89.51 81.68)">
                        {formatEpiValue(epiValue)}
                    </text>
                    <text className="cls-21" transform={`translate(9${tier.length === 1 ? 8 : 1}.11 49.36)`}>
                        {tier.toUpperCase()}
                    </text>
                </Fragment>
            ) : (
                <Fragment>
                    <text className="cls-16" transform="translate(90.74 68.37)">
                        EPgl, nren
                    </text>
                    <text className="cls-19" transform="translate(89.13 94.51)">
                    {epglNrenUnit}
                    </text>
                    <text className="cls-20" x="0" y="0" transform="translate(89.51 81.68)">
                        {formatEpiValue(epglNrenValue)}
                    </text>
                    <text className="cls-21" transform={`translate(9${tier.length === 1 ? 8 : 1}.11 49.36)`}>
                    {tier.toUpperCase()}
                    </text>
                </Fragment>
            )}
        </g>);
};
