import React from 'react';
import { storiesOf } from '@storybook/react';
import { EnergeticClass, EnergeticClassFabbricato, ENERGY_TIERS, OLD_ENERGY_TIERS } from '../EnergeticClass';

storiesOf('EnergeticClass', module)
    .add('new ape', () => <Example />)
    .add('old ape', () => <Example old />);

class Example extends React.Component {
    state = {
        tier: this.props.old ? OLD_ENERGY_TIERS[0] : ENERGY_TIERS[0],
        winterValue: 1,
        summerValue: 2,
    };

    render() {
        return (
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                }}
            >
                <EnergeticClass
                    width={500}
                    height={500}
                    tier={this.state.tier}
                    old={this.props.old}
                    epglRenValue="12"
                    epglRenUnit="kw"
                    epglNrenValue="21"
                    epglNrenUnit="Km/h"
                    epiValue="31"
                    epiUnit="Kg"
                />
                {!this.props.old && (
                    <EnergeticClassFabbricato
                        winterValue={this.state.winterValue}
                        summerValue={this.state.summerValue}
                    />
                )}
            </div>
        );
    }
}
