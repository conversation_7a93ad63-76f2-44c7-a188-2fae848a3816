import { PropsWithChildren, ReactNode } from 'react';
import clsx from 'clsx';

type CrmFiltersProps = PropsWithChildren;

/**
 * CrmFilters component displays filterable content within a modal window.
 * Used for creating filter interfaces in CRM-related views.
 *
 * @example
 * <CrmFilters>
 *   <CrmFilters.Element>
 *     <CrmFilters.Element.Title>Status</CrmFilters.Element.Title>
 *     <Select options={statusOptions} />
 *   </CrmFilters.Element>
 *   <CrmFilters.Actions>
 *      <Button>Apply</Button>
 *   </CrmFilters.Actions>
 * </CrmFilters>
 */
export function CrmFilters(props: CrmFiltersProps) {
    return <div className="crm-filter">{props.children}</div>;
}

function CrmFilterElementTitle(props: PropsWithChildren<{ icon?: ReactNode }>) {
    return (
        <div className="crm-filter__elementTitle gx-display-subtitle">
            {props.children}
        </div>
    );
}

function CrmFilterElement(
    props: PropsWithChildren<{
        direction?: 'horizontal' | 'vertical';
    }>
) {
    return (
        <div
            className={clsx('crm-filter__element', {
                'crm-filter__element--horizontal':
                    props.direction === 'horizontal',
                'crm-filter__element--vertical': props.direction === 'vertical',
            })}
        >
            {props.children}
        </div>
    );
}

CrmFilterElement.Title = CrmFilterElementTitle;

function CrmFilterActions(props: PropsWithChildren) {
    return <div className="crm-filter__actions">{props.children}</div>;
}

CrmFilters.Element = CrmFilterElement;
CrmFilters.Actions = CrmFilterActions;
