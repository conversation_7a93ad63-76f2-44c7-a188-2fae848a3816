import React, { Component } from 'react';
import { storiesOf } from '@storybook/react';
import { IframeSandbox } from './index';

storiesOf('IframeSandbox', module)
    .add('hello_world', () => (
        <IframeSandbox>
            <div>Hello world</div>
        </IframeSandbox>
    ))
    .add('with some stylesheet', () => <IframeWithStyleSheet />)
    .add('with inline stylesheet', () => <IframeWithStyleSheet useinlinestylesheet={true} />)
;

class IframeWithStyleSheet extends Component {
    setIframeHandler = iframe => {
        this.iframe = iframe;
    };

    logHTML = () => {
        console.log(this.iframe && this.iframe.contentHTML);
    };

    render() {
        return (
            <IframeSandbox
                stylesheets={['https://code.getmdl.io/1.3.0/material.indigo-pink.min.css']}
                useinlinestylesheet={this.props.useinlinestylesheet}
                width="700"
                height="400"
                ref={this.setIframeHandler}
            >
                <div className="mdl-card mdl-shadow--2dp" style={{ width: 512, margin: 10 }}>
                    <div
                        className="mdl-card__title mdl-card--expand"
                        style={{
                            color: '#fff',
                            height: 176,
                            background:
                                "url('https://getmdl.io/assets/demos/welcome_card.jpg') center / cover",
                        }}
                    >
                        <h2 className="mdl-card__title-text">Material card example</h2>
                    </div>
                    <div className="mdl-card__supporting-text">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenan convallis.
                    </div>
                    <div className="mdl-card__actions mdl-card--border">
                        <a
                            className="mdl-button mdl-button--colored mdl-js-button mdl-js-ripple-effect"
                            onClick={this.logHTML}
                        >
                            Get HTML!!!!
                        </a>
                    </div>
                </div>
            </IframeSandbox>
        );
    }
}
