import { DELETE_USER_FORM_KEYS, EMAIL_FORM_KEYS, PROFILE_FORM_KEYS } from './constants';

export type UserContact = {
    fkAgente?: number;
    /** prefix is included */
    numero?: string;
    tipo?: string;
    preferito?: boolean;
    idContattoAgente?: number;
    pubblico?: boolean;
    /** the same as numero, with spaces between number-groups */
    fNumero?: string;
    prefisso?: string;
    fNumeroSenzaPrefisso?: string;
    /** like 'it' */
    shortCode?: string;
};

export type BioSubmitDataType = {
    selected: string;
    id: string;
    lang: string;
    description: string;
};

export type ProfileFormValues = {
    [PROFILE_FORM_KEYS.AGENT_ID]: number;
    [PROFILE_FORM_KEYS.FIRST_NAME]: string;
    [PROFILE_FORM_KEYS.LAST_NAME]: string;
    [PROFILE_FORM_KEYS.EMAIL]: string;
    [PROFILE_FORM_KEYS.BIRTH_DATE]: string;
    [PROFILE_FORM_KEYS.SEX]: string | null; // converted from number, 0=male 1=female
    [PROFILE_FORM_KEYS.FISCAL_CODE]: string;
    [PROFILE_FORM_KEYS.ADDRESS]: string;
    [PROFILE_FORM_KEYS.POST_CODE]: string;
    [PROFILE_FORM_KEYS.REA_CODE]: string;
    [PROFILE_FORM_KEYS.VAT]: string;
    [PROFILE_FORM_KEYS.ASSOCIATION_ID]: number | string;
    [PROFILE_FORM_KEYS.CITY_OBJ]: {
        id: string;
        name: string;
    };
    [PROFILE_FORM_KEYS.CONTACTS]: UserContact[];
    [PROFILE_FORM_KEYS.BIOGRAPHY]: BioSubmitDataType[];
};

export type UserAssociation = {
    id: string;
    description: string;
};

export type CountryCallingCode = {
    /** example: 'Italia */
    name?: string;
    /** example: '+39' */
    callingCode?: string;
    /** example: 'it */
    shortCode?: string;
};

export type EmailFormKeysType = (typeof EMAIL_FORM_KEYS)[keyof typeof EMAIL_FORM_KEYS];

export type DeleteUserFormKeysType = (typeof DELETE_USER_FORM_KEYS)[keyof typeof DELETE_USER_FORM_KEYS];
