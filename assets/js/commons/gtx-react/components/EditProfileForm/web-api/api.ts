import { http } from '@pepita/http';
import { serialize } from '@pepita/querystring';

import { DeleteUserFormKeysType, EmailFormKeysType, ProfileFormValues } from '../types';
import {
    CHANGE_EMAIL_USER_ENDPOINT,
    CITY_AUTOCOMPLETE_API,
    DELETE_USER_ENDPOINT,
    UPDATE_USER_ENDPOINT,
} from './endpoints';

const headers = { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' };

export const getCitiesAutocompleteApi = async (query: string) => {
    if (!query) {
        return;
    }

    const searchParams = {
        query,
        idNaz: 'it',
        // results: 5, value 5 is ignored by api
        results: 10,
        sort: '+bestMatch',
    };

    const response = await http.get(CITY_AUTOCOMPLETE_API, { searchParams }).json();

    return response?.data || [];
};

export const changeEmailApi = async (data: Record<EmailFormKeysType, string>) => {
    const response = await fetch(CHANGE_EMAIL_USER_ENDPOINT.replace(':userId', `${data.idAgente}`), {
        method: 'PATCH',
        body: serialize(data),
        headers,
    });
    return await response.json();
};

export const deleteUserApi = async (data: Record<DeleteUserFormKeysType, string>): Promise<boolean> => {
    const { idAgente, password } = data;
    const response = await fetch(DELETE_USER_ENDPOINT.replace(':userId', `${idAgente}`), {
        method: 'DELETE',
        body: serialize({ password }),
        headers,
    });

    if (!response.ok) {
        const data = await response.json();

        throw data.message;
    }

    return response.ok;
};

export const updateUserApi = async (data: ProfileFormValues) => {
    const response = await fetch(UPDATE_USER_ENDPOINT.replace(':userId', `${data.idAgente}`), {
        method: 'PUT',
        body: serialize(data),
        headers,
    });
    return await response.json();
};
