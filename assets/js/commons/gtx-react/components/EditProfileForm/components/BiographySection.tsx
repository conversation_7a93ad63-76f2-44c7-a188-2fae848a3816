import { Select } from '@gx-design/select';
import { Tabs, TabsItem } from '@gx-design/tabs';
import { Tooltip } from '@gx-design/tooltip';
import { useMediaMatch } from '@gx-design/use-media-match';
import { trans } from '@pepita/babelfish';
import { useFormikContext } from 'formik';
import { FC, useEffect, useState } from 'react';

import { GxFkTextarea } from 'gtx-react/components/gx-formik';
import { EditProfileFormProps } from '../EditProfileForm';
import {
    BIO_KEY_REGEX,
    DESC_MAX_LENGTH,
    PROFILE_FORM_KEYS,
} from '../constants';
import { RowDoubleWrapper } from './';
import { checkAndOverrideFlagIcon } from 'gtx-react/utils/checkAndOverrideFlagIcon';

type Props = {
    languages: EditProfileFormProps['languages'];
};
export const BiographySection: FC<Props> = ({ languages }) => {
    const extraLargeDesktop = useMediaMatch('extraLargeDesktop');
    const largeDesktop = useMediaMatch('largeDesktop');

    const [activeTab, setActiveTab] = useState<string>(languages[0]);
    const { errors } = useFormikContext();

    const onTabClick = (elem: string) => () => setActiveTab(elem);
    const onSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) =>
        setActiveTab(event?.target?.value);

    // switching to the correct tab/selectOption if a validation error occurs
    useEffect(() => {
        const errorKeys = Object.keys(errors);
        for (let i = 0; i < errorKeys.length; i++) {
            const key = errorKeys[i];
            if (key.match(BIO_KEY_REGEX)) {
                const locale = key.split('_')[1];
                setActiveTab(locale);
            }
        }
    }, [errors]);

    return (
        <div className="gx-section">
            <div className="gx-title-1">
                {trans('label.web_portals_biography')}
            </div>
            <RowDoubleWrapper fullLine>
                {!largeDesktop ? (
                    <Select
                        label=""
                        isLabelVisible={false}
                        options={languages.map((lang) => ({
                            label: trans(`label.${lang}`),
                            value: lang,
                        }))}
                        value={activeTab}
                        onChange={onSelectChange}
                    />
                ) : (
                    <Tabs className="tab-translations">
                        {languages.map((lang) => (
                            <TabsItem
                                key={`country-tab-${lang}`}
                                text={trans(`label.${lang}`)}
                                startElement={
                                    <Tooltip
                                        position="top"
                                        text={trans(`label.${lang}`)}
                                    >
                                        <div
                                            className={`flag-icon flag-icon-${checkAndOverrideFlagIcon(
                                                lang
                                            )}`}
                                        />
                                    </Tooltip>
                                }
                                active={lang === activeTab}
                                onClick={
                                    lang === activeTab
                                        ? undefined
                                        : onTabClick(lang)
                                }
                            />
                        ))}
                    </Tabs>
                )}
                <GxFkTextarea
                    label={trans('label.description')}
                    id="input-description"
                    isLabelVisible={false}
                    name={`${PROFILE_FORM_KEYS.BIO_PREFIX}${activeTab}`}
                    maxLength={DESC_MAX_LENGTH}
                    rows={8}
                    placeholder={`${trans('label.insert_desc_in')} ${trans(
                        `label.${activeTab}`
                    )}`}
                />
            </RowDoubleWrapper>
        </div>
    );
};
