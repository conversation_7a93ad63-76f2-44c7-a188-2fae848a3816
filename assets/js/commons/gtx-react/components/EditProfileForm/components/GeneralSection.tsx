import { Alert } from '@gx-design/alert';
import { Icon } from '@gx-design/icon';
import { trans } from '@pepita/babelfish';
import { useFormikContext } from 'formik';
import { FC, useEffect } from 'react';

import { GxFkButtonInput, GxFkInput } from 'gtx-react/components/gx-formik';
import { RowDoubleWrapper, RowSingleWrapper } from './';
import { PROFILE_FORM_KEYS } from './../constants';

type Props = {
    /** This should be `true` when `user.titolare` is `true` */
    isMainAgent?: boolean;
    /** user.nuovaEmail */
    storedNewEmail?: string;
    openEmailModal: () => void;
    newEmail?: string;
};

export const GeneralSection: FC<Props> = ({
    openEmailModal,
    newEmail,
    isMainAgent,
    storedNewEmail,
}) => {
    const { setFieldValue } = useFormikContext();

    useEffect(() => {
        if (newEmail) {
            setFieldValue(PROFILE_FORM_KEYS.EMAIL, newEmail);
        }
    }, [newEmail, setFieldValue]);

    return (
        <div className="gx-section">
            {/* TODO: add SELECT to change user role when migrating the whole section "impostazioni/utenti"; see file 'templates/settings/settings/partials/profile/edit-form.html.twig */}
            <RowDoubleWrapper>
                <GxFkInput
                    required
                    name={PROFILE_FORM_KEYS.FIRST_NAME}
                    label={trans('label.name')}
                    id="input-name"
                    type="text"
                    placeholder={trans('label.insert_name')}
                />
                <GxFkInput
                    required
                    name={PROFILE_FORM_KEYS.LAST_NAME}
                    label={trans('label.surname')}
                    id="input-surname"
                    type="text"
                    placeholder={trans('label.insert_surname')}
                />
            </RowDoubleWrapper>
            <RowSingleWrapper>
                {isMainAgent ? (
                    <GxFkInput
                        disabled
                        required
                        name={PROFILE_FORM_KEYS.EMAIL}
                        label={trans('label.mail')}
                        id="input-mail"
                        type="email"
                        placeholder={trans('label.example_user_mail')}
                    />
                ) : (
                    <GxFkButtonInput
                        required
                        name={PROFILE_FORM_KEYS.EMAIL}
                        label={trans('label.mail')}
                        id="input-mail"
                        type="email"
                        placeholder={trans('label.example_user_mail')}
                        buttonContent={<Icon name="pencil" />}
                        onButtonClick={openEmailModal}
                        onFocusCapture={openEmailModal}
                    />
                )}
            </RowSingleWrapper>
            {storedNewEmail && (
                <Alert style="info" withMarginBottom>
                    <span
                        dangerouslySetInnerHTML={{
                            __html: trans('settings.edit_user.mail_verified', {
                                nuovaEmail: storedNewEmail,
                            }),
                        }}
                    />
                </Alert>
            )}
        </div>
    );
};
