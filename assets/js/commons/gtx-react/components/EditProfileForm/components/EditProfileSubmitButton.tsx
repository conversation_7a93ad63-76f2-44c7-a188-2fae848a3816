import { Button } from '@gx-design/button';
import { trans } from '@pepita/babelfish';
import { useFormikContext } from 'formik';
import { FC } from 'react';

import { useFormikFocusErroredFieldOnSubmit } from 'gtx-react/hooks/useFormikFocusErroredFieldOnSubmit';
import {
    ImmovoxConfirmActionModal,
    useImmovoxConfirmAction,
} from 'gtx-react/components/formik/ImmovoxConfirmAction';

type Props = { voxNumber?: string };
export const EditProfileSubmitButton: FC<Props> = ({ voxNumber }) => {
    const { dirty } = useFormikContext();
    const { wrapperFn, ...rest } = useImmovoxConfirmAction({
        voxNumber,
        fieldsToCheck: [
            { fieldName: 'contatti', isArray: true, subfieldName: 'numero' },
        ],
    });

    useFormikFocusErroredFieldOnSubmit();

    return (
        <>
            <Button variant="accent" onClick={wrapperFn} disabled={!dirty}>
                <span>{trans('label.save')}</span>
            </Button>
            <ImmovoxConfirmActionModal action="edit_save" {...rest} />
        </>
    );
};
