import { SelectProps } from '@gx-design/select';
import { trans } from '@pepita/babelfish';
import { FC } from 'react';

import { GxFkInput, GxFkSelect } from 'gtx-react/components/gx-formik';
import { PROFILE_FORM_KEYS } from './../constants';
import { RowDoubleWrapper, RowSingleWrapper } from './Wrappers';
import { EditProfileFormProps } from '../EditProfileForm';

const generateAssociations = (
    associations: EditProfileFormProps['associations']
): SelectProps['options'] => {
    return associations
        .filter((elem) => elem.id !== '-1')
        .map((elem) => ({
            label: elem.description,
            value: parseInt(elem.id),
        }));
};

type Props = {
    associations: EditProfileFormProps['associations'];
};

export const AgentDataSection: FC<Props> = ({ associations }) => {
    const options = generateAssociations(associations);

    return (
        <div className="gx-section">
            <h4 className="gx-title-1">{trans('label.agent_data')}</h4>
            <RowDoubleWrapper>
                <GxFkInput
                    name={PROFILE_FORM_KEYS.REA_CODE}
                    label={trans('label.rea_code')}
                    id="input-rea-code"
                    maxLength={100}
                    placeholder={trans('label.insert_rea_code')}
                />
                <GxFkSelect
                    options={options}
                    label={trans('label.association')}
                    id="input-association"
                    placeholder={trans('label.none_2')}
                    name={PROFILE_FORM_KEYS.ASSOCIATION_ID}
                />
            </RowDoubleWrapper>
            <RowSingleWrapper>
                <GxFkInput
                    name={PROFILE_FORM_KEYS.VAT}
                    label={trans('label.vat')}
                    id="input-vat"
                    minLength={2}
                    maxLength={11}
                    placeholder={trans('label.insert_vat_number')}
                />
            </RowSingleWrapper>
        </div>
    );
};
