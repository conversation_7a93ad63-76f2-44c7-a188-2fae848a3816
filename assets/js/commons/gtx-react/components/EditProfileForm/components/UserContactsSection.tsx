import { Button } from '@gx-design/button';
import { Modal } from '@gx-design/modal';
import { trans } from '@pepita/babelfish';
import { FieldArray, useFormikContext } from 'formik';
import { FC, useState } from 'react';

import { ImmovoxConfirmActionModal } from 'gtx-react/components/formik/ImmovoxConfirmAction';
import { EditProfileFormProps } from '../EditProfileForm';
import type { ProfileFormValues, UserContact } from '../types';
import { ContactRow, RowSingleWrapper } from './';
import { EMPTY_CONTACT, PROFILE_FORM_KEYS } from './../constants';

/** Using parts of the translation system strings to easily populate error message into the modal. */
type ErrorReason = 'removeMainError' | 'editMainError';

type Props = {
    countryCallingCodes: EditProfileFormProps['countryCallingCodes'];
    voxNumber?: string;
};

export const UserContactsSection: FC<Props> = ({
    countryCallingCodes,
    voxNumber,
}) => {
    const [showModal, setShowModal] = useState<boolean>(false);
    const [contactIndex, setContactIndex] = useState<number | null>(null);
    const [errorReason, setErrorReason] = useState<ErrorReason | null>(null);
    const [isImmovoxModalOpen, setIsImmovoxModalOpen] =
        useState<boolean>(false);

    const { values, setFieldValue } = useFormikContext<ProfileFormValues>();

    const onSetFavouriteClick = (idx: number) => () => {
        const numberOfContacts = values[PROFILE_FORM_KEYS.CONTACTS].length;
        const newContacts: UserContact[] = [];
        for (let i = 0; i < numberOfContacts; i++) {
            const preferito = i === idx;
            const newContact: UserContact = {
                ...values[PROFILE_FORM_KEYS.CONTACTS][i],
                preferito,
            };
            newContacts.push(newContact);
        }
        setFieldValue(PROFILE_FORM_KEYS.CONTACTS, newContacts);
    };

    const onDeleteClick = (index: number, remove: Function) => () => {
        const contact: UserContact = values[PROFILE_FORM_KEYS.CONTACTS][index];

        if (contact['fNumeroSenzaPrefisso'] === '' && !contact['preferito']) {
            if (values[PROFILE_FORM_KEYS.CONTACTS].length > 1) {
                remove(index);
            } else {
                setFieldValue(PROFILE_FORM_KEYS.CONTACTS, [EMPTY_CONTACT]);
            }
        } else if (contact['preferito']) {
            setShowModal(true);
            setErrorReason('removeMainError');
        } else if (contact['numero'] && contact['numero'] === voxNumber) {
            setIsImmovoxModalOpen(true);
        } else {
            setContactIndex(index);
            setShowModal(true);
        }
    };

    const onDeleteConfirm = (remove: Function) => () => {
        if (values[PROFILE_FORM_KEYS.CONTACTS].length > 1) {
            remove(contactIndex);
        } else {
            setFieldValue(PROFILE_FORM_KEYS.CONTACTS, [EMPTY_CONTACT]);
        }
        setShowModal(false);
        setIsImmovoxModalOpen(false);
        setContactIndex(null);
    };

    const onResetMainNumber = () => {
        setShowModal(true);
        setErrorReason('editMainError');
    };

    const onModalClose = () => {
        setShowModal(false);
        setContactIndex(null);
        setErrorReason(null);
    };

    const onAddContactClick = (push: Function) => () => push(EMPTY_CONTACT);

    const onImmovoxClose = () => setIsImmovoxModalOpen(false);

    return (
        <div className="gx-section">
            <h4 className="gx-title-1">{trans('label.phone_numbers')}</h4>
            <FieldArray
                name={PROFILE_FORM_KEYS.CONTACTS}
                render={({ push, remove }) => (
                    <div>
                        {values[PROFILE_FORM_KEYS.CONTACTS].map(
                            (contact: UserContact, idx: number) => (
                                <ContactRow
                                    key={`contact-${idx}`}
                                    index={idx}
                                    {...contact}
                                    countryCallingCodes={countryCallingCodes}
                                    onSetFavouriteClick={onSetFavouriteClick}
                                    remove={remove}
                                    onDeleteClick={onDeleteClick}
                                    onResetMainNumber={onResetMainNumber}
                                />
                            )
                        )}
                        <RowSingleWrapper fullLine />
                        <RowSingleWrapper fullLine>
                            <Button onClick={onAddContactClick(push)}>
                                {trans('label.add_number')}
                            </Button>
                        </RowSingleWrapper>
                        <Modal
                            size="small"
                            isOpen={showModal}
                            onClose={onModalClose}
                            onConfirm={
                                typeof contactIndex === 'number'
                                    ? onDeleteConfirm(remove)
                                    : onModalClose
                            }
                            title={trans(
                                typeof contactIndex === 'number'
                                    ? 'contacts.removeConfirm.title'
                                    : `contacts.${errorReason}.title`
                            )}
                            footer={
                                typeof contactIndex === 'number' ? (
                                    <>
                                        <Button
                                            variant="ghost"
                                            onClick={onModalClose}
                                        >
                                            {trans('label.cancel')}
                                        </Button>
                                        <Button
                                            variant="accent"
                                            onClick={onDeleteConfirm(remove)}
                                        >
                                            {trans('label.confirm')}
                                        </Button>
                                    </>
                                ) : (
                                    <Button
                                        variant="accent"
                                        onClick={onModalClose}
                                    >
                                        {trans('label.ok')}
                                    </Button>
                                )
                            }
                        >
                            <div
                                dangerouslySetInnerHTML={{
                                    __html: trans(
                                        typeof contactIndex === 'number'
                                            ? 'contacts.removeConfirm.message'
                                            : `contacts.${errorReason}.message`
                                    ),
                                }}
                            />
                        </Modal>
                        <ImmovoxConfirmActionModal
                            isOpen={isImmovoxModalOpen}
                            onClose={onImmovoxClose}
                            onConfirm={onDeleteConfirm(remove)}
                            previousValue={voxNumber}
                            action="remove"
                        />
                    </div>
                )}
            />
        </div>
    );
};
