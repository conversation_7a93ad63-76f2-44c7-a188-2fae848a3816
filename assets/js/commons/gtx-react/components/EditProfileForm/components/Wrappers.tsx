import clsx from 'clsx';
import type { FC, PropsWithChildren } from 'react';

type WrapperProps = {
    fullLine?: boolean;
};

export const RowSingleWrapper: FC<PropsWithChildren<WrapperProps>> = ({
    children,
    fullLine = false,
}) => (
    <div className="gx-row">
        <div className={clsx(['gx-col-xs-12', { 'gx-col-sm-6': !fullLine }])}>
            <div className="gx-box-row">{children}</div>
        </div>
    </div>
);

type RowDoubleWrapperProps = WrapperProps & {
    children: [React.ReactNode, React.ReactNode];
};

export const RowDoubleWrapper: FC<RowDoubleWrapperProps> = ({
    children,
    fullLine = false,
}) => (
    <div className="gx-row">
        <div className={clsx(['gx-col-xs-12', { 'gx-col-sm-6': !fullLine }])}>
            <div className="gx-box-row">{children[0]}</div>
        </div>
        <div className={clsx(['gx-col-xs-12', { 'gx-col-sm-6': !fullLine }])}>
            <div className="gx-box-row">{children[1]}</div>
        </div>
    </div>
);
