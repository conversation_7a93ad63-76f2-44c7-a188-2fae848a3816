import { HelperText } from '@gx-design/helper-text';
import { InputProps } from '@gx-design/input';
import clsx from 'clsx';
import { useField } from 'formik';

import { useFormikValidateFieldOnChangeIfError } from 'gtx-react/hooks/useFormikValidateFieldOnChangeIfError';

/**
 * intercepting optional custom `onChange`, executing it along with the original `onChange` from formik
 **/
const onChange =
    (onChangeFromProps: Function, onChangeFromFormik: Function) =>
    (e: React.ChangeEvent<HTMLElement>): void => {
        onChangeFromFormik(e);
        if (onChangeFromProps && typeof onChangeFromProps === 'function') {
            onChangeFromProps(e);
        }
    };

type PhoneWithPrefixesInputProps = Omit<
    InputProps,
    'onChange' | 'isLabelVisible' | 'label' | 'required' | 'tooltipHelper'
> & {
    leftComponent: React.ReactNode;
    name: string;
    onChange?: (_event: React.ChangeEvent<HTMLInputElement>) => void;
    index: number;
};

export const PhoneWithPrefixesInput: React.FC<PhoneWithPrefixesInputProps> = ({
    leftComponent,
    index,
    ...rest
}) => {
    const [fieldProps, fieldMeta] = useField(rest.name);

    useFormikValidateFieldOnChangeIfError(rest.name);

    return (
        <>
            <div className="gx-input-select-wrapper">
                {leftComponent}
                <input
                    data-testid={`input-phone-${index}`}
                    className={clsx([
                        'gx-input',
                        {
                            'gx-input--negative': fieldMeta.error,
                        },
                    ])}
                    {...fieldProps}
                    {...rest}
                    onChange={onChange(
                        rest.onChange ? rest.onChange : () => {},
                        fieldProps.onChange
                    )}
                />
            </div>
            {fieldMeta.error && (
                <HelperText text={fieldMeta.error} style="error" />
            )}
        </>
    );
};
