import { RadioGroup } from '@gx-design/radio';
import { trans } from '@pepita/babelfish';
import { useFormikContext } from 'formik';
import { FC, useMemo } from 'react';

import { DatePickerInput } from 'gtx-react/components/DatePicker';
import { GxFkInput, GxFkRadio } from 'gtx-react/components/gx-formik';
import AutocompleteFormik from 'gtx-react/containers/AutocompleteFormik';
import type { ProfileFormValues } from '../types';
import { getCitiesAutocompleteApi } from '../web-api/api';
import { RowDoubleWrapper, RowSingleWrapper } from './';
import { PROFILE_FORM_KEYS } from './../constants';

export const PersonalDataSection: FC = () => {
    const TODAY = useMemo(() => new Date(), []);
    const { values, setFieldValue } = useFormikContext<ProfileFormValues>();

    const formatAutocompleteValue = (item) =>
        `${item.name}${
            !item.chiefTown && item.province?.name
                ? ` (${item.province?.name})`
                : ''
        }`;

    const onCityChange = async (
        fieldName?: string,
        data?: Record<string, any>
    ) => {
        if (data?.id) {
            setFieldValue(PROFILE_FORM_KEYS.CITY_OBJ, {
                id: `${data.id}`,
                name: data.name,
            });
        } else if (fieldName) {
            // here when user clicks the 'X' to reset value
            // data undefined, fieldName defined
            setFieldValue(PROFILE_FORM_KEYS.CITY_OBJ, {
                id: '',
                name: '',
            });
        }
    };

    return (
        <div className="gx-section">
            <h4 className="gx-title-1">{trans('label.personal_data')}</h4>
            <div className="gx-row">
                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row gx-box-row--spaceBetween">
                        <DatePickerInput
                            name={PROFILE_FORM_KEYS.BIRTH_DATE}
                            label={trans('label.date_of_birth')}
                            placeholder={trans('label.date_format')}
                            defaultCalendarView="decade"
                            maxDate={TODAY}
                            id="input-date-of-birth"
                        />
                        <RadioGroup
                            variant="button"
                            label={trans('label.gender')}
                            isSmall
                        >
                            <GxFkRadio
                                name={PROFILE_FORM_KEYS.SEX}
                                label={trans('label.male')}
                                id="input-male"
                                value="0"
                            />
                            <GxFkRadio
                                name={PROFILE_FORM_KEYS.SEX}
                                label={trans('label.female')}
                                id="input-female"
                                value="1"
                            />
                        </RadioGroup>
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        <GxFkInput
                            name={PROFILE_FORM_KEYS.FISCAL_CODE}
                            label={trans('label.fiscal_code')}
                            id="input-fiscal-code"
                            type="email"
                            placeholder={trans('label.insert_fiscal_code')}
                            minLength={16}
                            maxLength={16}
                        />
                    </div>
                </div>
            </div>
            <RowSingleWrapper>
                <AutocompleteFormik
                    name={`${PROFILE_FORM_KEYS.CITY_OBJ}.id`}
                    key={`${PROFILE_FORM_KEYS.CITY_OBJ}.id`}
                    label={trans('label.municipality')}
                    data-testid="input-municipality"
                    placeholder={trans('label.municipality_required')}
                    defaultValue={values[PROFILE_FORM_KEYS.CITY_OBJ]}
                    getAutocompleteApi={getCitiesAutocompleteApi}
                    minLength={2}
                    onChange={onCityChange}
                    classWrap="gx-box-row"
                    itemKey="id"
                    selectedLabelKey="name"
                    formatFn={formatAutocompleteValue}
                />
            </RowSingleWrapper>
            <RowDoubleWrapper>
                <GxFkInput
                    name={PROFILE_FORM_KEYS.ADDRESS}
                    label={trans('label.residence_address')}
                    id="input-residence-address"
                    type="text"
                    placeholder={trans('label.insert_residence_address')}
                />
                <GxFkInput
                    name={PROFILE_FORM_KEYS.POST_CODE}
                    label={trans('label.postal_code')}
                    id="input-postal-code"
                    placeholder={trans('label.postal_code')}
                    minLength={5}
                    maxLength={5}
                />
            </RowDoubleWrapper>
        </div>
    );
};
