import gtxLoggedUser from '@getrix/common/js/gtx-logged-user';
import { Button } from '@gx-design/button';
import { Modal, ModalProps } from '@gx-design/modal';
import { trans } from '@pepita/babelfish';
import { Form, Formik, useFormikContext } from 'formik';
import { FC } from 'react';
import * as Yup from 'yup';

import { Loader } from 'gtx-react/components';
import { GxFkInput } from 'gtx-react/components/gx-formik';
import { EMAIL_FORM_KEYS } from '../constants';
import { EmailFormKeysType } from '../types';
import { RowSingleWrapper } from './Wrappers';

const VALIDATION_SCHEMA = Yup.object().shape({
    [EMAIL_FORM_KEYS.EMAIL]: Yup.string()
        .required(trans('label.required_value'))
        .email(trans('label.insert_valid_mail_2')),
    [EMAIL_FORM_KEYS.EMAIL_REPEAT]: Yup.string().when(
        `${EMAIL_FORM_KEYS.EMAIL}`,
        {
            is: (value: string) => value !== '',
            then: (schema) =>
                schema
                    .required(trans('label.required_value'))
                    .email(trans('label.insert_valid_mail_2'))
                    .oneOf(
                        [Yup.ref(`${EMAIL_FORM_KEYS.EMAIL}`)],
                        trans('label.repeat_mail.error')
                    ),
        }
    ),
    [EMAIL_FORM_KEYS.PASSWORD]: Yup.string().required(
        trans('label.required_value')
    ),
});

const VALIDATION_SCHEMA_BACKOFFICE = Yup.object().shape({
    [EMAIL_FORM_KEYS.EMAIL]: Yup.string()
        .required(trans('label.required_value'))
        .email(trans('label.insert_valid_mail_2')),
    [EMAIL_FORM_KEYS.EMAIL_REPEAT]: Yup.string().when(
        `${EMAIL_FORM_KEYS.EMAIL}`,
        {
            is: (value: string) => value !== '',
            then: (schema) =>
                schema
                    .required(trans('label.required_value'))
                    .email(trans('label.insert_valid_mail_2'))
                    .oneOf(
                        [Yup.ref(`${EMAIL_FORM_KEYS.EMAIL}`)],
                        trans('label.repeat_mail.error')
                    ),
        }
    ),
});

const ChangeEmailModalFooter: FC<{
    onClose: ModalProps['onClose'];
}> = ({ onClose }) => {
    const { validateForm, submitForm, setSubmitting } = useFormikContext();

    const onSubmitWrapper = async () => {
        const errors = await validateForm();
        if (Object.keys(errors)?.length) {
            return;
        }
        setSubmitting(true);
        await submitForm();
        setSubmitting(false);
    };

    return (
        <>
            <Button variant="ghost" onClick={onClose}>
                {trans('label.cancel')}
            </Button>
            <Button type="submit" variant="accent" onClick={onSubmitWrapper}>
                {trans('label.send_verification')}
            </Button>
        </>
    );
};

type ChangeEmailModalProps = {
    userId: number;
    isOpen: ModalProps['isOpen'];
    onClose: ModalProps['onClose'];
    onSubmit: (_x: any) => void;
    isSubmitting: boolean;
};
export const ChangeEmailModal: FC<ChangeEmailModalProps> = ({
    userId,
    isOpen,
    onClose,
    onSubmit,
    isSubmitting,
}) => {
    const INITIAL_VALUES: Record<EmailFormKeysType, string> = {
        [EMAIL_FORM_KEYS.AGENT_ID]: userId.toString(),
        [EMAIL_FORM_KEYS.EMAIL]: '',
        [EMAIL_FORM_KEYS.EMAIL_REPEAT]: '',
        [EMAIL_FORM_KEYS.PASSWORD]: '',
    };

    const INITIAL_VALUES_BACKOFFICE: Omit<
        Record<EmailFormKeysType, string>,
        'password'
    > = {
        [EMAIL_FORM_KEYS.AGENT_ID]: userId.toString(),
        [EMAIL_FORM_KEYS.EMAIL]: '',
        [EMAIL_FORM_KEYS.EMAIL_REPEAT]: '',
    };

    return (
        <Formik
            initialValues={
                Boolean(gtxLoggedUser('roles.ROLE_USER_BACKOFFICE'))
                    ? INITIAL_VALUES_BACKOFFICE
                    : INITIAL_VALUES
            }
            onSubmit={onSubmit}
            validateOnMount={false}
            validateOnBlur={false}
            validateOnChange={false}
            validationSchema={
                Boolean(gtxLoggedUser('roles.ROLE_USER_BACKOFFICE'))
                    ? VALIDATION_SCHEMA_BACKOFFICE
                    : VALIDATION_SCHEMA
            }
        >
            <Form noValidate>
                <Modal
                    isOpen={isOpen}
                    onClose={onClose}
                    title={trans('label.edit_email')}
                    size="small"
                    footer={<ChangeEmailModalFooter onClose={onClose} />}
                >
                    <Loader
                        loading={isSubmitting}
                        fixedOverlay={false}
                        centered={false}
                    />
                    <RowSingleWrapper fullLine>
                        <GxFkInput
                            name={EMAIL_FORM_KEYS.EMAIL}
                            label={trans('label.new_email')}
                            id="input-new-email"
                            type="email"
                            placeholder={trans('label.example_user_mail')}
                            required
                        />
                    </RowSingleWrapper>
                    <RowSingleWrapper fullLine>
                        <GxFkInput
                            name={EMAIL_FORM_KEYS.EMAIL_REPEAT}
                            label={trans('label.confirm_new_email')}
                            id="input-confirm-new-email"
                            type="email"
                            placeholder={trans('label.example_user_mail')}
                            required
                        />
                    </RowSingleWrapper>
                    {!gtxLoggedUser('roles.ROLE_USER_BACKOFFICE') && (
                        <RowSingleWrapper fullLine>
                            <GxFkInput
                                name={EMAIL_FORM_KEYS.PASSWORD}
                                label={trans('label.your_password')}
                                id="input-your-password"
                                type="password"
                                placeholder={trans('label.password')}
                                required
                            />
                        </RowSingleWrapper>
                    )}
                </Modal>
            </Form>
        </Formik>
    );
};
