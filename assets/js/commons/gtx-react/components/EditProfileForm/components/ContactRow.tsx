import { ActionList, ActionListItem } from '@gx-design/action-list';
import { Button } from '@gx-design/button';
import { Dropdown } from '@gx-design/dropdown';
import { Icon } from '@gx-design/icon';
import { Tooltip } from '@gx-design/tooltip';
import { trans } from '@pepita/babelfish';
import { useFormikContext } from 'formik';

import { GxFkCheckbox } from 'gtx-react/components/gx-formik';
import type {
    CountryCallingCode,
    ProfileFormValues,
    UserContact,
} from '../types';
import { PhoneWithPrefixesInput } from './';
import { PROFILE_FORM_KEYS } from './../constants';
import { checkAndOverrideFlagIcon } from 'gtx-react/utils/checkAndOverrideFlagIcon';

export type ContactRowProps = UserContact & {
    index: number;
    countryCallingCodes: Array<CountryCallingCode>;
    onSetFavouriteClick: (_idx: number) => () => void;
    remove: (_idx: number) => UserContact | undefined;
    onDeleteClick: (_idx: number, _remove: Function) => () => void;
    onResetMainNumber: () => void;
};

export const ContactRow: React.FC<ContactRowProps> = ({
    countryCallingCodes,
    shortCode,
    prefisso,
    preferito,
    onSetFavouriteClick,
    index,
    onDeleteClick,
    onResetMainNumber,
    remove,
}) => {
    const { setFieldValue, initialValues, values } =
        useFormikContext<ProfileFormValues>();

    const onPrefixChange =
        ({ shortCode, callingCode }: CountryCallingCode) =>
        () => {
            setFieldValue(
                `${PROFILE_FORM_KEYS.CONTACTS}.${index}.shortCode`,
                shortCode
            );
            setFieldValue(
                `${PROFILE_FORM_KEYS.CONTACTS}.${index}.prefisso`,
                callingCode
            );
        };

    const onNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (preferito && !e.currentTarget.value) {
            onResetMainNumber();
            setFieldValue(
                `${PROFILE_FORM_KEYS.CONTACTS}.${index}.fNumeroSenzaPrefisso`,
                initialValues[PROFILE_FORM_KEYS.CONTACTS][index][
                    'fNumeroSenzaPrefisso'
                ]
            );
        } else {
            // need to merge prefix and number without prefix for immovox check
            const prefix =
                values[PROFILE_FORM_KEYS.CONTACTS][index]['prefisso'];
            const numberWithoutPrefix =
                values[PROFILE_FORM_KEYS.CONTACTS][index][
                    'fNumeroSenzaPrefisso'
                ];
            setFieldValue(
                `${PROFILE_FORM_KEYS.CONTACTS}.${index}.numero`,
                `${prefix}${numberWithoutPrefix}`
            );
        }
    };

    return (
        <div className="gx-row" data-testid="wrapper-phone-input">
            <div className="gx-col-xs-12 gx-col-sm-6">
                <div className="sts-user-phone">
                    <PhoneWithPrefixesInput
                        leftComponent={
                            <Dropdown
                                buttonClassName="gx-select"
                                position="topLeft"
                                showCaret
                                maxHeight
                                buttonContent={
                                    <>
                                        <div
                                            className={`flag-icon flag-icon-${checkAndOverrideFlagIcon(
                                                shortCode || ''
                                            )}`}
                                        />
                                        <span
                                            data-testid={`select-phone-prefix-${index}`}
                                        >
                                            {prefisso}
                                        </span>
                                    </>
                                }
                            >
                                <ActionList>
                                    {countryCallingCodes.map((elem, idx) => (
                                        <ActionListItem
                                            data-testid="select-phone-prefix-option"
                                            key={`al-country-${idx}`}
                                            startElement={
                                                <div
                                                    className={`flag-icon flag-icon-${checkAndOverrideFlagIcon(
                                                        elem.shortCode || ''
                                                    )}`}
                                                />
                                            }
                                            text={`${elem.name} ${elem.callingCode}`}
                                            active={
                                                elem.shortCode === shortCode
                                            }
                                            onClick={onPrefixChange(elem)}
                                        />
                                    ))}
                                </ActionList>
                            </Dropdown>
                        }
                        type="text"
                        name={`${PROFILE_FORM_KEYS.CONTACTS}.${index}.fNumeroSenzaPrefisso`}
                        // onChange={preferito ? onMainNumberChange : undefined}
                        onChange={onNumberChange}
                        index={index}
                    />
                </div>
            </div>
            <div className="gx-col-xs-12 gx-col-sm-6">
                <div className="gx-box-row gx-box-row--spaceBetween sts-user-phone-visibility">
                    <GxFkCheckbox
                        variant="button"
                        label={trans('label.visible_on_portals')}
                        name={`${PROFILE_FORM_KEYS.CONTACTS}.${index}.pubblico`}
                        data-testid={`check-phone-visible-${index}`}
                    />
                    <div className="gx-multiButton">
                        <Tooltip
                            position="top"
                            text={trans(
                                preferito
                                    ? 'label.main_number'
                                    : 'label.make_main_number'
                            )}
                        >
                            <Button
                                iconOnly
                                onClick={onSetFavouriteClick(index)}
                                data-testid={`button-contact${
                                    preferito ? '-main' : '-not-main'
                                }`}
                            >
                                <Icon
                                    name={preferito ? 'star--active' : 'star'}
                                />
                            </Button>
                        </Tooltip>
                        <Button
                            iconOnly
                            onClick={onDeleteClick(index, remove)}
                            data-testid={`button-delete-contact-${index}`}
                        >
                            <Icon name="bin" />
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};
