import gtxConstants from '@getrix/common/js/gtx-constants';
import { Button } from '@gx-design/button';
import { Modal, ModalProps } from '@gx-design/modal';
import { trans } from '@pepita/babelfish';
import { Form, Formik, useFormikContext } from 'formik';
import { FC } from 'react';
import * as Yup from 'yup';

import { Loader } from 'gtx-react/components';
import { GxFkInput } from 'gtx-react/components/gx-formik';
import { DELETE_USER_FORM_KEYS } from '../constants';
import { DeleteUserFormKeysType } from '../types';
import { RowSingleWrapper } from './';

const VALIDATION_SCHEMA = Yup.object().shape({
    [DELETE_USER_FORM_KEYS.PASSWORD]: Yup.string().required(
        trans('label.insert_password')
    ),
});

const DeleteUserModalFooter: FC<{
    onClose: ModalProps['onClose'];
}> = ({ onClose }) => {
    const { validateForm, submitForm, setSubmitting } = useFormikContext();

    const onSubmitWrapper = async () => {
        const errors = await validateForm();
        if (Object.keys(errors)?.length) {
            return;
        }
        setSubmitting(true);
        await submitForm();
        setSubmitting(false);
    };

    return (
        <>
            <Button variant="ghost" onClick={onClose}>
                {trans('label.cancel')}
            </Button>
            <Button type="submit" variant="accent" onClick={onSubmitWrapper}>
                {trans('label.delete_profile')}
            </Button>
        </>
    );
};

type DeleteUserModalProps = {
    userId: number;
    isOpen: ModalProps['isOpen'];
    onClose: ModalProps['onClose'];
    onSubmit: (_x: any) => void;
    isSubmitting: boolean;
};
export const DeleteUserModal: FC<DeleteUserModalProps> = ({
    userId,
    isOpen,
    onClose,
    onSubmit,
    isSubmitting,
}) => {
    const INITIAL_VALUES: Record<DeleteUserFormKeysType, string> = {
        [DELETE_USER_FORM_KEYS.AGENT_ID]: userId.toString(),
        [DELETE_USER_FORM_KEYS.PASSWORD]: '',
    };

    return (
        <Formik
            initialValues={INITIAL_VALUES}
            onSubmit={onSubmit}
            validateOnMount={false}
            validateOnBlur={false}
            validateOnChange={false}
            validationSchema={VALIDATION_SCHEMA}
        >
            <Form noValidate>
                <Modal
                    isOpen={isOpen}
                    onClose={onClose}
                    title={trans('label.delete_user')}
                    size="small"
                    footer={<DeleteUserModalFooter onClose={onClose} />}
                >
                    <Loader
                        loading={isSubmitting}
                        fixedOverlay={false}
                        centered={false}
                    />
                    <RowSingleWrapper fullLine>
                        <span>
                            {trans('settings.user.remove1', {
                                APP_NAME: gtxConstants('APP_NAME'),
                            })}
                        </span>
                    </RowSingleWrapper>
                    <RowSingleWrapper fullLine>
                        <GxFkInput
                            name={DELETE_USER_FORM_KEYS.PASSWORD}
                            label={trans('label.password')}
                            id="input-password"
                            isLabelVisible={false}
                            type="password"
                            placeholder={trans('label.password')}
                        />
                    </RowSingleWrapper>
                </Modal>
            </Form>
        </Formik>
    );
};
