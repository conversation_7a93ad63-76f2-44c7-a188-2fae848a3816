import gtxConstants from '@getrix/common/js/gtx-constants';
import { UserContact } from './types';
import { GxNavigationBus } from 'lib/gx-navigation-bus';

export const PROFILE_FORM_KEYS = {
    FIRST_NAME: 'nome',
    LAST_NAME: 'cognome',
    EMAIL: 'email',
    BIRTH_DATE: 'dataNascita',
    SEX: 'sesso',
    FISCAL_CODE: 'codiceFiscale',
    ADDRESS: 'indirizzo',
    POST_CODE: 'cap',
    REA_CODE: 'codiceRea',
    VAT: 'partitaIva',
    ASSOCIATION_ID: 'associazione',
    CITY_OBJ: 'comune',
    CONTACTS: 'contatti',
    BIO_PREFIX: 'bio_',
    STATUS: 'status',
    ROLE: 'ruolo',
    AGENT_ID: 'idAgente',
    BIOGRAPHY: 'biografia',
} as const;

export const BIO_KEY_REGEX = new RegExp(`^${PROFILE_FORM_KEYS.BIO_PREFIX}`);

export const DESC_MAX_LENGTH = 3000;

export const EMPTY_CONTACT: UserContact = {
    preferito: false,
    pubblico: true,
    fNumeroSenzaPrefisso: '',
    shortCode: window.gtxConstants.DEFAULT_CONTACT_NUMBER_SHORT_CODE,
    prefisso: window.gtxConstants.DEFAULT_CONTACT_INTERNATIONAL_PREFIX,
    tipo: '1',
};

export const EMAIL_FORM_KEYS = {
    AGENT_ID: 'idAgente',
    EMAIL: 'email',
    EMAIL_REPEAT: 'confemail',
    PASSWORD: 'password',
} as const;

export const DELETE_USER_FORM_KEYS = {
    AGENT_ID: 'idAgente',
    PASSWORD: 'password',
} as const;

export const PHONE_NO_PREFIX_REGEX = new RegExp(gtxConstants('REGEX_PHONE_STRICT_NO_PREFIX'));

export const UPDATE_AGENCY = GxNavigationBus.createEventType('update-agency', 'in');
