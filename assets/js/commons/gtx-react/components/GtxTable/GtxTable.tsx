import React, { FC } from 'react';
import { TableConfigs, TableOptions } from '../GxTable/types';
import { trans } from '@pepita-i18n/babelfish';
import { GxTable } from '../GxTable/GxTable';

const DEFAULT_CONFIGS = {
    itemSelection: false,
    storeThWidth: false,
    emptyState: {
        text: trans('label.no_results_found'),
        image: '/bundles/base/getrix/common/img/empty-state/empty-state.png',
    },
    labels: {
        singleRowSelected: trans('label.selected'),
        moreRowsSelected: trans('label.selected_plural'),
    }
} as TableConfigs;

type GtxTableProps<T> = Omit<TableOptions<T>, 'configs'> & {
    configs?: TableConfigs;
};

export const GtxTable = <T,>(props: GtxTableProps<T>) => {
    return (
        <GxTable
            {...props}
            configs={{
                ...DEFAULT_CONFIGS,
                ...props.configs,
            }}
        />
    );
};
