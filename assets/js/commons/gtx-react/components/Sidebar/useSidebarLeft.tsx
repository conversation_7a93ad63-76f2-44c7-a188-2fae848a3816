import { createContext, useContext } from 'react';

type SidebarLeftContextType = {
    isOpenSidebar: boolean;
    setIsOpenSidebar: (v: boolean) => void;
    enabled: boolean;
    onSidebarToggleClick?: (v: boolean) => void;
};

export const SidebarLeftContext = createContext<
    SidebarLeftContextType | undefined
>(undefined);

export const useSidebarLeft = () => {
    const ctx = useContext(SidebarLeftContext);
    if (!ctx) {
        throw new Error(
            'useSidebarLeft must be used within a SidebarLeftContext.Provider'
        );
    }
    return ctx;
};
