import React, {
    useRef,
    createRef,
    useEffect,
    useState,
    forwardRef,
    useMemo,
    useCallback,
    FC,
} from 'react';
import { createPortal } from 'react-dom';
import classNames from 'classnames';
import useTable from './hooks/useTable';
import useDnD from './hooks/useDnD';
import useTableHorizontalScroll from './hooks/useTableHorizontalScroll';
import { Icon, IconProps } from '@gx-design/icon';
import { Tooltip } from '@gx-design/tooltip';
import { Checkbox } from '@gx-design/checkbox';
import { Button } from '@gx-design/button';
import { Dropdown } from '@gx-design/dropdown';
import { EmptyState } from '@gx-design/empty-state';
import { ActionList, ActionListItem } from '@gx-design/action-list';
import useClickWithExcludedElements from 'gtx-react/hooks/useClickWithExcludedElements';
import { useForwardedRef } from 'gtx-react/hooks/useForwardedRef';
import {
    QuickAction,
    TableActions,
    TableConfigs,
    TableOptions,
    TableRow,
    MenuAction,
    BulkAction,
    TableHeader,
    TableColumn,
    TableConfigsLabels,
    FirstCellAction,
    TableRowCell,
    TableData,
} from './types';
import useTableResize from './hooks/useTableResize';
import { ApiCallOnVisibleProvider } from './ApiCallOnVisibleProvider';
import { useApiCallOnVisibileRef } from './hooks/useApiCallOnVisibleRef';

const LAST_ROWS_OFFSET = 2;

const DEFAULT_LABELS: TableConfigsLabels = {
    singleRowSelected: 'selected',
    moreRowsSelected: 'selected',
};

type ActionsHeaderProps = {
    /**
     * Custom class names.
     */
    className: string;
    /**
     * Custom style object.
     */
    style: any;
    /**
     * Optional bulk actions list.
     */
    bulkActions?: TableActions['bulk'];
    /**
     * Table configs object.
     */
    configs?: TableConfigs;
    /**
     * Labels list object.
     */
    labels: TableConfigsLabels;
    /**
     * Selected rows ids array.
     */
    selectedRows: string[];
    /**
     * Table rows count.
     */
    totalRows: number;
    /**
     * Optional checkbox click handler.
     */
    onCheckboxClickAction?: () => void;
};

type HeaderProps = {
    /**
     * Custom class names.
     */
    className: string;
    /**
     * Custom style object.
     */
    style: any;
    /**
     * If true a bulk row selection checkbox will be displayed.
     */
    hasItemsSelection: boolean;
    /**
     * Headers data object.
     */
    headers: TableColumn[];
    /**
     * Optional actions object.
     */
    menuActions: TableActions['menu'];
    /**
     * Table rows count.
     */
    totalRows: number;
    /**
     * Action to perform when the checkbox is clicked.
     */
    onBulkSelection: () => void;
    /**
     * Action to perform with sorting elements.
     */
    sortingAction?: TableActions['sorting'];
    /**
     * Current sorting key.
     */
    sortKey: string;
    /**
     * Current sorting direction (ASC or DESC).
     */
    sortDirection: string;
    /** Inherited from Header props */
    quickActions: TableActions['quick'];
    /**
     * Determines if at least one row has the action col to be shown
     */
    showActionsCol: boolean;
};

type RowProps = {
    /**
     * Row data object.
     */
    row: TableRow;
    /**
     * Actions object (quick, menu, main).
     */
    actions: Omit<TableActions, 'quick' | 'bulk' | 'sorting'>;
    /**
     * Table config object.
     */
    configs: TableConfigs;
    /**
     * Main column identifier.
     */
    mainColumnKey: string;
    /**
     * Action to perform when the row is selected.
     */
    onRowSelectionChange: (value: TableRow['extra']['id']) => void;
    /**
     * Seleted rows id array.
     */
    selectedRows: string[];
    /**
     * Determines if a row is one of the last (see `LAST_ROWS_OFFSET` value).
     */
    isLastRow: boolean;
    /**
     * Determines if at least one row has the action col to be shown
     */
    showActionsCol: boolean;
};

type CellProps = {
    /**
     * Row cell object.
     */
    cell: TableRowCell;
    /**
     * Row item id.
     */
    itemId: string;
};

type HeaderThContentProps = {
    header: TableHeader;
    isFirst?: boolean;
    sortKey: string;
    sortDirection: string;
    couldSort?: boolean;
};

const getSelectedRowsLabel = (
    selectedRows: string[],
    labels: TableConfigs['labels']
): string => {
    let selectedLabel = labels ? labels.singleRowSelected?.toLowerCase() : '';

    if (selectedRows.length && selectedRows.length > 1) {
        selectedLabel = labels ? labels.moreRowsSelected?.toLowerCase() : '';
    }

    return `${selectedRows.length} ${selectedLabel}`;
};

const StickyHeader = ({ ...props }) => {
    const headerRef = useRef<HTMLInputElement>(undefined);
    const stickyHeaderRef = useRef<HTMLInputElement>(undefined);
    const [sticky, setSticky] = useState(false);
    const [leftMargin, setLeftMargin] = useState<number | null>(null);
    const [width, setWidth] = useState(null);

    const stickyClassName = 'is-sticky';

    const onVerticalScroll = () => {
        if (!headerRef.current || !props.tableRef.current) {
            return;
        }

        const { top, left, height } = headerRef.current.getBoundingClientRect();
        const { bottom } = props.tableRef.current.getBoundingClientRect();

        // need to get header here because, on firt render, header has a different classname
        const headerNode = document.getElementsByClassName(
            'gx-navigation-header'
        )[0];
        const headerHeight = headerNode?.getBoundingClientRect().height ?? 0;

        if (top < headerHeight) {
            setSticky(true);
            setLeftMargin(left);
        } else {
            setSticky(false);
        }

        if (bottom < headerHeight + height) {
            setSticky(false);
        }
    };

    const setCloneWidth = (stickyElement) => {
        if (!stickyElement) {
            return;
        }

        stickyHeaderRef.current = stickyElement;

        let children = headerRef.current?.querySelectorAll('th');
        let cloneChildren = stickyElement.querySelectorAll('th');

        setWidth(props.tableRef.current.offsetWidth);
        const tableRect = props.tableRef.current.getBoundingClientRect();
        setLeftMargin(tableRect.left);

        [].forEach.call(children, (node: HTMLTableCellElement, i) => {
            cloneChildren[i].style.minWidth = `${node.offsetWidth}px`;
        });
        // need to do this here because some layout shifting happens after the first full render
        storeThWidth();
    };

    const storeThWidth = () => {
        if (props.storeThWidth && headerRef.current) {
            const cssVars: string[] = [];
            const children = headerRef.current.querySelectorAll('th');

            children.forEach((node) => {
                const nodeAttributesKeys = Object.keys(node);
                let cssVarName = '';
                for (let i = 0; i < nodeAttributesKeys.length; i++) {
                    const key = nodeAttributesKeys[i];
                    if (key.match(/^__reactFiber/)) {
                        cssVarName = node[key]?.key;
                        break;
                    }
                }
                if (cssVarName) {
                    cssVars.push(`--${cssVarName}: ${node.offsetWidth}px;`);
                }
            });

            const styleTag = document.createElement('style');
            styleTag.innerText = `:root{ ${cssVars.join(' ')} }`;
            styleTag.id = 'gx-table-stored-th-width';
            document.getElementById('gx-table-stored-th-width')?.remove();
            document.getElementsByTagName('head')[0].appendChild(styleTag);
        }
    };

    useEffect(() => {
        document.addEventListener('scroll', onVerticalScroll, {
            passive: true,
        });

        return () => {
            document.removeEventListener('scroll', onVerticalScroll);
        };
    }, []);

    useEffect(() => {
        if (!stickyHeaderRef?.current) {
            return;
        }
        stickyHeaderRef.current.scrollLeft = props.scrollFromLeft;
    }, [props.scrollFromLeft]);

    if (!props.tableRef || !props.tableRef.current) {
        return null;
    }

    return (
        <>
            <Header
                {...(props as HeaderProps)}
                ref={headerRef as any}
                className={props.className}
            />
            <Header
                {...(props as HeaderProps)}
                ref={ref => {
                    setCloneWidth(ref);
                }}
                style={{
                    left: leftMargin,
                    width,
                    display: sticky ? 'block' : 'none',
                }}
                className={stickyClassName}
            />
        </>
    );
};

export const StickyActionsHeader = (props) => {
    const actionsHeaderRef = useRef<HTMLInputElement>(undefined);
    const [isSticky, setIsSticky] = useState(false);
    const [showHeader, setShowHeader] = useState(false);
    const stickyClassName = 'is-sticky';

    const headerNode = useMemo(
        () => document.getElementsByClassName('gx-navigation-header')[0],
        []
    );
    const headerHeight = headerNode?.getBoundingClientRect().height || 0;

    const updateHeaderVisibility = useCallback(() => {
        if (!actionsHeaderRef.current) {
            return;
        }

        const { top } = actionsHeaderRef.current.getBoundingClientRect();
        const shouldShow = top < headerHeight;
        setShowHeader(shouldShow);
        setIsSticky(shouldShow);
    }, [headerHeight]);

    useEffect(() => {
        document.addEventListener('scroll', updateHeaderVisibility, {
            passive: true,
        });
        return () =>
            document.removeEventListener('scroll', updateHeaderVisibility);
    }, [updateHeaderVisibility]);

    useEffect(() => {
        updateHeaderVisibility();
    }, [props.someItemSelected, updateHeaderVisibility]);

    const mountElement = document.querySelector('.gx-table-newWrap');
    return (
        <>
            <ActionsHeader
                ref={actionsHeaderRef}
                className={classNames(props.className)}
                {...props}
            />
            {(isSticky || (props.someItemSelected && showHeader)) &&
                mountElement &&
                createPortal(
                    <ActionsHeader
                        {...(props as ActionsHeaderProps)}
                        className={stickyClassName}
                    />,
                    mountElement
                )}
        </>
    );
};
const StickyHorizontalScrollbar = ({
    bubbleWidth,
    bubbleLeft,
    onScroll,
    scrollableOffsetWidth,
}) => {
    // this element is needed to know when the table is scrolled
    const dummyElementRef = useRef<HTMLDivElement>(undefined);
    const stickyScrollbarRef = useRef<HTMLDivElement>(undefined);
    const stickyScrollbarBubbleRef = useRef<HTMLDivElement>(undefined);
    const [showScrollbarBubble, setShowScrollbarBubble] =
        useState<boolean>(false);

    useDnD(
        stickyScrollbarBubbleRef,
        bubbleLeft,
        scrollableOffsetWidth,
        onScroll,
        showScrollbarBubble
    );

    const setVisibility = () => {
        if (!dummyElementRef || !dummyElementRef.current) {
            return;
        }

        const { top, left, bottom, right } =
            dummyElementRef.current.getBoundingClientRect();

        setShowScrollbarBubble(
            !(
                top >= 0 &&
                left >= 0 &&
                bottom <=
                    (window.innerHeight ||
                        document.documentElement.clientHeight) &&
                right <=
                    (window.innerWidth || document.documentElement.clientWidth)
            )
        );
    };

    useEffect(() => {
        setVisibility();

        document.addEventListener('scroll', setVisibility);

        return () => {
            document.removeEventListener('scroll', setVisibility);
        };
    }, []);

    return (
        <>
            <div
                style={{ position: 'relative', height: '1px' }}
                ref={dummyElementRef}
            ></div>
            {showScrollbarBubble && bubbleWidth > 0 && (
                <div
                    className={'gx-table-new__scrollbar is-sticky'}
                    ref={stickyScrollbarRef}
                >
                    <div
                        className="gx-table-new__scrollbarBubble"
                        style={{ width: bubbleWidth, left: bubbleLeft }}
                        ref={stickyScrollbarBubbleRef}
                    ></div>
                </div>
            )}
        </>
    );
};

export const GxTable = forwardRef<HTMLDivElement, TableOptions>(
    (options, ref) => {
        // trying to get ref from the outside first, then creating a new one if nothing is passed from outside
        const innerRef = useRef<HTMLDivElement>(undefined);
        const outerRef = useForwardedRef(ref);
        const tableWrapRef = ref && outerRef ? outerRef : innerRef;

        const tableContentRef = useRef<HTMLTableElement>(undefined);

        const {
            columns,
            data: dataIn,
            configs,
            actions,
            noInitialItems: noInitialItemsConfig,
            onSelection,
            clearSelection,
        } = options;

        const data: TableData = dataIn || { items: [] };

        const noInitialItems =
            noInitialItemsConfig || !(data.items && data.items?.length > 0);

        const {
            bulkActions,
            headers,
            mainColumnKey,
            onItemSelection,
            onBulkSelection,
            rows,
            rowActions,
            selectedRows: selectedRowsIn,
            onHeaderActionSelection,
            sortingAction,
            sortKey,
            sortDirection,
        } = useTable({ columns, data, actions });

        const showActionsCol =
            rows.filter((item) => {
                const quickActions = configs.itemActionsHelper
                    ? configs.itemActionsHelper(
                          item.extra.id,
                          rowActions,
                          'quick'
                      )
                    : rowActions.quick;
                const menuActions = configs.itemActionsHelper
                    ? configs.itemActionsHelper(
                          item.extra.id,
                          rowActions,
                          'menu'
                      )
                    : rowActions.menu;

                return quickActions || menuActions;
            }).length > 0;

        const selectedRows = selectedRowsIn.filter((itm) => {
            if (typeof configs?.itemSelection === 'function') {
                const itemSelectionObj = configs?.itemSelection(itm);

                return itemSelectionObj.status === true;
            } else {
                return true;
            }
        });

        const labels = configs?.labels ?? DEFAULT_LABELS;

        const { handleScrollLeft, scrollFromLeft, scrollPosition } =
            useTableHorizontalScroll(tableWrapRef);

        const {
            stickyScrollbarBubbleWidth,
            scrollableOffsetWidth,
            tableWidth,
        } = useTableResize(tableContentRef, tableWrapRef);

        const handleOnItemSelection = (item) => {
            onItemSelection(item);
        };

        /** sticky horizontal scrollbar handler */
        useEffect(() => {
            tableWrapRef.current.scrollLeft = scrollFromLeft;
            // calling the optional onHorizontalScroll callback, to handle horizontal scroll sync
            if (
                options.configs?.onHorizontalScroll &&
                typeof options.configs.onHorizontalScroll === 'function'
            ) {
                options.configs.onHorizontalScroll(scrollFromLeft);
            }
        }, [scrollFromLeft]);

        useEffect(() => {
            if (
                clearSelection == null ||
                typeof clearSelection === 'undefined'
            ) {
                return;
            }
            /** ActionsHeader reset */
            onHeaderActionSelection();
        }, [clearSelection]);

        useEffect(() => {
            onSelection && onSelection(selectedRows);
        }, [selectedRows]);

        return (
            <div
                className={classNames('gx-table-newWrap', {
                    'gx-table-newWrap--checked': selectedRows.length,
                    'start-scroll': scrollPosition === 'start',
                    'end-scroll': scrollPosition === 'end',
                    'start-scroll end-scroll': scrollPosition === 'none',
                })}
                ref={tableWrapRef}
            >
                <StickyActionsHeader
                    someItemSelected={selectedRows.length > 0}
                    bulkActions={bulkActions}
                    configs={configs}
                    labels={labels}
                    selectedRows={selectedRows}
                    totalRows={rows?.length}
                    onCheckboxClickAction={onHeaderActionSelection}
                />
                <table className="gx-table-new" ref={tableContentRef}>
                    <StickyHeader
                        tableRef={tableWrapRef}
                        hasItemsSelection={configs?.itemSelection}
                        headers={headers}
                        menuActions={rowActions?.menu}
                        totalRows={rows?.length}
                        onBulkSelection={onBulkSelection}
                        sortingAction={sortingAction}
                        sortKey={sortKey}
                        sortDirection={sortDirection}
                        scrollFromLeft={scrollFromLeft}
                        quickActions={rowActions?.quick}
                        storeThWidth={configs?.storeThWidth}
                        tableWidth={
                            configs?.storeThWidth ? tableWidth : undefined
                        }
                        showActionsCol={showActionsCol}
                    />
                    <ApiCallOnVisibleProvider>
                        {rows?.length ? (
                            <tbody className="gx-table-new__body">
                                {rows.map((row, index) => (
                                    <Row
                                        key={`row_${row.extra.id}`}
                                        row={row}
                                        actions={rowActions}
                                        configs={configs}
                                        onRowSelectionChange={
                                            handleOnItemSelection
                                        }
                                        showActionsCol={showActionsCol}
                                        selectedRows={selectedRows}
                                        mainColumnKey={mainColumnKey}
                                        isLastRow={
                                            index >=
                                            rows.length - LAST_ROWS_OFFSET
                                        }
                                    />
                                ))}
                            </tbody>
                        ) : null}
                    </ApiCallOnVisibleProvider>
                </table>
                <StickyHorizontalScrollbar
                    onScroll={handleScrollLeft}
                    bubbleWidth={stickyScrollbarBubbleWidth}
                    bubbleLeft={scrollFromLeft}
                    scrollableOffsetWidth={scrollableOffsetWidth}
                />
                {!rows?.length && configs && configs.emptyState && (
                    <EmptyState
                        title={configs.emptyState.text}
                        img={configs.emptyState?.image}
                    >
                        {noInitialItems &&
                            configs.emptyState?.buttonOnClick &&
                            configs.emptyState?.buttonLabel && (
                                <Button
                                    onClick={configs.emptyState.buttonOnClick}
                                >
                                    {configs.emptyState.buttonLabel}
                                </Button>
                            )}
                    </EmptyState>
                )}
            </div>
        );
    }
);

const HeaderThContent: React.FC<HeaderThContentProps> = ({
    header,
    isFirst = false,
    sortKey,
    sortDirection,
    couldSort,
}) => {
    return (
        <div
            className={classNames({
                'gx-table-new__order': header.sortable && couldSort,
                'gx-table-new__headFirst': isFirst,
            })}
        >
            {header.header}
            {header.sortable && header.sortKey === sortKey && (
                <Icon
                    className="gx-order-arrow"
                    name={
                        header.sortable &&
                        header.sortKey === sortKey &&
                        sortDirection === 'ASC'
                            ? 'arrow-top'
                            : 'arrow-down'
                    }
                />
            )}
            {header?.headerEndElement ? header.headerEndElement : null}
        </div>
    );
};

const Header = forwardRef<HTMLTableSectionElement, HeaderProps>(
    (
        {
            className,
            style,
            hasItemsSelection,
            headers,
            showActionsCol,
            totalRows = null,
            onBulkSelection = null,
            sortingAction = null,
            sortKey = null,
            sortDirection = null,
            menuActions,
            quickActions,
        },
        ref
    ) => {
        const handleSort = (key: string) => {
            if (!sortingAction) {
                return;
            }

            let dir = 'DESC';

            if (sortKey === key) {
                if (sortDirection === 'DESC') {
                    dir = 'ASC';
                }
            }

            sortingAction.action(key, dir);
        };
        const couldSort = totalRows && totalRows > 2;
        return (
            <thead
                className={classNames(['gx-table-new__head', className])}
                ref={ref as any}
                style={style}
            >
                <tr className="gx-table__row">
                    {hasItemsSelection && (
                        <th
                            key={`th_bulk_selection`}
                            className="gx-table-new__cell gx-table-new__cell--check"
                        >
                            <div>
                                <div className="gx-table-new__headFirst">
                                    <Checkbox
                                        data-testid="gx-table-select-all"
                                        checked={false}
                                        disabled={!totalRows}
                                        onChange={onBulkSelection}
                                    />
                                </div>
                            </div>
                        </th>
                    )}
                    {headers.map(
                        (header: TableHeader, index: number) =>
                            header.visible && (
                                <th
                                    key={`th_${header.key}`}
                                    className={classNames(
                                        'gx-table-new__cell',
                                        {
                                            'gx-table-new__cell--main':
                                                header.main,
                                        }
                                    )}
                                    style={header?.inlineStyle}
                                    onClick={
                                        // header.sortable
                                        header.sortable && couldSort
                                            ? () => handleSort(header.sortKey)
                                            : null
                                    }
                                >
                                    {!hasItemsSelection && index === 0 ? (
                                        <div>
                                            <HeaderThContent
                                                isFirst
                                                header={header}
                                                sortKey={sortKey}
                                                sortDirection={sortDirection}
                                                couldSort={couldSort}
                                            />
                                        </div>
                                    ) : (
                                        <HeaderThContent
                                            header={header}
                                            sortKey={sortKey}
                                            sortDirection={sortDirection}
                                            couldSort={couldSort}
                                        />
                                    )}
                                </th>
                            )
                    )}

                    {showActionsCol && (
                        <th
                            className="gx-table-new__cell gx-table-new__cell--actions"
                            data-testid="actions-cell"
                        >
                            <div>
                                <div className="gx-table-new__headLast"></div>
                            </div>
                        </th>
                    )}
                </tr>
            </thead>
        );
    }
);

const ActionsHeader = forwardRef<HTMLDivElement, ActionsHeaderProps>(
    (
        {
            className,
            style,
            bulkActions = null,
            configs,
            labels,
            onCheckboxClickAction = null,
            selectedRows,
            totalRows,
        },
        ref
    ) => {
        if (!selectedRows.length) {
            return null;
        }

        return (
            <div
                className={classNames(['gx-table-newOverHead', className])}
                ref={ref as any}
                style={style}
            >
                {configs?.itemSelection && onCheckboxClickAction && (
                    <Checkbox
                        checked={selectedRows.length === totalRows}
                        indeterminate={
                            selectedRows.length !== totalRows &&
                            selectedRows.length > 0
                        }
                        onChange={onCheckboxClickAction}
                    />
                )}
                <span className="gx-table-newOverHead__info">
                    {getSelectedRowsLabel(selectedRows, labels)}
                </span>
                {bulkActions &&
                    bulkActions.map((bulkAction: BulkAction) => (
                        <Tooltip
                            key={`header_bulk_action_${bulkAction.icon}`}
                            text={
                                (selectedRows.length > 1 &&
                                bulkAction.pluralLabel
                                    ? bulkAction.pluralLabel
                                    : bulkAction.label) || ''
                            }
                        >
                            <Button
                                variant="ghost"
                                onClick={() => (
                                    bulkAction.action(selectedRows),
                                    onCheckboxClickAction
                                        ? onCheckboxClickAction()
                                        : null
                                )}
                                iconOnly
                            >
                                <Icon
                                    name={bulkAction.icon as IconProps['name']}
                                />
                            </Button>
                        </Tooltip>
                    ))}
            </div>
        );
    }
);

const Row: React.FC<RowProps> = ({
    actions,
    configs,
    isLastRow,
    mainColumnKey,
    onRowSelectionChange,
    row,
    selectedRows,
    showActionsCol,
}) => {
    const id = row.extra.id;
    const firstCellActionsRef = useRef(undefined);
    const lastCellActionsRef = useRef(undefined);
    const cellActionRefs = useRef(row.cells.map(() => createRef()));
    const menuActionsRefs = useRef(row.cells.map(() => createRef()));
    const rowRef = useRef(undefined);
    const mainAction = configs.itemActionsHelper
        ? configs.itemActionsHelper(id, actions, 'main')
        : actions?.main;
    const quickActions = configs.itemActionsHelper
        ? configs.itemActionsHelper(id, actions, 'quick')
        : actions.quick;
    const menuActions = configs.itemActionsHelper
        ? configs.itemActionsHelper(id, actions, 'menu')
        : actions.menu;
    const firstCellActions = actions?.firstCell;

    const onRowClick = useClickWithExcludedElements(
        [
            firstCellActionsRef,
            lastCellActionsRef,
            ...cellActionRefs.current,
            ...menuActionsRefs.current,
        ],
        () => (mainAction ? mainAction.action(id) : null)
    );

    if (!id) {
        return;
    }

    const isRowSelected = selectedRows && selectedRows.includes(id);

    const getFlagValue = (value: any) => {
        if (typeof value === 'boolean') {
            return value;
        }

        if (Number.isInteger(value) && (value === 1 || value === 0)) {
            return !!value;
        }

        return null;
    };

    let showCheckbox = false;
    let isCheckBoxDisabled = false;
    let checkBoxTooltipText = '';
    if (
        configs?.itemSelection &&
        typeof configs?.itemSelection === 'function'
    ) {
        const itemSelectionFuncValue = configs?.itemSelection(id);
        showCheckbox =
            itemSelectionFuncValue.status === 'disabled' ||
            !!itemSelectionFuncValue.status;
        isCheckBoxDisabled = itemSelectionFuncValue.status === 'disabled';
        checkBoxTooltipText = itemSelectionFuncValue.tooltip || '';
    } else {
        showCheckbox = !!configs?.itemSelection;
    }

    return (
        <tr
            key={`tr_${id}`}
            ref={rowRef}
            className={classNames('gx-table-new__row', {
                'is-selected': isRowSelected,
                [row.customRowClassName]: Boolean(row.customRowClassName),
                'no-main-action':
                    actions.main === undefined || actions.main === null,
            })}
            onClick={onRowClick}
        >
            {showCheckbox || firstCellActions ? (
                <>
                    <td
                        key={`th_bulk_selection`}
                        className="gx-table-new__cell gx-table-new__cell--check"
                    >
                        <div ref={firstCellActionsRef}>
                            {showCheckbox &&
                                (checkBoxTooltipText ? (
                                    <Tooltip
                                        text={checkBoxTooltipText}
                                        position="top"
                                    >
                                        <div>
                                            <Checkbox
                                                checked={isRowSelected}
                                                disabled={isCheckBoxDisabled}
                                                onChange={() =>
                                                    onRowSelectionChange(id)
                                                }
                                            />
                                        </div>
                                    </Tooltip>
                                ) : (
                                    <Checkbox
                                        checked={isRowSelected}
                                        disabled={isCheckBoxDisabled}
                                        onChange={() =>
                                            onRowSelectionChange(id)
                                        }
                                    />
                                ))}
                            {firstCellActions &&
                                firstCellActions.map(
                                    (firstCellAction: FirstCellAction) => {
                                        const flagValue = getFlagValue(
                                            row.extra[firstCellAction.itemKey]
                                        );

                                        return (
                                            <Button
                                                key={firstCellAction.itemKey}
                                                variant="ghost"
                                                onClick={() =>
                                                    firstCellAction.action(
                                                        id,
                                                        !flagValue
                                                    )
                                                }
                                                className={classNames({
                                                    'is-disabled':
                                                        flagValue === null,
                                                    [firstCellAction?.unflagClassName]:
                                                        flagValue !== null &&
                                                        !flagValue &&
                                                        firstCellAction?.unflagClassName,
                                                    [firstCellAction?.flagClassName]:
                                                        flagValue !== null &&
                                                        flagValue &&
                                                        firstCellAction?.flagClassName,
                                                    [firstCellAction?.disabledClassName]:
                                                        flagValue === null &&
                                                        firstCellAction?.disabledClassName,
                                                })}
                                                disabled={flagValue === null}
                                                iconOnly
                                            >
                                                {firstCellAction?.flagTooltip ||
                                                firstCellAction?.unflagTooltip ||
                                                firstCellAction?.disabledTooltip ? (
                                                    <Tooltip
                                                        position="bottom"
                                                        text={
                                                            flagValue ===
                                                                null &&
                                                            firstCellAction?.disabledTooltip
                                                                ? firstCellAction?.disabledTooltip
                                                                : flagValue
                                                                ? firstCellAction?.flagTooltip
                                                                : firstCellAction?.unflagTooltip
                                                        }
                                                    >
                                                        <Icon
                                                            name={
                                                                flagValue ===
                                                                    null &&
                                                                firstCellAction?.iconDisabled
                                                                    ? firstCellAction?.iconDisabled
                                                                    : flagValue
                                                                    ? firstCellAction.iconFlag
                                                                    : firstCellAction.iconUnflag
                                                            }
                                                        />
                                                    </Tooltip>
                                                ) : (
                                                    <Icon
                                                        name={
                                                            flagValue ===
                                                                null &&
                                                            firstCellAction?.iconDisabled
                                                                ? firstCellAction?.iconDisabled
                                                                : flagValue
                                                                ? firstCellAction.iconFlag
                                                                : firstCellAction.iconUnflag
                                                        }
                                                    />
                                                )}
                                            </Button>
                                        );
                                    }
                                )}
                        </div>
                    </td>
                </>
            ) : null}
            {row.cells.map((cell: TableRowCell, index: number) =>
                cell.visible ? (
                    <td
                        key={`cell_${cell.key}_${id}`}
                        className={classNames('gx-table-new__cell', {
                            'gx-table-new__cell--main':
                                cell.key === mainColumnKey,
                            'gx-table-new__cell--maxWidth': cell.fixedWidth,
                            'gx-table-new__cell--upgrade':
                                cell.cellAction || cell.cellLink,
                            [cell.className]: cell.className,
                        })}
                    >
                        {cell.asyncContent ? (
                            <AsyncCell
                                cell={cell}
                                itemId={id}
                                ref={cellActionRefs.current[index]}
                            />
                        ) : (
                            <Cell
                                cell={cell}
                                itemId={id}
                                ref={cellActionRefs.current[index]}
                            />
                        )}
                    </td>
                ) : null
            )}
            {showActionsCol && (
                <td
                    className="gx-table-new__cell gx-table-new__cell--actions last-col"
                    data-testid="actions-cell"
                >
                    <div
                        className="gx-table-new__actionsWrap"
                        ref={lastCellActionsRef}
                    >
                        <div className="gx-table-new__actions">
                            {quickActions &&
                                quickActions.map((quickAction: QuickAction) => {
                                    if (quickAction?.customElement) {
                                        const CustomElement =
                                            quickAction.customElement;
                                        return (
                                            <CustomElement
                                                id={id}
                                                key={`cell_action_custom_${quickAction.icon}_${id}`}
                                            />
                                        );
                                    } else {
                                        return (
                                            <Tooltip
                                                key={`cell_action_${quickAction.icon}_${id}`}
                                                position="top"
                                                text={quickAction.label}
                                            >
                                                <Button
                                                    variant="ghost"
                                                    data-testid={`cell_quickaction_${id}`}
                                                    as={
                                                        quickAction.link
                                                            ? 'a'
                                                            : 'button'
                                                    }
                                                    onClick={() =>
                                                        quickAction?.action(id)
                                                    }
                                                    href={
                                                        quickAction.link
                                                            ? quickAction.link(
                                                                  id
                                                              )
                                                            : null
                                                    }
                                                    className={
                                                        quickAction?.buttonClassName
                                                    }
                                                    aria-label={
                                                        quickAction.label
                                                    }
                                                    iconOnly
                                                >
                                                    <Icon
                                                        name={
                                                            quickAction.icon as IconProps['name']
                                                        }
                                                    />
                                                </Button>
                                            </Tooltip>
                                        );
                                    }
                                })}
                            {menuActions && (
                                <div className="gx-dropupButton">
                                    <Dropdown
                                        data-testid={`cell_menuactions`}
                                        buttonIsIconOnly={true}
                                        buttonVariant="ghost"
                                        buttonContent={<Icon name="ellipsis" />}
                                        position={
                                            isLastRow
                                                ? 'topRight'
                                                : 'bottomRight'
                                        }
                                    >
                                        <ActionList>
                                            {menuActions.map(
                                                (
                                                    menuAction: MenuAction,
                                                    index
                                                ) => (
                                                    <span
                                                        key={`span_wrap_action_list_item_${menuAction.label}`}
                                                        ref={
                                                            menuActionsRefs
                                                                .current[index]
                                                        }
                                                        onClick={() =>
                                                            menuAction.action(
                                                                id
                                                            )
                                                        }
                                                    >
                                                        {/* this span is needed to attach ref to the ActionListItems avoiding main action click action */}
                                                        <ActionListItem
                                                            key={`action_list_item_${menuAction.label}`}
                                                            text={
                                                                menuAction.label
                                                            }
                                                            endElement={
                                                                menuAction?.labelEndElement
                                                                    ? menuAction.labelEndElement
                                                                    : null
                                                            }
                                                        />
                                                    </span>
                                                )
                                            )}
                                        </ActionList>
                                    </Dropdown>
                                </div>
                            )}
                        </div>
                    </div>
                </td>
            )}
        </tr>
    );
};

const AsyncCell = forwardRef<HTMLDivElement, CellProps>(
    ({ itemId, cell }, ref) => {
        const cellRef = useRef(undefined);
        const { results, isLoading, apiCallError } = useApiCallOnVisibileRef(
            cellRef,
            itemId,
            cell.asyncContent,
            {
                abortOnOutOfView: true,
            }
        );

        return (
            <div ref={cellRef}>
                {isLoading ? (
                    <Icon name="loader" className="gx-spin" />
                ) : (
                    <div ref={ref}>
                        {apiCallError ? '---' : results}
                        {results && !apiCallError && (
                            <>
                                <CellAction itemId={itemId} cell={cell} />
                                <FixedCellAction itemId={itemId} cell={cell} />
                            </>
                        )}
                    </div>
                )}
            </div>
        );
    }
);

const Cell = forwardRef<HTMLDivElement, CellProps>(({ itemId, cell }, ref) => (
    <>
        <div>{cell.content}</div>
        <FixedCellAction itemId={itemId} cell={cell} />
        <CellAction itemId={itemId} cell={cell} ref={ref} />
    </>
));

const CellAction = forwardRef<HTMLDivElement, CellProps>(
    ({ itemId, cell }, ref) => {
        let shouldBeRendered = true;
        if (cell?.shouldRenderCellAction instanceof Function) {
            shouldBeRendered = cell.shouldRenderCellAction(itemId);
        }
        return shouldBeRendered && (cell?.cellAction || cell?.cellLink) ? (
            <div ref={ref}>
                <a
                    href={cell?.cellLink ? cell.cellLink(itemId) : null}
                    onClick={
                        cell?.cellAction ? () => cell.cellAction(itemId) : null
                    }
                    className="gx-table-new__upgrade"
                >
                    {cell.cellActionLabel}
                </a>
            </div>
        ) : null;
    }
);

const FixedCellAction: FC<CellProps> = ({ itemId, cell }) => {
    let shouldBeRendered = true;
    if (cell?.shouldRenderCellAction instanceof Function) {
        shouldBeRendered = cell.shouldRenderCellAction(itemId);
    }

    const onClick = (e) => {
        e?.stopPropagation();
        if (
            'action' in cell.fixedCellAction &&
            typeof cell.fixedCellAction.action === 'function'
        ) {
            cell.fixedCellAction.action(itemId);
        }
    };

    return shouldBeRendered && cell.fixedCellAction ? (
        <div className="gx-table-new__fixedCellActionWrap">
            <Button
                as={'link' in cell.fixedCellAction ? 'a' : undefined}
                href={
                    'link' in cell.fixedCellAction
                        ? cell.fixedCellAction.link(itemId)
                        : undefined
                }
                onClick={onClick}
                size="small"
                className={classNames('gx-table-new__fixedCellAction', {
                    [cell.fixedCellAction?.customClass]:
                        cell.fixedCellAction?.customClass,
                })}
                target={
                    'link' in cell.fixedCellAction &&
                    cell.fixedCellAction.target
                        ? cell.fixedCellAction.target
                        : undefined
                }
            >
                {cell.fixedCellAction?.startIcon && (
                    <Icon name={cell.fixedCellAction.startIcon} />
                )}
                <span>{cell.fixedCellAction.label}</span>
            </Button>
        </div>
    ) : null;
};
