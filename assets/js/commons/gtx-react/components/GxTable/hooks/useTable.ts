import { useMemo } from 'react';
import useTableColumns from './useTableColumns';
import useTableRows from './useTableRows';
import { TableOptions, TableItem } from '../types';

type KeysToRemove = 'configs' | 'onSelection';
type UseTableProps = Omit<TableOptions, KeysToRemove>;

const useTable = ({ data, columns, actions }: UseTableProps) => {
    const { headers, headerColumnsKeys, mainColumnKey } = useTableColumns(columns);
    const { onRowSelection, onBulkRowsSelection, onEmptyRowsSelection, rows, rowActions, selectedRows } = useTableRows(
        data,
        columns,
        actions || {}
    );

    /**
     * Returns the sorting actions.
     */
    const sortingAction = useMemo(() => {
        if (!actions || !actions?.sorting) {
            return null;
        }

        const { sorting } = actions;

        return sorting;
    }, [actions]);

    /**
     * Returns the bulk actions.
     */
    const bulkActions = useMemo(() => {
        if (!actions) {
            return null;
        }

        const { bulk } = actions;

        return bulk;
    }, [actions]);

    /**
     * Returns current table sorting key.
     */
    const sortKey = useMemo(() => {
        if (!data?.sortData) {
            return null;
        }

        const { key } = data.sortData;

        return key;
    }, [data.sortData]);

    /**
     * Returns current table sorting direction (ASC or DESC).
     */
    const sortDirection = useMemo(() => {
        if (!data?.sortData) {
            return null;
        }

        const { direction } = data.sortData;

        return direction;
    }, [data.sortData]);

    return {
        headers,
        bulkActions,
        headerColumnsKeys,
        mainColumnKey,
        onBulkSelection: onBulkRowsSelection,
        onHeaderActionSelection: onEmptyRowsSelection,
        onItemSelection: (id: TableItem['id']) => onRowSelection(id),
        selectedRows,
        rows,
        rowActions,
        sortingAction,
        sortKey,
        sortDirection,
    };
};

export default useTable;
