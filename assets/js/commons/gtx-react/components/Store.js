import React, { Component, Fragment } from 'react';
import { createStore } from '../store';
import { Provider } from 'react-redux';
/**
 * @typedef {Object} StoreProps
 * @property {Object} reducers
 * @property {() => Promise<any>} api
 * @property {React.ReactNode} [onApiError]
 * @property {(ready: boolean) => React.ReactNode} children
 * @property {Array} [middlewares]
 */

/**
 * @extends {Component<StoreProps>}
 */
export class Store extends Component {
    state = {
        data: null,
        apiError: false,
    };

    get store() {
        return this.state.data ? createStore(this.props.reducers, this.state.data, this.props.middlewares) : null;
    }

    componentDidMount() {
        this.props
            .api()
            .then((data) => {
                this.setState({
                    apiError: false,
                    data,
                });
            })
            .catch(() => this.setState({ apiError: true }));
    }

    render() {
        return this.state.apiError && this.props.onApiError ? (
            this.props.onApiError
        ) : this.state.data ? (
            <Provider store={this.store}>
                <Fragment>{this.props.children(true)}</Fragment>
            </Provider>
        ) : (
            this.props.children(false)
        );
    }
}
