import { render, screen } from '#tests/react/testing-library-enhanced';
import { describe, expect, test } from 'vitest';
import { CrmAutocomplete } from './CrmAutocomplete';

describe('HighlightText', () => {
    // Test case 1: No term provided
    test('should render the original text when no term is provided', () => {
        const text = 'This is a sample text.';
        render(<CrmAutocomplete.HighlightText text={text} term="" />);

        // Expect the original text to be present without any strong tags
        const spanElement = screen.getByText(text);
        expect(spanElement).toBeInTheDocument();
    });

    // Test case 2: Term found in the text (single occurrence)
    test('should highlight the term when found in the text', () => {
        const text = 'This is a sample text.';
        const term = 'sample';
        render(<CrmAutocomplete.HighlightText text={text} term={term} />);

        // Expect the text to be split and the term wrapped in strong
        // We can check the structure by querying for the strong tag
        const strongElement = screen.getByText('sample');
        expect(strongElement).toBeInTheDocument();
        expect(strongElement.textContent).toBe(term);

        // We can also check the overall structure by examining the container's innerHTML
        // Note: Using container.innerHTML is less preferred than querying specific elements,
        // but can be useful for verifying the exact structure in simple cases like this.
        const { container } = render(
            <CrmAutocomplete.HighlightText text={text} term={term} />
        );
        expect(container.innerHTML).toBe(
            '<span class="crm-autocomplete__optionsText">This is a <strong>sample</strong> text.</span>'
        );
    });

    // Test case 3: Term not found in the text
    test('should render the original text when the term is not found', () => {
        const text = 'This is a sample text.';
        const term = 'missing';
        render(<CrmAutocomplete.HighlightText text={text} term={term} />);

        // Expect the original text to be present without any strong tags
        const spanElement = screen.getByText(text);
        expect(spanElement).toBeInTheDocument();
    });

    // Test case 4: Term with multiple occurrences
    test('should highlight all occurrences of the term', () => {
        const text = 'sample text with sample term and another sample.';
        const term = 'sample';
        render(<CrmAutocomplete.HighlightText text={text} term={term} />);

        // Expect three strong elements with the text 'sample'
        const strongElements = screen.getAllByText(term, {
            selector: 'strong',
        });
        expect(strongElements).toHaveLength(3);
        strongElements.forEach((element) => {
            expect(element.textContent).toBe(term);
        });

        const { container } = render(
            <CrmAutocomplete.HighlightText text={text} term={term} />
        );
        expect(container.innerHTML).toBe(
            '<span class="crm-autocomplete__optionsText"><strong>sample</strong> text with <strong>sample</strong> term and another <strong>sample</strong>.</span>'
        );
    });

    // Test case 5: Case-insensitivity
    test('should highlight the term case-insensitively', () => {
        const text = 'Sample text with SAMPLE term and another sAmPlE.';
        const term = 'sample';
        render(<CrmAutocomplete.HighlightText text={text} term={term} />);

        // Expect three strong elements with the original casing from the text
        const strongElements = screen.getAllByText(/sample/i, {
            selector: 'strong',
        }); // Use regex for case-insensitive text match
        expect(strongElements).toHaveLength(3);

        expect(strongElements[0]!.textContent).toBe('Sample');
        expect(strongElements[1]!.textContent).toBe('SAMPLE');
        expect(strongElements[2]!.textContent).toBe('sAmPlE');

        const { container } = render(
            <CrmAutocomplete.HighlightText text={text} term={term} />
        );
        expect(container.innerHTML).toBe(
            '<span class="crm-autocomplete__optionsText"><strong>Sample</strong> text with <strong>SAMPLE</strong> term and another <strong>sAmPlE</strong>.</span>'
        );
    });

    // Test case 6: Term at the beginning of the text
    test('should highlight the term at the beginning of the text', () => {
        const text = 'Start text with term.';
        const term = 'Start';
        render(<CrmAutocomplete.HighlightText text={text} term={term} />);

        const { container } = render(
            <CrmAutocomplete.HighlightText text={text} term={term} />
        );
        expect(container.innerHTML).toBe(
            '<span class="crm-autocomplete__optionsText"><strong>Start</strong> text with term.</span>'
        );
    });

    // Test case 7: Term at the end of the text
    test('should highlight the term at the end of the text', () => {
        const text = 'Text ends with term End.';
        const term = 'End.';
        const { rerender } = render(
            <CrmAutocomplete.HighlightText text={text} term={term} />
        );

        rerender(<CrmAutocomplete.HighlightText text={text} term={term} />);
        expect(screen.getByText('End.').nodeName).toBe('STRONG');
    });

    // Test case 8: Empty text
    test('should render nothing or an empty span for empty text', () => {
        const text = '';
        const term = 'term';
        render(<CrmAutocomplete.HighlightText text={text} term={term} />);

        const { container } = render(
            <CrmAutocomplete.HighlightText text={text} term={term} />
        );
        expect(container.innerHTML).toBe(
            '<span class="crm-autocomplete__optionsText"></span>'
        );
    });

    // Test case 9: Term with special regex characters
    test('should handle terms with special regex characters', () => {
        const text = 'This text has a dot. and a plus+ and a star.+*';
        const term = '.+*'; // These are regex special characters
        render(<CrmAutocomplete.HighlightText text={text} term={term} />);

        // The term '.+*' should be escaped and found as a literal string
        const strongElement = screen.getByText('.+*', {
            selector: 'strong',
        });
        expect(strongElement).toBeInTheDocument();
        expect(strongElement.textContent).toBe('.+*');

        const { container } = render(
            <CrmAutocomplete.HighlightText text={text} term={term} />
        );
        expect(container.innerHTML).toBe(
            '<span class="crm-autocomplete__optionsText">This text has a dot. and a plus+ and a star<strong>.+*</strong></span>'
        );
    });

    // Test case 10: Adjacent terms (split handles this by including empty strings)
    test('should handle adjacent terms correctly', () => {
        const text = 'termterm';
        const term = 'term';
        render(<CrmAutocomplete.HighlightText text={text} term={term} />);

        // The split result for 'termterm' and regex /(term)/gi is ['', 'term', '', 'term', '']
        // The mapping should render <strong>term</strong><strong>term</strong>
        const strongElements = screen.getAllByText(term, {
            selector: 'strong',
        });
        expect(strongElements).toHaveLength(2);
        expect(strongElements[0]!.textContent).toBe('term');
        expect(strongElements[1]!.textContent).toBe('term');

        const { container } = render(
            <CrmAutocomplete.HighlightText text={text} term={term} />
        );
        expect(container.innerHTML).toBe(
            '<span class="crm-autocomplete__optionsText"><strong>term</strong><strong>term</strong></span>'
        );
    });

    // Test case 11: Overlapping terms (regex split might not handle true overlaps as expected,
    // but it handles cases where a match is immediately followed by another)
    test('should handle terms that appear consecutively', () => {
        const text = 'ababab';
        const term = 'aba';
        render(<CrmAutocomplete.HighlightText text={text} term={term} />);

        // The split result for 'ababab' and regex /(aba)/gi is ['', 'aba', 'bab']
        // This is a limitation of regex split for overlapping matches.
        // The current implementation will highlight the first 'aba'.
        const strongElement = screen.getByText('aba', {
            selector: 'strong',
        });
        expect(strongElement).toBeInTheDocument();

        const { container } = render(
            <CrmAutocomplete.HighlightText text={text} term={term} />
        );
        // Depending on the exact regex engine and split behavior, this might vary slightly.
        // With /(aba)/gi, it typically finds the first 'aba', splits, and the rest is 'bab'.
        // Let's verify based on the observed behavior of String.prototype.split with capturing groups.
        // The split result is ['','aba','bab'] -> ['', 'aba', 'b', 'aba', ''] if term is 'ab'
        // For term 'aba' in 'ababab', regex /(aba)/gi, split is ['', 'aba', 'bab']
        // parts = ['', 'aba', 'bab']
        // index 0: '' (not match) -> <React.Fragment key={0}></React.Fragment>
        // index 1: 'aba' (match) -> <strong key={1}>aba</strong>
        // index 2: 'bab' (not match) -> <React.Fragment key={2}>bab</React.Fragment>
        // Result: <span><strong>aba</strong>bab</span>
        expect(container.innerHTML).toBe(
            '<span class="crm-autocomplete__optionsText"><strong>aba</strong>bab</span>'
        );
    });
});
