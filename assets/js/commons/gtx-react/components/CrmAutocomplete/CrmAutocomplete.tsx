import { Icon } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import clsx from 'clsx';
import {
    ComponentProps,
    createContext,
    forwardRef,
    Fragment,
    PropsWithChildren,
    RefObject,
    useContext,
    useMemo,
    useRef,
} from 'react';
import { createPortal } from 'react-dom';
import { useContentDropdownContext } from '../ContentDropdown/ContentDropdownContext';

export function CrmAutoCompleteInputReset(props: {
    onClick: () => void;
    hasInput: boolean;
}) {
    return (
        <button
            className={`crm-autocomplete__reset ${
                !props.hasInput ? 'is-hidden' : ''
            }`}
            onClick={props.onClick}
        >
            <Icon name="cross-circle--active" />
            <span className="gx-sr-only">{trans('label.reset')}</span>
        </button>
    );
}

type HighlightTextProps = {
    /** The original text to display. */
    text: string;
    /** The term to highlight within the text. */
    term: string;
};

/**
 * A React component that highlights occurrences of a term within a given text.
 * The matching parts are wrapped in <strong> tags.
 * This version avoids using dangerouslySetInnerHTML.
 *
 * @param props - The component props: text and term.
 */
const HighlightText: React.FC<HighlightTextProps> = ({ text, term }) => {
    if (!term) {
        // If no term is provided, just return the original text
        return <span className="crm-autocomplete__optionsText">{text}</span>;
    }

    // Escape special characters in the term to use it safely in a regex
    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // Create a regular expression for the escaped term, case-insensitive and global
    const regex = new RegExp(`(${escapedTerm})`, 'gi');

    // Split the text by the regex matches.
    // The result will be an array where elements at even indices are non-matches
    // and elements at odd indices are matches (due to the capturing group).
    const parts = text.split(regex);

    // Map over the parts to render them
    return (
        <span className="crm-autocomplete__optionsText">
            {parts.map((part, index) => {
                // Check if the current part is a match (odd index)
                // We also need to verify if the part actually matches the term
                // because split can sometimes return empty strings or non-matches
                // at odd indices depending on the regex and text.
                // A simple check is to see if the part, case-insensitively, equals the term.
                const isMatch =
                    index % 2 !== 0 &&
                    part.toLowerCase() === term.toLowerCase();

                if (isMatch) {
                    // If it's a match, wrap it in <strong>
                    return <strong key={index}>{part}</strong>;
                } else {
                    // Otherwise, render as plain text
                    return <Fragment key={index}>{part}</Fragment>;
                }
            })}
        </span>
    );
};

const CrmAutocompleteContext = createContext<
    | {
          containerRef: RefObject<HTMLInputElement>;
      }
    | undefined
>(undefined);

const useCrmAutocompleteContext = () => {
    const context = useContext(CrmAutocompleteContext);
    if (!context) {
        throw new Error(
            'useCrmAutocompleteContext must be used within a CrmAutocompleteContextProvider'
        );
    }
    return context;
};

export function CrmAutocomplete(props: PropsWithChildren) {
    const containerRef = useRef<HTMLInputElement>(null);

    return (
        <CrmAutocompleteContext.Provider value={{ containerRef }}>
            <div className="crm-autocomplete" ref={containerRef}>
                {props.children}
            </div>
        </CrmAutocompleteContext.Provider>
    );
}

function CrmAutocompleteSuggestions(
    props: PropsWithChildren<{ isOpen: boolean }>
) {
    const { containerRef } = useCrmAutocompleteContext();
    const { dropdownAnchorElement } = useContentDropdownContext();

    const rootElement = dropdownAnchorElement?.current ?? document.body;

    const style = useMemo(() => {
        const inputRect = containerRef.current?.getBoundingClientRect();
        const rootRect = rootElement.getBoundingClientRect();
        const gxSpacing = 4;

        console.debug(
            'CrmAutocompleteSuggestions - Calculating dropdown postion styles',
            props.isOpen
        );

        if (!inputRect || !rootRect) {
            return;
        }

        const scrollTop = rootElement.scrollTop;
        const scrollLeft = rootElement.scrollLeft;

        return {
            top: inputRect.bottom + scrollTop - rootRect.top + gxSpacing,
            left: inputRect.left + scrollLeft - rootRect.left,
        };
    }, [props.isOpen, containerRef, rootElement]);

    return createPortal(
        <div
            className={clsx('crm-autocomplete__options', {
                'is-open': props.isOpen,
            })}
            style={style}
        >
            {props.children}
        </div>,
        rootElement
    );
}

const CrmAutocompleteInput = forwardRef<
    HTMLInputElement,
    {
        isSelected: boolean;
        isError?: boolean;
    } & ComponentProps<'input'>
>(({ isSelected, isError, className, itemRef, ...rest }, ref) => {
    return (
        <input
            ref={ref}
            className={clsx(
                'crm-autocomplete__input',
                {
                    'is-selected': isSelected,
                    'is-error': isError,
                },
                className
            )}
            disabled={!!isSelected || isError}
            {...rest}
        />
    );
});

CrmAutocompleteInput.displayName = 'CrmAutocompleteInput';

function CrmAutocompleteLoading(props: { isLoading: boolean }) {
    return (
        <div data-testid="loading" className="crm-autocomplete__loading">
            {props.isLoading ? <Icon name="loader" className="gx-spin" /> : ''}
        </div>
    );
}

CrmAutocomplete.Loading = CrmAutocompleteLoading;
CrmAutocomplete.Input = CrmAutocompleteInput;
CrmAutocomplete.Suggestions = CrmAutocompleteSuggestions;
CrmAutocomplete.HighlightText = HighlightText;
CrmAutocomplete.ResetButton = CrmAutoCompleteInputReset;
