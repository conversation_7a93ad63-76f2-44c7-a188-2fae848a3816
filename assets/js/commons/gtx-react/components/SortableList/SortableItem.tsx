import type {
    DraggableSyntheticListeners,
    UniqueIdentifier,
} from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import type { ComponentProps, CSSProperties, PropsWithChildren } from 'react';
import { createContext, useContext, useMemo } from 'react';

type Props = {
    id: UniqueIdentifier;
    disabled?: boolean;
} & Omit<ComponentProps<'li'>, 'id'>;

interface Context {
    attributes: Record<string, any>;
    listeners: DraggableSyntheticListeners;
    ref(node: HTMLElement | null): void;
}

const SortableItemContext = createContext<Context>({
    attributes: {},
    listeners: undefined,
    ref() {},
});

export function SortableItem<ExtendedProps extends Props>({
    children,
    id,
    style: elementStyle,
    disabled,
    ...elementProps
}: PropsWithChildren<ExtendedProps>) {
    const {
        attributes,
        isDragging,
        listeners,
        setNodeRef,
        setActivatorNodeRef,
        transform,
        transition,
    } = useSortable({ id, disabled });
    const context = useMemo(
        () => ({
            attributes,
            listeners,
            ref: setActivatorNodeRef,
        }),
        [attributes, listeners, setActivatorNodeRef]
    );
    const style: CSSProperties = {
        opacity: isDragging ? 0.4 : undefined,
        transform: CSS.Translate.toString(transform),
        transition,
    };

    return (
        <SortableItemContext.Provider value={context}>
            <li
                ref={setNodeRef}
                {...elementProps}
                style={{ ...elementStyle, ...style }}
            >
                {children}
            </li>
        </SortableItemContext.Provider>
    );
}

export function DragHandler(
    props: PropsWithChildren<{
        className?: string;
        disabled?: boolean;
    }>
) {
    const { attributes, listeners, ref } = useContext(SortableItemContext);

    return (
        <button
            disabled={props.disabled}
            className={props.className}
            {...attributes}
            {...listeners}
            ref={ref}
        >
            {props.children}
        </button>
    );
}
