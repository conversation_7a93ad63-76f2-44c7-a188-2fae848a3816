import { expect, test, vi } from 'vitest';
import { SortableList } from './SortableList';
import { render, screen } from '#tests/react/testing-library-enhanced';

test('SortableList', async () => {
    const onChangeMock = vi.fn();
    const { user } = render(
        <SortableList
            items={[
                { id: 1, name: 'name1' },
                { id: 2, name: 'name2' },
            ]}
            renderItem={(item) => (
                <SortableList.Item id={item.id}>
                    <SortableList.DragHandler>
                        {item.name}
                    </SortableList.DragHandler>
                </SortableList.Item>
            )}
            onChange={onChangeMock}
        />
    );
    expect(screen.getByText('name1')).toBeInTheDocument();
    expect(screen.getByText('name2')).toBeInTheDocument();

    await user.tab();
    await user.tab();
    expect(screen.getByText('name2')).toHaveFocus();

    // Can't test as I would.
    // onChangeMock should be called when the order changes but It doesn't work as expected in test environment. 🤷‍♂️
});
