import type { Active } from '@dnd-kit/core';
import {
    DndContext,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import {
    SortableContext,
    arrayMove,
    sortableKeyboardCoordinates,
} from '@dnd-kit/sortable';
import type { ComponentProps, ReactNode } from 'react';
import React, { useMemo, useState } from 'react';

import { DragHandler, SortableItem } from './SortableItem';
import { SortableOverlay } from './SortableOverlay';

type BaseItem = { id: string | number };

type Props<T extends BaseItem> = {
    items: T[];
    onChange(items: T[]): void;
    renderItem(item: T): ReactNode;
} & Omit<ComponentProps<'ul'>, 'onChange'>;

export function SortableList<T extends BaseItem>({
    items,
    onChange,
    renderItem,
    ...elementProps
}: Props<T>) {
    const [active, setActive] = useState<Active | null>(null);

    const activeItem = useMemo(() => {
        return items.find(({ id }) => id === active?.id);
    }, [active, items]);
    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    return (
        <DndContext
            sensors={sensors}
            onDragStart={({ active }) => {
                setActive(active);
            }}
            onDragEnd={({ active, over }) => {
                if (over && active.id !== over?.id) {
                    const activeIndex = items.findIndex(
                        ({ id }) => id === active.id
                    );
                    const overIndex = items.findIndex(
                        ({ id }) => id === over.id
                    );
                    onChange(arrayMove(items, activeIndex, overIndex));
                }
                setActive(null);
            }}
            onDragCancel={() => {
                setActive(null);
            }}
        >
            <SortableContext items={items}>
                <ul {...elementProps}>
                    {items.map((item) => (
                        <React.Fragment key={item.id}>
                            {renderItem(item)}
                        </React.Fragment>
                    ))}
                </ul>
            </SortableContext>
            <SortableOverlay>
                {activeItem ? renderItem(activeItem) : null}
            </SortableOverlay>
        </DndContext>
    );
}

SortableList.Item = SortableItem;
SortableList.DragHandler = DragHandler;
