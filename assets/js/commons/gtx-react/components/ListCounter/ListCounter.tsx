import { Button } from '@gx-design/button';
import { Counter } from 'types/counters';
import { useMediaMatch } from '@gx-design/use-media-match';

type ListCounterProps = {
    type?: 'matches' | 'threads';
    data?: Counter;
    disabled?: boolean;
    href?: string;
};

export const ListCounter = ({
    type,
    data,
    disabled,
    href,
}: ListCounterProps) => {
    const isListMode = useMediaMatch('largeDesktop');

    if (disabled || !data || !type) {
        return (
            <Button size={!isListMode ? 'small' : undefined} disabled iconOnly>
                0
            </Button>
        );
    } else {
        const hasNew = data[type].new > 0;
        const hasTotal = data[type].total > 0;

        return (
            <Button
                size={!isListMode ? 'small' : undefined}
                disabled={!hasTotal}
                className="list-counter-button"
                as={hasTotal ? 'a' : 'button'}
                target="_blank"
                href={href && href}
                iconOnly
            >
                <>
                    {hasNew ? (
                        <>
                            <div className="gx-notification-badge gx-notification-badge--dot"></div>
                            {data[type].new}
                        </>
                    ) : (
                        data[type].total
                    )}
                </>
            </Button>
        );
    }
};
