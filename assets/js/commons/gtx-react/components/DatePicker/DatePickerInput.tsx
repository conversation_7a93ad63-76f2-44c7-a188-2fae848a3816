import {
    isAfter,
    isBefore,
    isSameDay,
    isWithinInterval,
    subDays,
} from 'date-fns';
import { useFormikContext } from 'formik';
import { FC, useCallback, useMemo, useRef, useState } from 'react';
import { CalendarProps } from 'react-calendar';
import { createPortal } from 'react-dom';

import {
    CalendarValuePiece,
    DatePicker,
    DatePickerOnChangeValue,
    DatePickerProps,
} from './DatePicker';
import { PatternFormikInput } from './PatternFormikInput';
import { useOnClickOutside } from '@gx-design/use-on-click-outside';
import {
    computeMinMaxDate,
    formatDate,
    generateInitialState,
    isValidDate,
    parseDate,
} from './utils';

export type DatePickerInputProps = {
    /** Formik field name */
    name: string;
    /** Label above the input */
    label?: string;
    /** Placeholder inside the input */
    placeholder?: string;
    /**
     * Initial calendar view; if omitted, start from days in month; possible values:
     *  "month" | "year" | "decade" | "century"
     */
    defaultCalendarView?: CalendarProps['defaultView'];
    /**
     * The least detailed view, defaults to `"year"`; possible values:
     *  "month" | "year" | "decade" | "century"
     */
    minDetail?: CalendarProps['minDetail'];
    /** minimum selectable date (days before this Date will be disabled) */
    minDate?: CalendarProps['minDate'];
    /** maximum selectable date (days after this Date will be disabled) */
    maxDate?: CalendarProps['maxDate'];
    /** ID to be passed to input element */
    id?: string;
} & Pick<DatePickerProps, 'nextAriaLabel' | 'prevAriaLabel'>;

/**
 * Input component (with 'calendar' icon as a left addon) with a DatePicker, to be used in a formik form, which stores the value as a formatted string "DD/MM/YYYY".
 *
 * User can either pick a date through:
 * - the `<DatePicker />` component (rendered in a portal);
 * - manually type the date through the `<PatternFormikInput />` masked input component; datepicker will read the input value when input will lose focus; if user types an invalid/incomplete date, form value will be reverted to last valid value (string date or empty).
 *
 * DatePicker will close when:
 * - user picks a date;
 * - user clicks outside the picker or input;
 * - user will press the 'Tab' key while the input is focused
 *
 * Event listeners to close the picker will be attached to `window` only when the picker is open.
 */
export const DatePickerInput: FC<DatePickerInputProps> = ({
    name,
    id,
    defaultCalendarView,
    minDetail = defaultCalendarView === 'decade' ? 'decade' : 'year',
    label,
    placeholder,
    minDate,
    maxDate,
    ...props
}) => {
    const { values, setFieldValue, initialValues } = useFormikContext<Date>();
    const { computedMinDate, computedMaxDate } = computeMinMaxDate({
        date: parseDate(initialValues[name]),
        minDate,
        maxDate,
    });

    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [value, setValue] = useState<CalendarValuePiece>(
        generateInitialState(values[name])
    );

    const inputRef = useRef<HTMLInputElement>(null);
    const portalRef = useRef<HTMLDivElement>(null);
    const inputRect = inputRef?.current?.getBoundingClientRect();

    /** absolute-positioning the portal which wraps the DatePicker based on the current input's rendered position */
    const wrapperStyle: React.CSSProperties = {
        top: inputRect && inputRect.y + inputRect.height + 10 + window.scrollY,
        left: inputRect?.x,
        position: 'absolute',
        // TODO: in future this should be replaced with a dedicated class name instead according to @ciabatta
        zIndex: 2600,
    };

    const hidePicker = useCallback(() => setIsOpen(false), []);

    /** Updating the internal state on calendar open, based on current formik value. */
    const showPicker = () => {
        if (!isOpen) {
            setValue(generateInitialState(values[name]));
        }
        setIsOpen(true);
    };

    const onDayPick = (
        newValue: DatePickerOnChangeValue,
        _event: React.MouseEvent<HTMLButtonElement, MouseEvent>
    ) => {
        if (!Array.isArray(newValue)) {
            setValue(newValue);
            setFieldValue(name, newValue ? formatDate(newValue) : '');
            setIsOpen(false);
        }
    };

    /** Reading the value from input to pick the new date, or revert to last valid date, or resetting the date to `null` */
    const onBlur = () => {
        const newDate = parseDate(values[name]);

        if (!newDate) {
            // new date is empty string
            setValue(null);
        } else if (
            isValidDate({
                date: newDate,
                minDate: computedMinDate,
                maxDate: computedMaxDate,
            })
        ) {
            setValue(newDate);
        } else {
            // new date is incomplete or invalid, reverting to last valid date
            const newFieldValue =
                value instanceof Date ? formatDate(value) : '';
            setFieldValue(name, newFieldValue);
        }
    };

    /** Closes calendar when `Tab` key is pressed */
    const onInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Tab') {
            setIsOpen(false);
        }
    };

    /** This mechanism is useful when min/maxDate are defined and the initialValue is before the `minDate` or after the `maxDate`. So this will disable the tiles in the range between initialValue and min/maxDate */
    const tileDisabled = useCallback(
        ({ date, view }) => {
            if (!minDate && !maxDate) {
                return false;
            }
            if (view === 'month' || view === 'year') {
                if (
                    minDate &&
                    computedMinDate &&
                    !isSameDay(minDate, computedMinDate) &&
                    isBefore(date, minDate)
                ) {
                    return isWithinInterval(date, {
                        start: computedMinDate,
                        end: subDays(minDate, 1),
                    });
                }
                if (
                    maxDate &&
                    computedMaxDate &&
                    !isSameDay(maxDate, computedMaxDate) &&
                    isAfter(date, maxDate)
                ) {
                    return isWithinInterval(date, {
                        start: maxDate,
                        end: computedMaxDate,
                    });
                }
                return false;
            }
            return false;
        },
        [minDate, maxDate, computedMinDate, computedMaxDate]
    );

    const refs = useMemo(() => [inputRef, portalRef], [inputRef, portalRef]);

    useOnClickOutside({
        refs,
        handler: hidePicker,
        shouldListen: isOpen,
    });

    return (
        <div>
            <PatternFormikInput
                id={id}
                ref={inputRef}
                name={name}
                onFocus={showPicker}
                onBlur={onBlur}
                label={label}
                onKeyDown={onInputKeyDown}
                placeholder={placeholder}
                addonIcon="calendar"
            />
            {isOpen &&
                createPortal(
                    <DatePicker
                        ref={portalRef}
                        wrapperStyle={wrapperStyle}
                        onChange={onDayPick}
                        value={value}
                        defaultView={defaultCalendarView}
                        minDetail={minDetail}
                        minDate={computedMinDate}
                        maxDate={computedMaxDate}
                        tileDisabled={
                            minDate || maxDate ? tileDisabled : undefined
                        }
                        {...props}
                    />,
                    document.body
                )}
        </div>
    );
};
