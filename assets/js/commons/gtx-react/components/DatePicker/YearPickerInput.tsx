import { AddonInputProps } from '@gx-design/addon-input';
import { useOnClickOutside } from '@gx-design/use-on-click-outside';
import { useFormikContext } from 'formik';
import React, { FC, useCallback, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import { GxFkAddonSimpleNumInput } from 'gtx-react/components/gx-formik';
import {
    DatePicker,
    DatePickerOnChangeValue,
    DatePickerProps,
} from './DatePicker';
import { isValidDate } from './utils';

const CALENDAR_ADDON: AddonInputProps['addon'] = {
    position: 'left',
    type: 'icon',
    value: 'calendar',
};

export type YearPickerInputProps = {
    /** Formik field name */
    name: string;
    /** Label above the input */
    label: string;
    /** Placeholder inside the input */
    placeholder?: string;
    /** minimum selectable year (years before this will be disabled) */
    minYear?: string;
    /** maximum selectable year (years after this will be disabled) */
    maxYear?: string;
    /** ID to be passed to input element */
    id?: string;
} & Pick<DatePickerProps, 'nextAriaLabel' | 'prevAriaLabel'>;

/**
 * Input component (with 'calendar' icon as a left addon) with a YearPicker, to be used in a formik form, which stores the value as a formatted string "YYYY".
 *
 * User can either pick a year through:
 * - the `<DatePicker />` component (rendered in a portal);
 * - manually type the date through the `<AddonInput />` (only digits are allowed - four max); datepicker will read the input value when input will lose focus; if user types an invalid year, form value will be reverted to last valid value (string date or empty).
 *
 * DatePicker will close when:
 * - user picks a year;
 * - user clicks outside the picker or input;
 * - user will press the 'Tab' key while the input is focused
 *
 * Event listeners to close the picker will be attached to `window` only when the picker is open.
 */
export const YearPickerInput: FC<YearPickerInputProps> = ({
    name,
    id,
    label,
    placeholder,
    minYear,
    maxYear,
    ...props
}) => {
    const { values, setFieldValue, initialValues } = useFormikContext<Date>();
    const minDate = minYear ? new Date(minYear) : undefined;
    const maxDate = maxYear ? new Date(maxYear) : undefined;

    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [value, setValue] = useState<string | null>(initialValues[name]);

    const inputRef = useRef<HTMLInputElement>(null);
    const portalRef = useRef<HTMLDivElement>(null);
    const inputRect = inputRef?.current?.getBoundingClientRect();

    /** absolute-positioning the portal which wraps the DatePicker based on the current input's rendered position */
    const wrapperStyle: React.CSSProperties = inputRect
        ? {
              top:
                  inputRect &&
                  inputRect.y + inputRect.height + 10 + window.scrollY,
              //   40 is the width of the addon
              left: inputRect?.x - 40,
              position: 'absolute',
              // TODO: in future this should be replaced with a dedicated class name instead according to @ciabatta
              zIndex: 2600,
          }
        : {};

    const hidePicker = useCallback(() => setIsOpen(false), []);

    /** Updating the internal state on calendar open, based on current formik value. */
    const showPicker = () => {
        if (!isOpen) {
            setValue(values[name]);
        }
        setIsOpen(true);
    };

    const onDayPick = (
        newValue: DatePickerOnChangeValue,
        _event: React.MouseEvent<HTMLButtonElement, MouseEvent>
    ) => {
        if (newValue instanceof Date) {
            const year = newValue.getFullYear().toString();
            setValue(year);
            setFieldValue(name, year);
            setIsOpen(false);
        }
    };

    /** Reading the value from input to pick the new date, or revert to last valid date, or resetting the date to `null` */
    const onBlur = () => {
        const newDate = new Date(values[name]);
        if (!values[name]) {
            setValue(null);
        } else if (
            isValidDate({
                date: newDate,
                minDate,
                maxDate,
            })
        ) {
            setValue(newDate.getFullYear().toString());
        } else {
            // new date is invalid, reverting to last valid date
            setFieldValue(name, value);
        }
    };

    /** Closes calendar when `Tab` key is pressed */
    const onInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Tab') {
            setIsOpen(false);
        }
    };

    const refs = useMemo(() => [inputRef, portalRef], [inputRef, portalRef]);

    useOnClickOutside({
        refs,
        handler: hidePicker,
        shouldListen: isOpen,
    });

    return (
        <div>
            <GxFkAddonSimpleNumInput
                id={id}
                ref={inputRef}
                name={name}
                onFocus={showPicker}
                onBlur={onBlur}
                label={label}
                onKeyDown={onInputKeyDown}
                placeholder={placeholder}
                addon={CALENDAR_ADDON}
            />
            {isOpen &&
                createPortal(
                    <DatePicker
                        ref={portalRef}
                        wrapperStyle={wrapperStyle}
                        onChange={onDayPick}
                        value={value}
                        defaultView="decade"
                        minDetail="decade"
                        maxDetail="decade"
                        minDate={minDate}
                        maxDate={maxDate}
                        {...props}
                    />,
                    document.body
                )}
        </div>
    );
};
