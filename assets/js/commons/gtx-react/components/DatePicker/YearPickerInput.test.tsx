import { render, screen } from '#tests/react/testing-library-enhanced';
import { Button } from '@gx-design/button';
import { format, addYears, startOfDecade, endOfDecade } from 'date-fns';
import { it as locale } from 'date-fns/locale';
import { Form, Formik, useFormikContext } from 'formik';
import { describe, expect, it } from 'vitest';

import { YearPickerInput, YearPickerInputProps } from './YearPickerInput';

const HEADER_SEPARATOR = '–';
const INITIAL_VALUE_FULL = { date: '1990' };
const INITIAL_VALUE_EMPTY = { date: '' };
// react-calendar decade is YY11-YY00
const TODAY_HEADER = `${format(
    addYears(startOfDecade(new Date()), 1),
    'yyyy'
)} ${HEADER_SEPARATOR} ${format(addYears(endOfDecade(new Date()), 1), 'yyyy')}`;

const ResetForm = () => {
    const { resetForm } = useFormikContext();
    return <Button onClick={() => resetForm()}>Reset</Button>;
};

const renderPicker = (
    initialValues: { date: string },
    pickerProps: Partial<YearPickerInputProps>
) =>
    render(
        <Formik
            enableReinitialize
            initialValues={initialValues}
            onSubmit={() => {}}
        >
            <Form>
                <p>DatePicker</p>
                <ResetForm />
                <YearPickerInput
                    id="date"
                    name="date"
                    label="label.date"
                    prevAriaLabel="Calendar prev"
                    nextAriaLabel="Calendar next"
                    {...pickerProps}
                />
            </Form>
        </Formik>
    );

describe('DatePickerInput', () => {
    it('should prefill the input and have the picker pre-filled accordingly to initialValue', async () => {
        const { user } = renderPicker(INITIAL_VALUE_FULL, {});

        const input = screen.getByRole('textbox', { name: /label.date/ });
        expect(input).toBeInTheDocument();
        expect(input).toHaveValue(INITIAL_VALUE_FULL.date);

        await user.click(input);
        const picker = screen.getByTestId('react-calendar-picker-wrapper');
        expect(picker).toBeVisible();
        const header = screen.getByRole('button', {
            name: `1981 ${HEADER_SEPARATOR} 1990`,
        });
        expect(header).toBeVisible();
        expect(picker).toContainElement(header);

        const day = screen.getByRole('button', { name: '1990' });
        expect(day).toBeVisible();
        expect(picker).toContainElement(day);
        expect(day).toHaveClass('react-calendar__tile--active');
    });
    it('should handle invalid input value by resetting last valid value', async () => {
        const { user } = renderPicker(INITIAL_VALUE_FULL, { minYear: '1900' });

        const input = screen.getByRole('textbox', { name: /label.date/ });
        await user.type(input, '{BackSpace>20/}{a}{b}{1}{8}{0}{0}');

        const outside = screen.getByText('DatePicker');
        await user.click(outside);

        expect(input).toHaveValue(INITIAL_VALUE_FULL.date);
    });
    it('should change the date by interacting with picker', async () => {
        const { user } = renderPicker(INITIAL_VALUE_FULL, {});

        const input = screen.getByRole('textbox', { name: /label.date/ });
        await user.click(input);
        const picker = screen.getByTestId('react-calendar-picker-wrapper');
        expect(picker).toBeVisible();
        const next = screen.getByRole('button', {
            name: 'Calendar next',
        });
        expect(next).toBeVisible();
        await user.click(next);

        const newDay = screen.getByRole('button', { name: '1999' });
        expect(newDay).toBeVisible();
        await user.click(newDay);

        expect(picker).not.toBeVisible();
        expect(input).toHaveValue('1999');
    });
    it('should change the date by typing a new valid date', async () => {
        const { user } = renderPicker(INITIAL_VALUE_FULL, {});

        const input = screen.getByRole('textbox', { name: /label.date/ });
        await user.type(input, '{BackSpace>20/}{1}{9}{9}{9}{Tab}');

        expect(input).toHaveValue('1999');
        await user.click(input);

        const header = screen.getByRole('button', {
            name: `1991 ${HEADER_SEPARATOR} 2000`,
        });
        expect(header).toBeVisible();
        const day = screen.getByRole('button', { name: '1999' });
        expect(day).toBeVisible();
        expect(day).toHaveClass('react-calendar__tile--active');
    });
    it('should reset the value when the form is resetted', async () => {
        const { user } = renderPicker(INITIAL_VALUE_EMPTY, {});
        const firstDayOfDecade = addYears(startOfDecade(new Date()), 1);

        const input = screen.getByRole('textbox', { name: /label.date/ });
        expect(input).toHaveValue('');
        await user.click(input);
        const picker = screen.getByTestId('react-calendar-picker-wrapper');
        expect(picker).toBeVisible();

        const firstYearButton = screen.getByRole('button', {
            name: format(firstDayOfDecade, 'yyyy', {
                locale,
            }),
        });
        expect(firstYearButton).toBeVisible();

        await user.click(firstYearButton);
        expect(picker).not.toBeVisible();
        expect(input).toHaveValue(format(firstDayOfDecade, 'yyyy'));

        const reset = screen.getByRole('button', { name: 'Reset' });
        await user.click(reset);

        expect(input).toHaveValue('');
        await user.click(input);
        const header = screen.getByRole('button', {
            name: TODAY_HEADER,
        });
        expect(header).toBeVisible();
        const selectedDays = screen.getAllByRole('button');
        for (let i = 0; i < selectedDays.length; i++) {
            const element = selectedDays[i];
            expect(element).not.toHaveClass('react-calendar__tile--active');
        }
    });
    it('should disable tiles accordingly to minYear prop', async () => {
        const firstYearOfDecade = addYears(startOfDecade(new Date()), 1);
        const secondYearOfDecade = addYears(firstYearOfDecade, 1);
        const { user } = renderPicker(INITIAL_VALUE_EMPTY, {
            minYear: format(secondYearOfDecade, 'yyyy'),
        });

        const input = screen.getByRole('textbox', { name: /label.date/ });
        await user.click(input);

        const firstDayButton = screen.getByRole('button', {
            name: format(firstYearOfDecade, 'yyyy', {
                locale,
            }),
        });
        expect(firstDayButton).toBeVisible();
        expect(firstDayButton).toBeDisabled();

        const prevButton = screen.getByRole('button', {
            name: 'Calendar prev',
        });
        expect(prevButton).toBeVisible();
        expect(prevButton).toBeDisabled();
    });
});
