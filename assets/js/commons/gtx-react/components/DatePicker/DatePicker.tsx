import { forwardRef, FC } from 'react';
import { Icon, IconProps } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { Calendar, CalendarProps } from 'react-calendar';
import { getAgencyLanguage } from 'lib/languages';

export type CalendarValuePiece = Date | null;
export type DatePickerOnChangeValue =
    | CalendarValuePiece
    | [CalendarValuePiece, CalendarValuePiece];

export type DatePickerProps = {
    /** style for the div which wraps the calendar; useful for absolute-positioning the calendar under the input */
    wrapperStyle?: React.CSSProperties;
    /** if `true`, an arrow from the top-side of the calendar portal will be shown; defaults to `false` */
    showArrowIndicator?: boolean;
    /** style for the "arrow" which indicates the current focused input, using "left" property to animate it; if `showArrowIndicator` is set to `false`, this prop has no effect.  */
    arrowStyle?: React.CSSProperties;
    /** optional prop to tell where the arrow should starts from (intelligent positiong of the whole `DatePicker`, right now implemented only for `DateRangePickerInput`); defaults to `bottom`. */
    arrowStartsFrom?: 'top' | 'bottom';
} & CalendarProps;

const NavigationButton: FC<{ direction: 'left' | 'right' }> = ({
    direction,
}) => (
    <Button as="div" iconOnly>
        <Icon name={`arrow-${direction}` as IconProps['name']} />
    </Button>
);

/**
 * Wrapper for react-calendar with all default values and styles we need.
 *
 * {@link https://github.com/wojtekmaj/react-calendar}
 */
export const DatePicker = forwardRef<HTMLDivElement, DatePickerProps>(
    (
        {
            // Custom props below
            wrapperStyle,
            showArrowIndicator,
            arrowStyle,
            // Calendar props below
            selectRange,
            prev2Label = null,
            next2Label = null,
            prevLabel = <NavigationButton direction="left" />,
            nextLabel = <NavigationButton direction="right" />,
            showNeighboringMonth = false,
            minDetail = 'year',
            arrowStartsFrom = 'bottom',
            ...props
        },
        ref
    ) => (
        <div
            ref={ref}
            className={`react-calendar__portal-wrapper ${
                showArrowIndicator ? 'react-calendar-range' : ''
            }`}
            style={wrapperStyle}
            data-testid="react-calendar-picker-wrapper"
        >
            {showArrowIndicator && (
                <span
                    className={`react-calendar-range__arrow starts-from-${arrowStartsFrom}`}
                    style={arrowStyle}
                />
            )}
            <Calendar
                selectRange={selectRange}
                allowPartialRange={selectRange}
                prev2Label={prev2Label}
                next2Label={next2Label}
                prevLabel={prevLabel}
                nextLabel={nextLabel}
                showNeighboringMonth={showNeighboringMonth}
                minDetail={minDetail}
                locale={getAgencyLanguage()}
                {...props}
            />
        </div>
    )
);
