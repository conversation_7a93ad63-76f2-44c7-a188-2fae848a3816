import { isValid, format, parse, isBefore, isAfter, isSameDay } from 'date-fns';
import { DEFAULT_PATTERN } from './constants';
// no need to import locales from date-fns because, for pickers, we just need to handle dates with no translated words or other language-specific features

/** Wrapping date-fns format function and injecting default options. */
export const formatDate = (date: Date): string => {
    return format(date, DEFAULT_PATTERN);
};
/** Wrapping date-fns parse function and injecting default options. */
export const parseDate = (input: string): Date | null => {
    try {
        if (!input) {
            throw new Error('No input date has been provided!');
        }
        return parse(input, DEFAULT_PATTERN, new Date());
    } catch (error) {
        return null;
    }
};

const generateInitialDate = (x: string) => {
    const parsed = parseDate(x);
    return isValid(parsed) ? parsed : null;
};

/**
 * Generates initial date state for both datepickers
 * @param args the initial form value (`string` for single date-picker, `[string, string]` for date-range-picker)
 * @return {Date | [Date, Date]} return the parsed Date (or null)
 */
export function generateInitialState(args: string): Date;
// eslint-disable-next-line no-redeclare
export function generateInitialState(args: [string, string]): [Date, Date];
// eslint-disable-next-line no-redeclare
export function generateInitialState(args: string | [string, string]): Date | null | [Date | null, Date | null] {
    if (Array.isArray(args) && args.length === 2) {
        return [generateInitialDate(args[0]), generateInitialDate(args[1])];
    }
    if (typeof args === 'string') {
        return generateInitialDate(args);
    }
    return null;
}

export type IsValidDateInput = {
    date: Date | null;
    minDate?: Date;
    maxDate?: Date;
};
/** extending dfns `isValid` with checks on optional min/max date */
export const isValidDate = ({ date, minDate, maxDate }: IsValidDateInput) => {
    if (!date || !isValid(date)) {
        return false;
    } else {
        const isAfterMin = minDate && isValid(minDate) && !isSameDay(date, minDate) ? isAfter(date, minDate) : true;
        const isBeforeMax = maxDate && isValid(maxDate) && !isSameDay(date, maxDate) ? isBefore(date, maxDate) : true;
        return isAfterMin && isBeforeMax;
    }
};

type ComputeMinMaxDateOutput = {
    computedMinDate?: Date;
    computedMaxDate?: Date;
};

export const computeMinMaxDate = ({ date, minDate, maxDate }: IsValidDateInput): ComputeMinMaxDateOutput => {
    let computedMinDate = minDate;
    let computedMaxDate = maxDate;

    if (date && isValid(date)) {
        if (minDate && isBefore(date, minDate)) {
            computedMinDate = date;
        }
        if (maxDate && isAfter(date, maxDate)) {
            computedMaxDate = date;
        }
    }

    return { computedMinDate, computedMaxDate };
};
