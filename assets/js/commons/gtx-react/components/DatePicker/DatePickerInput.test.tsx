import { render, screen } from '#tests/react/testing-library-enhanced';
import { Button } from '@gx-design/button';
import { addDays, format, startOfMonth, subDays } from 'date-fns';
import { it as locale } from 'date-fns/locale';
import { Form, Formik, useFormikContext } from 'formik';
import { describe, expect, it } from 'vitest';

import { DatePickerInput, DatePickerInputProps } from './DatePickerInput';

const INITIAL_VALUE_FULL = { date: '16/10/1990' };
const INITIAL_VALUE_EMPTY = { date: '' };
const TODAY_MONTH_YEAR_LONG = format(new Date(), 'MMMM yyyy', { locale });

const ResetForm = () => {
    const { resetForm } = useFormikContext();
    return <Button onClick={() => resetForm()}>Reset</Button>;
};

const renderPicker = (
    initialValues: { date: string },
    pickerProps: Partial<DatePickerInputProps>
) =>
    render(
        <Formik
            enableReinitialize
            initialValues={initialValues}
            onSubmit={() => {}}
        >
            <Form>
                <p>DatePicker</p>
                <ResetForm />
                <DatePickerInput
                    id="date"
                    name="date"
                    label="label.date"
                    prevAriaLabel="Calendar prev"
                    nextAriaLabel="Calendar next"
                    {...pickerProps}
                />
            </Form>
        </Formik>
    );

describe('DatePickerInput', () => {
    it('should prefill the input and have the picker pre-filled accordingly to initialValue', async () => {
        const { user } = renderPicker(INITIAL_VALUE_FULL, {});

        const input = screen.getByRole('textbox', { name: /label.date/ });
        expect(input).toBeInTheDocument();
        expect(input).toHaveValue(INITIAL_VALUE_FULL.date);

        await user.click(input);
        const picker = screen.getByTestId('react-calendar-picker-wrapper');
        expect(picker).toBeVisible();
        const header = screen.getByRole('button', { name: 'ottobre 1990' });
        expect(header).toBeVisible();
        expect(picker).toContainElement(header);

        const day = screen.getByRole('button', { name: '16 ottobre 1990' });
        expect(day).toBeVisible();
        expect(picker).toContainElement(day);
        expect(day).toHaveClass('react-calendar__tile--active');
    });
    it('should handle invalid input value by resetting last valid value', async () => {
        const { user } = renderPicker(INITIAL_VALUE_FULL, {});

        const input = screen.getByRole('textbox', { name: /label.date/ });
        await user.type(input, '{BackSpace>20/}{1}{0}{1}{0}');

        const outside = screen.getByText('DatePicker');
        await user.click(outside);

        expect(input).toHaveValue(INITIAL_VALUE_FULL.date);
    });
    it('should change the date by interacting with picker', async () => {
        const { user } = renderPicker(INITIAL_VALUE_FULL, {});

        const input = screen.getByRole('textbox', { name: /label.date/ });
        await user.click(input);
        const picker = screen.getByTestId('react-calendar-picker-wrapper');
        expect(picker).toBeVisible();
        const next = screen.getByRole('button', {
            name: 'Calendar next',
        });
        expect(next).toBeVisible();
        await user.click(next);

        const newDay = screen.getByRole('button', { name: '10 novembre 1990' });
        expect(newDay).toBeVisible();
        await user.click(newDay);

        expect(picker).not.toBeVisible();
        expect(input).toHaveValue('10/11/1990');
    });
    it('should change the date by typing a new valid date', async () => {
        const { user } = renderPicker(INITIAL_VALUE_FULL, {});

        const input = screen.getByRole('textbox', { name: /label.date/ });
        await user.type(input, '{BackSpace>20/}{1}{0}{1}{1}{1}{9}{9}{0}{Tab}');

        expect(input).toHaveValue('10/11/1990');
        await user.click(input);

        const header = screen.getByRole('button', { name: 'novembre 1990' });
        expect(header).toBeVisible();
        const day = screen.getByRole('button', { name: '10 novembre 1990' });
        expect(day).toBeVisible();
        expect(day).toHaveClass('react-calendar__tile--active');
    });
    it('should reset the value when the form is resetted', async () => {
        const { user } = renderPicker(INITIAL_VALUE_EMPTY, {});
        const firstDayOfMonth = startOfMonth(new Date());

        const input = screen.getByRole('textbox', { name: /label.date/ });
        expect(input).toHaveValue('');
        await user.click(input);
        const picker = screen.getByTestId('react-calendar-picker-wrapper');
        expect(picker).toBeVisible();

        const firstDayButton = screen.getByRole('button', {
            name: format(firstDayOfMonth, 'd MMMM yyyy', {
                locale,
            }).toLowerCase(),
        });
        expect(firstDayButton).toBeVisible();

        await user.click(firstDayButton);
        expect(picker).not.toBeVisible();
        expect(input).toHaveValue(format(firstDayOfMonth, 'dd/MM/yyyy'));

        const reset = screen.getByRole('button', { name: 'Reset' });
        await user.click(reset);

        expect(input).toHaveValue('');
        await user.click(input);
        const header = screen.getByRole('button', {
            name: TODAY_MONTH_YEAR_LONG,
        });
        expect(header).toBeVisible();
        const selectedDays = screen.getAllByRole('button');
        for (let i = 0; i < selectedDays.length; i++) {
            const element = selectedDays[i];
            expect(element).not.toHaveClass('react-calendar__tile--active');
        }
    });
    it('should disable tiles accordingly to minDate prop', async () => {
        const firstDayOfMonth = startOfMonth(new Date());
        const secondDayOfMonth = addDays(firstDayOfMonth, 1);
        const { user } = renderPicker(INITIAL_VALUE_EMPTY, {
            minDate: secondDayOfMonth,
        });

        const input = screen.getByRole('textbox', { name: /label.date/ });
        await user.click(input);

        const firstDayButton = screen.getByRole('button', {
            name: format(firstDayOfMonth, 'd MMMM yyyy', {
                locale,
            }).toLowerCase(),
        });
        expect(firstDayButton).toBeVisible();
        expect(firstDayButton).toBeDisabled();

        const prevButton = screen.getByRole('button', {
            name: 'Calendar prev',
        });
        expect(prevButton).toBeVisible();
        expect(prevButton).toBeDisabled();
    });
    it('should properly disable tiles when minDate prop is set and initial value is before minDate', async () => {
        const { user } = renderPicker(INITIAL_VALUE_FULL, {
            minDate: new Date(),
        });

        const input = screen.getByRole('textbox', { name: /label.date/ });
        await user.click(input);

        const picker = screen.getByTestId('react-calendar-picker-wrapper');
        expect(picker).toBeVisible();

        const prevButton = screen.getByRole('button', {
            name: 'Calendar prev',
        });
        expect(prevButton).toBeVisible();
        expect(prevButton).toBeDisabled();

        const nextButton = screen.getByRole('button', {
            name: 'Calendar next',
        });
        expect(nextButton).toBeVisible();
        expect(nextButton).not.toBeDisabled();

        const selectedDate = new Date('1990-10-16');
        const selectedDay = screen.getByRole('button', {
            name: format(selectedDate, 'd MMMM yyyy', { locale }).toLowerCase(),
        });
        expect(selectedDay).toBeDisabled();
        expect(selectedDay).toHaveClass('react-calendar__tile--active');

        const prevDate = subDays(selectedDate, 1);
        const prevDay = screen.getByRole('button', {
            name: format(prevDate, 'd MMMM yyyy', { locale }).toLowerCase(),
        });
        expect(prevDay).toBeDisabled();

        const nextDate = subDays(selectedDate, 1);
        const nextDay = screen.getByRole('button', {
            name: format(nextDate, 'd MMMM yyyy', { locale }).toLowerCase(),
        });
        expect(nextDay).toBeDisabled();
    });
});
