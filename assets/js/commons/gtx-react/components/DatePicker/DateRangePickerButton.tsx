import gtxLoggedUser from '@getrix/common/js/gtx-logged-user';
import { Button, ButtonProps } from '@gx-design/button';
import { Icon, IconProps } from '@gx-design/icon';
import { addDays, isSameDay, isWithinInterval } from 'date-fns';
import { useWindowSize } from 'gtx-react/hooks/useWindowSize';
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import {
    CalendarValuePiece,
    DatePicker,
    DatePickerOnChangeValue,
    DatePickerProps,
} from './DatePicker';
import { useOnClickOutside } from '@gx-design/use-on-click-outside';

const LOCALE = gtxLoggedUser('language').replace('_', '-');

export type DateRangePickerButtonProps = {
    /** Range value. */
    value: [CalendarValuePiece, CalendarValuePiece] | null;
    /** External onChange, fired when component has both start and end range. */
    onDateChange: (x: [CalendarValuePiece, CalendarValuePiece]) => void;
    /** Icon name into button, defaults to `calendar`. */
    iconName?: IconProps['name'];
    /** Change this to let the calendar enlarge to the left or to the right, based on button's position. */
    calendarPosition?: 'left' | 'right';
    /** Variant prop to be passed to the outside Button component */
    buttonVariant?: ButtonProps['variant'];
    /** Defaults to 0, negative values are not allowed; it is responsible to allow a date range of minimun length (for example, if it is set to 1, range length should be at least of 2 days). */
    minimumNights?: number;
} & Omit<DatePickerProps, 'selectRange' | 'allowPartialRange' | 'value'>;

/**
 * Component consisting on a button which opens a datepicker (into a portal) to select a range. Range could be selected from the Calendar, or could be changed externally.
 *
 * DatePicker will close when:
 * - user picks a date range;
 * - user clicks outside the picker or input;
 *
 * Event listeners to close the picker will be attached to `window` only when the picker is open.
 */
export const DateRangePickerButton: FC<DateRangePickerButtonProps> = ({
    iconName = 'calendar',
    onDateChange,
    calendarPosition = 'left',
    buttonVariant,
    minimumNights = 0,
    value,
    ...calendarProps
}) => {
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [tempValue, setTempValue] = useState<
        [CalendarValuePiece, CalendarValuePiece] | null
    >(value);

    const size = useWindowSize();
    const buttonRef = useRef<HTMLButtonElement>(null);
    const portalRef = useRef<HTMLDivElement>(null);
    const buttonRect = buttonRef?.current?.getBoundingClientRect();

    const wrapperStyle: React.CSSProperties = {
        top:
            buttonRect &&
            buttonRect.y + buttonRect.height + 10 + window.scrollY,
        left: calendarPosition === 'right' ? buttonRect?.x : undefined,
        right:
            calendarPosition === 'left'
                ? (size?.width || 0) -
                  ((buttonRect?.x || 0) + (buttonRect?.width || 0))
                : undefined,
        position: 'absolute',
        zIndex: 5,
    };

    const togglePicker = () => setIsOpen((prevState) => !prevState);

    const closePickerAndReset = () => {
        setIsOpen(false);
        setTempValue(value);
    };

    const onDayPick = (
        newValue: DatePickerOnChangeValue,
        _event: React.MouseEvent<HTMLButtonElement, MouseEvent>
    ) => {
        if (Array.isArray(newValue)) {
            setTempValue(newValue);
            if (newValue[0] !== null && newValue[1] !== null) {
                // calling onChange when both values are valid
                onDateChange(newValue);
                togglePicker();
            }
        }
    };
    /** Useful with `minimumNights` prop, to accordingly disable dates to achieve that functionality. */
    const tileDisabled = useCallback(
        ({ date, view }) => {
            if (
                view === 'month' &&
                Array.isArray(tempValue) &&
                tempValue[0] &&
                !tempValue[1]
            ) {
                return isWithinInterval(date, {
                    start: tempValue[0],
                    end: addDays(tempValue[0], minimumNights - 1),
                });
            }
            return false;
        },
        [tempValue, minimumNights]
    );
    /** Useful with `minimumNight` prop, to override styles for disabled (but valid for range) tiles. */
    const tileClassname = useCallback(
        ({ date, view }) => {
            if (
                view === 'month' &&
                Array.isArray(tempValue) &&
                tempValue[0] &&
                !tempValue[1] &&
                isSameDay(tempValue[0], date)
            ) {
                return 'react-calendar-range-start';
            }
        },
        [tempValue]
    );

    const refs = useMemo(() => [buttonRef, portalRef], [buttonRef, portalRef]);

    useOnClickOutside({
        refs,
        handler: closePickerAndReset,
        shouldListen: isOpen,
    });

    useEffect(() => setTempValue(value), [value]);

    return (
        <>
            <Button
                ref={buttonRef}
                iconOnly
                onClick={togglePicker}
                variant={buttonVariant}
            >
                <Icon name={iconName} />
            </Button>
            {isOpen &&
                createPortal(
                    <DatePicker
                        ref={portalRef}
                        wrapperStyle={wrapperStyle}
                        onChange={onDayPick}
                        locale={LOCALE}
                        {...calendarProps}
                        selectRange
                        allowPartialRange
                        value={tempValue}
                        tileDisabled={minimumNights ? tileDisabled : undefined}
                        tileClassName={
                            minimumNights ? tileClassname : undefined
                        }
                    />,
                    document.body
                )}
        </>
    );
};
