import { HelperText } from '@gx-design/helper-text';
import { Icon, IconProps } from '@gx-design/icon';
import clsx from 'clsx';
import { useField } from 'formik';
import { forwardRef } from 'react';
import {
    PatternFormat as BasePatternFormat,
    PatternFormatProps as BasePatternFormatProps,
} from 'react-number-format';

import { useFormikValidateFieldOnChangeIfError } from 'gtx-react/hooks/useFormikValidateFieldOnChangeIfError';

const MASK_CHAR = '-';

export type PatternFormikInputProps = {
    /** Input label; if omitted, no blank space will be left above the input */
    label?: string;
    /** Icon name for the addon input; if omitted, a normal input will be rendered */
    addonIcon?: IconProps['name'];
    /** boolean to bound the ref to the input (if `true`); defaults to `false` */
    isRangeInput?: boolean;
    /** Hide error messages; defaults to `false`  */
    hideErrors?: boolean;
} & React.InputHTMLAttributes<HTMLInputElement>;

/**
 * Input component, bound to formik, which includes `<PatternFormat />` to use a masked input;
 * it can accept a `ref` (bound to the wrapper div for the single datepicker, or the the input
 * for date-range-picker); it can render an input with addon if the `addonIcon` prop has a value.
 *
 * {@link https://github.com/s-yadav/react-number-format}
 */
export const PatternFormikInput = forwardRef<
    HTMLInputElement | HTMLDivElement,
    PatternFormikInputProps & { name: string }
>(({ label, name, addonIcon, isRangeInput = false, ...props }, ref) => {
    const [fieldProps, fieldMeta] = useField(name);

    useFormikValidateFieldOnChangeIfError(name);

    return (
        <>
            {label ? (
                <label className="gx-label" htmlFor={props.id}>
                    {label}
                </label>
            ) : null}
            <div
                ref={addonIcon ? ref : undefined}
                className={clsx([
                    {
                        'gx-time_picker gx-input-addon-wrapper':
                            Boolean(addonIcon),
                    },
                    { ['DateInput']: !addonIcon },
                    {
                        'gx-input-addon-wrapper--negative': Boolean(
                            fieldMeta?.error
                        ),
                    },
                ])}
            >
                {addonIcon && (
                    <div className="gx-input-addon gx-input-addon--withIcon">
                        <Icon name={addonIcon} />
                    </div>
                )}
                <PatternInput
                    className={clsx([
                        {
                            'gx-input--withAddon': Boolean(addonIcon),
                            'gx-input--negative': fieldMeta?.error,
                        },
                    ])}
                    format="##/##/####"
                    ref={isRangeInput ? ref : undefined}
                    {...fieldProps}
                    {...props}
                    type="text"
                    defaultValue={undefined}
                />
            </div>
            {fieldMeta?.error && (
                <HelperText text={fieldMeta.error} style="error" />
            )}
        </>
    );
});

export const PatternInput = forwardRef<
    HTMLInputElement | HTMLDivElement,
    BasePatternFormatProps
>(({ className, ...props }, ref) => {
    return (
        <BasePatternFormat
            getInputRef={ref}
            className={clsx(['gx-input', className])}
            mask={MASK_CHAR}
            {...props}
        />
    );
});

PatternInput.displayName = 'PatternInput';
