import { But<PERSON> } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { Loader } from '@gx-design/loader';
import { Modal } from '@gx-design/modal';
import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita/babelfish';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { FC, useRef, useState } from 'react';

import { Image, ImageEditor } from 'gtx-react/components';
import { GxNavigationBus } from 'lib/gx-navigation-bus';
import { checkIsBase64Image, convertBase64ToBlob } from 'lib/images';
import { UPDATE_AGENCY } from '../EditProfileForm/constants';
import { changeProfilePicApi } from './api';
import { queries } from './utils';

type Props = {
    userId: number;
    /** Function to retrieve the initial value for profile pic url, to be passed to `use<PERSON><PERSON><PERSON>`; since `staleTime` is set to `Infinity`, this function will be called just one time. */
    queryFn: () => Promise<string | null>;
};
const useProfilePic = (
    userId: number,
    queryFn: () => Promise<string | null>
) => {
    const result = useQuery({
        queryKey: queries.profilePicKey(userId),
        queryFn,
        staleTime: Infinity,
    });

    return { ...result, profilePicUrl: result?.data };
};
/**
 * Component for the user profile page, responsible to:
 * - show the profile image if exists (using `<Image />` component), or show a button to open a modal to upload an image;
 * - show two buttons to update and delete the profile image, if it exists.
 *
 * It also integrates the `<ImageEditor />` component to edit the image.
 *
 * It should live inside a `QueryClient`.
 */
export const ProfileImage: FC<Props> = ({ userId, queryFn }) => {
    const queryClient = useQueryClient();
    const { profilePicUrl } = useProfilePic(userId, queryFn);

    const [isRemoveModalOpen, setIsRemoveModalOpen] = useState<boolean>(false);
    const [newPic, setNewPic] = useState<string | ArrayBuffer | null>(null);
    const [showEditor, setShowEditor] = useState<boolean>(false);

    const changePicInputRef = useRef<HTMLInputElement>(null);

    const { showNotification } = useNotifyContext();

    const onMutationSuccess =
        (isEdit: boolean) => (data, variable, context) => {
            queryClient.setQueryData(
                queries.profilePicKey(userId),
                isEdit ? data?.url : null
            );
            showNotification({
                type: 'success',
                message: trans(
                    `userProfile.image.${isEdit ? 'uploaded' : 'removedSucces'}`
                ),
            });
            GxNavigationBus.dispatchEvent({
                type: UPDATE_AGENCY,
                payload: { agentAvatar: isEdit ? data?.url : null },
            });
        };

    const onMutationError = (isEdit: boolean) => () => {
        showNotification({
            type: 'error',
            message: trans(
                `userProfile.image.${isEdit ? 'failed' : 'removedError'}`
            ),
        });
    };

    const { mutate: deletePic, isPending: isLoadingDelete } = useMutation({
        mutationFn: changeProfilePicApi,
        onSuccess: onMutationSuccess(false),
        onError: onMutationError(false),
    });

    const { mutate: updatePic, isPending: isLoadingUpdate } = useMutation({
        mutationFn: changeProfilePicApi,
        onSuccess: onMutationSuccess(true),
        onError: onMutationError(true),
    });

    const openModal = (open: boolean) => () => setIsRemoveModalOpen(open);

    const onRemoveConfirm = () => {
        setIsRemoveModalOpen(false);
        deletePic({ userId });
    };

    const onChangePicClick = (e) => {
        e.preventDefault();
        changePicInputRef.current?.click();
    };

    const onPicChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target?.files?.[0];
        if (!file) {
            return;
        }

        const reader = new FileReader();

        reader.readAsDataURL(file);
        reader.onerror = () => {
            // never happened to fall here but let's prevent
            showNotification({
                type: 'error',
                message: trans('media.acceptFileTypes'),
            });
        };
        reader.onloadend = (event: ProgressEvent<FileReader>) => {
            try {
                const imageToEdit = event.target?.result as string;
                if (!checkIsBase64Image(imageToEdit)) {
                    throw new Error('Uploaded file is not an image');
                }
                setNewPic(imageToEdit);
                setShowEditor(true);
            } catch (error) {
                console.error('Error loading image:', error);
                showNotification({
                    type: 'error',
                    message: trans('media.acceptFileTypes'),
                });
            }
        };
    };

    const onEditorClose = () => setShowEditor(false);

    const onPicSave = async (file: Blob) => {
        const reader = new FileReader();

        reader.readAsDataURL(file);
        reader.onerror = () => {
            console.error('Reader onError');
        };
        // reader.onprogress = () => {
        //     setIsLoading(true);
        // };
        reader.onloadend = async () => {
            setShowEditor(false);
            const data = new FormData();
            // reader.result is a base64 string
            data.append(
                'blob',
                convertBase64ToBlob(reader.result as string),
                'blob'
            );
            updatePic({ userId, data });
        };
    };

    return (
        <div className="user-profile-pic__wrapper">
            <div className="user-profile-pic">
                {profilePicUrl ? (
                    <>
                        <Image src={profilePicUrl} />
                        <div className="user-profile-pic__action gx-multiButton">
                            <Button
                                iconOnly
                                size="small"
                                onClick={onChangePicClick}
                            >
                                <Icon name="pencil" />
                            </Button>
                            <Button
                                iconOnly
                                size="small"
                                onClick={openModal(true)}
                            >
                                <Icon name="bin" />
                            </Button>
                        </div>
                    </>
                ) : (
                    <div
                        className="user-profile-pic__empty"
                        onClick={onChangePicClick}
                    >
                        <Icon name="user-round" />
                        <span className="label-replica">
                            {trans('label.add_photo')}
                        </span>
                    </div>
                )}
                <input
                    type="file"
                    id="uploadUserImage"
                    multiple={false}
                    className="hidden"
                    accept="image/*"
                    onChange={onPicChange}
                    ref={changePicInputRef}
                />
                {(isLoadingDelete || isLoadingUpdate) && <Loader />}
                <Modal
                    size="small"
                    isOpen={isRemoveModalOpen}
                    title={trans('userProfile.image.removeConfirmTitle')}
                    onClose={openModal(false)}
                    onConfirm={onRemoveConfirm}
                    footer={
                        <>
                            <Button variant="ghost" onClick={openModal(false)}>
                                {trans('label.cancel')}
                            </Button>
                            <Button
                                type="submit"
                                variant="accent"
                                onClick={onRemoveConfirm}
                            >
                                {trans('label.confirm')}
                            </Button>
                        </>
                    }
                >
                    <div>{trans('userProfile.image.removeConfirmMessage')}</div>
                </Modal>
                {showEditor && (
                    <ImageEditor
                        onClose={onEditorClose}
                        onSave={onPicSave}
                        image={newPic}
                        mode="raw"
                        ratio="1:1"
                    />
                )}
            </div>
        </div>
    );
};
