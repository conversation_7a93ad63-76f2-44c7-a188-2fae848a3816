import { http } from '@pepita/http';

const PROFILE_PIC_ENDPOINT = '/impostazioni/utenti/{userId}/image';

export const changeProfilePicApi = async ({ userId, data }: { userId: number; data?: FormData }) => {
    if (typeof userId !== 'number') {
        return;
    }

    const url = PROFILE_PIC_ENDPOINT.replace('{userId}', `${userId}`);
    let response;

    if (!data) {
        // delete image
        response = await http.delete(url).json();
    } else {
        // update image
        response = await http.post(url, { form: data }).json();
    }
    return response;
};
