import { AddonInput, AddonInputProps } from '@gx-design/addon-input';
import { ButtonInput, ButtonInputProps } from '@gx-design/button-input';
import { Checkbox, CheckboxProps } from '@gx-design/checkbox';
import { Input, InputProps } from '@gx-design/input';
import { Radio, RadioProps } from '@gx-design/radio';
import { Select, SelectProps } from '@gx-design/select';
import { Textarea, TextareaProps } from '@gx-design/textarea';
import { useField } from 'formik';
import { FC, forwardRef } from 'react';

import { useFormikFormattedNumField } from 'gtx-react/hooks/useFormikFormattedNumField';
import { useFormikSimpleNumField } from 'gtx-react/hooks/useFormikSimpleNumField';
import { useFormikValidateFieldOnChangeIfError } from 'gtx-react/hooks/useFormikValidateFieldOnChangeIfError';
import { GxFkRangeInput } from './GxFkRangeInput';

// wrapping @gx-desing input components for formik usage

// maybe in the future we can add more functions to this CustomProps, like onFocus, onBlur etc...
type CustomProps<HTMLElement> = {
    /**
     * Use this ONLY if you need to do other things after changing the input's value, but DO NOT CHANGE input's value, formik's onChange is already executing!
     */
    onChange?: (_event: React.ChangeEvent<HTMLElement>) => void;
    name: string;
};

/**
 * intercepting optional custom `onChange`, executing it along with the original `onChange` from formik
 **/
const onChange =
    (onChangeFromFormik: Function, onChangeFromProps?: Function) =>
    (e: React.ChangeEvent<HTMLElement>): void => {
        onChangeFromFormik(e);
        if (onChangeFromProps && typeof onChangeFromProps === 'function') {
            onChangeFromProps(e);
        }
    };

export type FormattedNumberInputProps<T> = T &
    Pick<Intl.NumberFormatOptions, 'maximumFractionDigits'>;

export type SimpleNumberInputProps<T> = T & {
    /** Max length for the number (like 4 for years) */
    maximumDigits?: number;
};

export const GxFkInput: FC<
    Omit<InputProps, 'onChange'> & CustomProps<HTMLInputElement>
> = (props) => {
    const [fieldProps, fieldMeta] = useField(props.name);

    useFormikValidateFieldOnChangeIfError(props.name);

    return (
        <Input
            error={fieldMeta.error}
            {...fieldProps}
            {...props}
            onChange={onChange(fieldProps.onChange, props.onChange)}
        />
    );
};

export const GxFkRadio: FC<
    Omit<RadioProps, 'onChange'> & CustomProps<HTMLInputElement>
> = ({ name, value, label, ...props }) => {
    const [fieldProps, fieldMeta] = useField({ type: 'radio', name, value });

    return (
        <Radio
            error={fieldMeta.error}
            {...fieldProps}
            {...props}
            label={label}
            onChange={onChange(fieldProps.onChange, props.onChange)}
        />
    );
};

export const GxFkSelect: FC<
    Omit<SelectProps, 'onChange'> & CustomProps<HTMLSelectElement>
> = (props) => {
    const [fieldProps, fieldMeta] = useField(props.name);

    useFormikValidateFieldOnChangeIfError(props.name);

    return (
        <Select
            error={fieldMeta.error}
            id={props.id || props.name}
            {...fieldProps}
            {...props}
            onChange={onChange(fieldProps.onChange, props.onChange)}
        />
    );
};

/**
 * Simple numeric input (represented as a string), user cannot type non-digit chars.
 * Optional `maximumDigits` prop to set maximum length.
 */
export const GxFkAddonSimpleNumInput = forwardRef<
    HTMLInputElement,
    SimpleNumberInputProps<
        Omit<AddonInputProps, 'onChange'> & CustomProps<HTMLInputElement>
    >
>(({ maximumDigits, ...props }, ref) => {
    const { fieldProps, fieldMeta } = useFormikSimpleNumField(
        props.name,
        maximumDigits
    );

    useFormikValidateFieldOnChangeIfError(props.name);

    return (
        <AddonInput
            ref={ref}
            error={fieldMeta.error}
            {...fieldProps}
            {...props}
            onChange={onChange(fieldProps.onChange, props.onChange)}
        />
    );
});

export const GxFkAddonNumberInput: FC<
    FormattedNumberInputProps<
        Omit<AddonInputProps, 'onChange'> & CustomProps<HTMLInputElement>
    >
> = ({ maximumFractionDigits = undefined, ...props }) => {
    const { fieldProps, fieldMeta } = useFormikFormattedNumField(
        props.name,
        maximumFractionDigits
    );

    useFormikValidateFieldOnChangeIfError(props.name);

    return (
        <AddonInput
            error={fieldMeta.error}
            {...fieldProps}
            {...props}
            onChange={onChange(fieldProps.onChange, props.onChange)}
        />
    );
};

export const GxFkAddonInput = forwardRef<
    HTMLInputElement,
    Omit<AddonInputProps, 'onChange'> & CustomProps<HTMLInputElement>
>((props, ref) => {
    const [fieldProps, fieldMeta] = useField(props.name);

    useFormikValidateFieldOnChangeIfError(props.name);

    return (
        <AddonInput
            ref={ref}
            error={fieldMeta.error}
            {...fieldProps}
            {...props}
            onChange={onChange(fieldProps.onChange, props.onChange)}
        />
    );
});

export const GxFkTextarea: FC<
    Omit<TextareaProps, 'onChange'> & CustomProps<HTMLTextAreaElement>
> = (props) => {
    const [fieldProps, fieldMeta] = useField(props.name);

    useFormikValidateFieldOnChangeIfError(props.name);

    return (
        <Textarea
            error={fieldMeta.error}
            {...fieldProps}
            {...props}
            onChange={onChange(fieldProps.onChange, props.onChange)}
        />
    );
};

export const GxFkCheckbox: FC<
    Omit<CheckboxProps, 'onChange'> & CustomProps<HTMLInputElement>
> = ({ name, value, ...props }) => {
    const [fieldProps, fieldMeta] = useField({ type: 'checkbox', name, value });

    useFormikValidateFieldOnChangeIfError(name);

    return (
        <Checkbox
            error={fieldMeta.error}
            {...fieldProps}
            {...props}
            onChange={onChange(fieldProps.onChange, props.onChange)}
        />
    );
};

export const GxFkButtonInput: FC<
    Omit<ButtonInputProps, 'onChange'> & CustomProps<HTMLInputElement>
> = (props) => {
    const [fieldProps, fieldMeta] = useField(props.name);

    useFormikValidateFieldOnChangeIfError(props.name);

    return (
        <ButtonInput
            error={fieldMeta.error}
            {...fieldProps}
            {...props}
            onChange={onChange(fieldProps.onChange, props.onChange)}
        />
    );
};

export { GxFkRangeInput };
