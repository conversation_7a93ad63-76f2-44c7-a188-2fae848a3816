import { InputProps } from '@gx-design/input';
import { trans } from '@pepita-i18n/babelfish';
import { GxFkInput } from '.';

type GxFkRangeInputProps = InputProps & {
    /** Placeholder for the left-side input, defaults to: `${trans('label.from')}:` */
    minPlaceholder?: string;
    /** Placeholder for the right-side input, defaults to: `${trans('label.to')}:` */
    maxPlaceholder?: string;
    /** Suffix for the left-side input, to be used into it's formik's name, defaults to `Min`. Example: `priceMin` */
    minSuffix?: string;
    /** Suffix for the right-side input, to be used into it's formik's name, defaults to `Max`. Example: `priceMax` */
    maxSuffix?: string;
    /** Classname for the div containing the whole component. */
    containerClassName?: string;
    /** Classname for the div containing the two inputs; defaults to `filter-box__range` */
    wrapperClassName?: string;
};
/**
 * Convenience wrapper for two formik inputs, to be used to define a numeric range.
 * Numeric digits only are allowed.
 */
export const GxFkRangeInput: React.FC<GxFkRangeInputProps> = ({
    label,
    name,
    minPlaceholder = `${trans('label.from')}:`,
    maxPlaceholder = `${trans('label.to')}:`,
    minSuffix = 'Min',
    maxSuffix = 'Max',
    containerClassName,
    wrapperClassName = 'filter-box__range',
    ...props
}: GxFkRangeInputProps) => {
    const handleKeyDown = (evt: React.KeyboardEvent<HTMLInputElement>) => {
        let charKey = evt.key;
        let allowed = '0123456789';

        if (
            allowed.indexOf(charKey) !== -1 ||
            charKey === 'Backspace' || // backspace
            charKey === 'Tab' || // tab
            charKey === 'ArrowLeft' || // left arrow
            charKey === 'ArrowRight' || // right arrow
            charKey === 'Delete' // delete
        ) {
            return true;
        } else {
            return evt.preventDefault();
        }
    };

    return (
        <div className={containerClassName}>
            <label className="gx-label">{label}</label>
            <div className={wrapperClassName}>
                <GxFkInput
                    {...props}
                    isLabelVisible={false}
                    label={`${label} ${minPlaceholder}`}
                    id={`${name}${minSuffix}`}
                    name={`${name}${minSuffix}`}
                    placeholder={minPlaceholder}
                    onKeyDown={handleKeyDown}
                />
                <GxFkInput
                    {...props}
                    isLabelVisible={false}
                    label={`${label} ${maxPlaceholder}`}
                    id={`${name}${maxSuffix}`}
                    name={`${name}${maxSuffix}`}
                    placeholder={maxPlaceholder}
                    onKeyDown={handleKeyDown}
                />
            </div>
        </div>
    );
};
