import React from 'react';
import { GtxReactPage } from './GtxApp';
import { createOuterWrapper } from './createOuterWrapper';
import { Route } from 'react-router-dom';
import { GtxAppContextProvider } from './GtxAppContextProvider';

/**
 * Creates the routes for the application
 * it will render portals for each page
 * and will use the outer wrapper to provide the necessary context and providers for the page
 */
export const createRoutes = (
    pages: GtxReactPage[],
    pagesPortals: any[],
    setInitialData: (data: unknown) => void,
    initialData: unknown,
    qsPayload: Record<string, string>
) => {
    return pages.map((pageItm) => {
        const Wrapper = pageItm.wrapper || React.Fragment;
        const OuterWrapper = createOuterWrapper(pageItm, setInitialData);

        return (
            <Route
                key={pageItm.path}
                path={pageItm.path}
                element={
                    <OuterWrapper>
                        {[
                            pagesPortals.find(
                                (portalItm) => pageItm.path === portalItm.path
                            ),
                        ].map((item) => {
                            const PortalComponent = item?.portal;
                            const actions = pageItm.header
                                ? pageItm.header?.actions
                                : null;

                            return (
                                <GtxAppContextProvider
                                    initialData={initialData}
                                    key={`portal-content-${pageItm.path}`}
                                    goBack={
                                        item?.goBack ? item.goBack : () => {}
                                    }
                                >
                                    <Wrapper>
                                        {PortalComponent && (
                                            <PortalComponent
                                                queryString={qsPayload}
                                                headerActions={
                                                    actions && actions
                                                }
                                            />
                                        )}
                                    </Wrapper>
                                </GtxAppContextProvider>
                            );
                        })}
                    </OuterWrapper>
                }
            />
        );
    });
};
