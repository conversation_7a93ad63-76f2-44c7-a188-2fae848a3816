import { type ComponentType, createElement } from 'react';

const api = ['internal', 'fapi'] as const;

type Api = (typeof api)[number];

/**
 * Switch component that renders a component based on the value of the `value` prop.
 * Use this component when you need to render a version of a component based on the old (from mls) or new api (fapi).
 */
export default function ApiSwitch(
    props: {
        value: Api;
    } & { [key in Api]: ComponentType }
) {
    if (!props[props.value]) {
        throw new Error(
            `Component for api ${
                props.value
            } not found: available apis are ${api.join(', ')}`
        );
    }

    return createElement(props[props.value]);
}
