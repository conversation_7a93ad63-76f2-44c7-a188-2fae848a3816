import React, { Fragment, useCallback } from 'react';
import { Modal } from '@gx-design/modal';
import { trans } from '@pepita-i18n/babelfish';
import { Loader } from '../../components';
import { renderImageEditor } from 'imgeditor';

const styles = {
    modalImageEditor: 'modal-image-editor',
    editorContainer: 'modal-image-editor__container',
};
/**
 * @typedef {Object} ImageEditorProps
 * @property {string} [image]
 * @property {boolean} [loading]
 * @property {'data'|'raw'} [mode]
 * @property {string} [title]
 * @property {function} [onSave]
 * @property {function} [onClose]
 * @property {string} [ratio]
 * @property {string} [suggestedAspectRatio]
 */

/**
 * @param {ImageEditorProps} props
 */
export const ImageEditor = ({
    image,
    loading = false,
    mode = 'data',
    onSave,
    onClose,
    suggestedAspectRatio,
    ratio,
    title = trans('label.edit_image'),
}) => {
    let closeEditor = () => {};

    const handleClose = () => {
        closeEditor();
        onClose && onClose();
    };

    const handleSave = ({ data }) => {
        onSave && onSave(data);
        closeEditor();
    };

    const editorRef = useCallback((node) => {
        if (node !== null) {
            closeEditor = renderImageEditor(node, {
                suggestedaspectratio: suggestedAspectRatio,
                ratio: ratio,
                onSave: handleSave,
                imgurl: image,
                mode: mode,
                translations: {
                    save: trans('label.save'),
                    restore: trans('label.restore'),
                    rotate: trans('label.rotate'),
                    adapt: trans('label.adapt'),
                    drag: trans('label.drag_to_reposition'),
                },
            });
        }
    }, []);

    return (
        <Modal
            size="large"
            isOpen={true}
            title={title}
            className={styles.modalImageEditor}
            bodyHeight="minHeight"
            onClose={handleClose}
        >
            <Fragment>
                <div className={styles.editorContainer} ref={editorRef} />
                <Loader loading={loading} fixedOverlay={false} centered={false} />
            </Fragment>
        </Modal>
    );
};
