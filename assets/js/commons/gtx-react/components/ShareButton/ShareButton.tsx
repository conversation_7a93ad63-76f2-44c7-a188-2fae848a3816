import { ActionList, ActionListItem } from '@gx-design/action-list';
import { Dropdown, DropdownProps } from '@gx-design/dropdown';
import { Icon } from '@gx-design/icon';
import { Tooltip } from '@gx-design/tooltip';
import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { IconName } from '@pepita-fe/sprite-b2b/sprite';
import { copyToClipboard } from 'gtx-react/utils/copyToClipboard';

type ShareButtonProps = {
    link: string;
    onDropdownButtonClick?: () => void;
    onShareItemClick?: (sharingChannel: string) => void;
    buttonClassName?: string;
    buttonVariant?: DropdownProps['buttonVariant'];
};
const SHARING_CHANNEL = {
    CopyURL: 'Copy URL',
    WhatsApp: 'WhatsApp',
    Messenger: 'Messenger',
    Facebook: 'Facebook',
    X: 'X',
    Linkedin: 'LinkedIn',
} as const;

type ShareAction = {
    icon: IconName;
    url: (link: string) => string;
    sharingChannel: string;
    width: number;
};

function addUtmParams(link: string): string {
    try {
        const url = new URL(link);
        url.searchParams.set('utm_source', 'crm');
        url.searchParams.set('utm_campaign', 'share_link');
        return url.toString();
    } catch {
        // Not a valid URL, return as is
        return link;
    }
}

const openPopup = (url: string, width: number) => {
    // Calculate center position for the popup
    const height = 600;
    const left = window.screenX + (window.outerWidth - width) / 2;
    const top = window.screenY + (window.outerHeight - height) / 2;

    window.open(
        url,
        '_blank',
        `noopener,noreferrer,width=${width},height=${height},menubar=no,toolbar=no,location=yes,status=no,resizable=yes,scrollbars=yes,top=${top},left=${left}`
    );
};

const offSiteShareActions: ShareAction[] = [
    {
        icon: 'brand-whatsapp',
        url: (link: string) => `https://api.whatsapp.com/send?text=${link}`,
        sharingChannel: SHARING_CHANNEL.WhatsApp,
        width: 480,
    },
    {
        icon: 'brand-messenger',
        url: (link: string) =>
            `https://www.facebook.com/dialog/send?link=${link}&app_id=199783830036103&redirect_uri=${encodeURIComponent(
                window.location.href
            )}`,
        sharingChannel: SHARING_CHANNEL.Messenger,
        width: 1200,
    },
    {
        icon: 'brand-facebook',
        url: (link: string) =>
            `https://www.facebook.com/sharer/sharer.php?u=${link}`,
        sharingChannel: SHARING_CHANNEL.Facebook,
        width: 1024,
    },
    {
        icon: 'brand-x',
        url: (link: string) => `https://x.com/intent/post?url=${link}`,
        sharingChannel: SHARING_CHANNEL.X,
        width: 480,
    },
    {
        icon: 'brand-linkedin',
        url: (link: string) =>
            `https://www.linkedin.com/feed/?shareActive=true&text=${link}`,
        sharingChannel: SHARING_CHANNEL.Linkedin,
        width: 1024,
    },
];

export const ShareButton = ({
    link,
    onDropdownButtonClick,
    onShareItemClick,
    buttonClassName,
    buttonVariant,
}: ShareButtonProps) => {
    const { showNotification } = useNotifyContext();

    const linkWithUtm = addUtmParams(link);
    const encodedLinkWithUtm = encodeURIComponent(linkWithUtm);

    const copyToClipBoardHandler = (ev: React.MouseEvent) => {
        ev.stopPropagation();
        copyToClipboard(linkWithUtm);
        showNotification({
            type: 'success',
            message: trans('label.copy_link_success'),
        });
        if (onShareItemClick) {
            onShareItemClick(SHARING_CHANNEL.CopyURL);
        }
    };

    const offSiteActionClickHandler = (action: ShareAction) => {
        return (ev: React.MouseEvent) => {
            ev.stopPropagation();
            openPopup(action.url(encodedLinkWithUtm), action.width);
            if (onShareItemClick) {
                onShareItemClick(action.sharingChannel);
            }
        };
    };

    const offSiteShareActionsComponents = offSiteShareActions.map((action) => (
        <ActionListItem
            key={action.sharingChannel}
            startElement={<Icon name={action.icon} />}
            text={action.sharingChannel}
            onClick={offSiteActionClickHandler(action)}
        />
    ));

    const shareActionsComponents = [
        <ActionListItem
            key={SHARING_CHANNEL.CopyURL}
            onClick={copyToClipBoardHandler}
            startElement={<Icon name="copy" />}
            text={trans('label.copy_link')}
        />,
    ].concat(offSiteShareActionsComponents);

    return (
        <div onClick={() => onDropdownButtonClick && onDropdownButtonClick()}>
            <Dropdown
                buttonVariant={buttonVariant}
                buttonContent={
                    <Tooltip
                        key={`cell_quick_action_share_property`}
                        position="top"
                        text={trans('label.share_ad')}
                    >
                        <Icon name="share-ios" />
                    </Tooltip>
                }
                buttonIsIconOnly
                position="bottomRight"
                buttonClassName={buttonClassName}
            >
                <ActionList>{shareActionsComponents}</ActionList>
            </Dropdown>
        </div>
    );
};
