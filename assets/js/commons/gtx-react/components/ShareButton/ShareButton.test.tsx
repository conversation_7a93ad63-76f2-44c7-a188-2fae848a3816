import {
    render,
    screen,
    fireEvent,
} from '#tests/react/testing-library-enhanced';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ShareButton } from './ShareButton';
import { copyToClipboard } from 'gtx-react/utils/copyToClipboard';
import { NotifyProvider } from '@gx-design/snackbar';

Object.defineProperty(window, 'open', {
    value: vi.fn(),
    writable: true,
});

// Mock window.location.href for tests
Object.defineProperty(window, 'location', {
    value: {
        href: 'https://test.domain.com/current-page',
    },
    writable: true,
});

// Mock window properties for popup positioning
Object.defineProperty(window, 'screenX', {
    value: 100,
    writable: true,
});

Object.defineProperty(window, 'screenY', {
    value: 100,
    writable: true,
});

Object.defineProperty(window, 'outerWidth', {
    value: 1920,
    writable: true,
});

Object.defineProperty(window, 'outerHeight', {
    value: 1080,
    writable: true,
});

vi.mock('gtx-react/utils/copyToClipboard', () => ({
    copyToClipboard: vi.fn().mockImplementation(() => Promise.resolve()),
}));

describe('ShareButton', () => {
    const link = 'https://example.com';

    // Mock values for window properties
    const mockWindow = {
        screenX: 100,
        screenY: 100,
        outerWidth: 1920,
        outerHeight: 1080,
    };

    // Calculate popup positioning dynamically like the component does
    const calculatePopupPosition = (width: number) => {
        const height = 600; // Fixed height from component
        const left = mockWindow.screenX + (mockWindow.outerWidth - width) / 2;
        const top = mockWindow.screenY + (mockWindow.outerHeight - height) / 2;
        return { top, left };
    };

    const renderShareButton = () => {
        return render(<ShareButton link={link} />, {
            wrapper: ({ children }) => (
                <NotifyProvider>{children}</NotifyProvider>
            ),
        });
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    const openDropDown = () => {
        const dropDownButton = screen.getByRole('button');
        expect(dropDownButton).toBeInTheDocument();
        fireEvent.click(dropDownButton);
    };

    it('renders all share actions', () => {
        renderShareButton();

        openDropDown();

        expect(screen.getByText('label.copy_link')).toBeInTheDocument();
        expect(screen.getByText('WhatsApp')).toBeInTheDocument();
        expect(screen.getByText('Messenger')).toBeInTheDocument();
        expect(screen.getByText('Facebook')).toBeInTheDocument();
        expect(screen.getByText('X')).toBeInTheDocument();
        expect(screen.getByText('LinkedIn')).toBeInTheDocument();
    });

    it('copies link to clipboard when Copia link is clicked', async () => {
        renderShareButton();
        openDropDown();
        const copyButton = screen.getByText('label.copy_link');
        copyButton.click();

        const linkWithUtm =
            'https://example.com/?utm_source=crm&utm_campaign=share_link';
        expect(copyToClipboard).toHaveBeenCalledWith(linkWithUtm);
    });

    it('opens WhatsApp share url', () => {
        renderShareButton();
        openDropDown();
        fireEvent.click(screen.getByText('WhatsApp'));

        const linkWithUtm =
            'https://example.com/?utm_source=crm&utm_campaign=share_link';
        const { top, left } = calculatePopupPosition(480); // WhatsApp width

        expect(window.open).toHaveBeenCalledWith(
            `https://api.whatsapp.com/send?text=${encodeURIComponent(
                linkWithUtm
            )}`,
            '_blank',
            `noopener,noreferrer,width=480,height=600,menubar=no,toolbar=no,location=yes,status=no,resizable=yes,scrollbars=yes,top=${top},left=${left}`
        );
    });

    it('opens Messenger share url', () => {
        renderShareButton();
        openDropDown();
        fireEvent.click(screen.getByText('Messenger'));

        const linkWithUtm =
            'https://example.com/?utm_source=crm&utm_campaign=share_link';
        const { top, left } = calculatePopupPosition(1200); // Messenger width

        expect(window.open).toHaveBeenCalledWith(
            `https://www.facebook.com/dialog/send?link=${encodeURIComponent(
                linkWithUtm
            )}&app_id=199783830036103&redirect_uri=${encodeURIComponent(
                'https://test.domain.com/current-page'
            )}`,
            '_blank',
            `noopener,noreferrer,width=1200,height=600,menubar=no,toolbar=no,location=yes,status=no,resizable=yes,scrollbars=yes,top=${top},left=${left}`
        );
    });

    it('opens Facebook share url', () => {
        renderShareButton();
        openDropDown();
        fireEvent.click(screen.getByText('Facebook'));

        const linkWithUtm =
            'https://example.com/?utm_source=crm&utm_campaign=share_link';
        const { top, left } = calculatePopupPosition(1024); // Facebook width

        expect(window.open).toHaveBeenCalledWith(
            `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                linkWithUtm
            )}`,
            '_blank',
            `noopener,noreferrer,width=1024,height=600,menubar=no,toolbar=no,location=yes,status=no,resizable=yes,scrollbars=yes,top=${top},left=${left}`
        );
    });

    it('opens X share url', () => {
        renderShareButton();
        openDropDown();
        fireEvent.click(screen.getByText('X'));

        const linkWithUtm =
            'https://example.com/?utm_source=crm&utm_campaign=share_link';
        const { top, left } = calculatePopupPosition(480); // X width

        expect(window.open).toHaveBeenCalledWith(
            `https://x.com/intent/post?url=${encodeURIComponent(linkWithUtm)}`,
            '_blank',
            `noopener,noreferrer,width=480,height=600,menubar=no,toolbar=no,location=yes,status=no,resizable=yes,scrollbars=yes,top=${top},left=${left}`
        );
    });

    it('opens LinkedIn share url', () => {
        renderShareButton();
        openDropDown();
        fireEvent.click(screen.getByText('LinkedIn'));

        const linkWithUtm =
            'https://example.com/?utm_source=crm&utm_campaign=share_link';
        const { top, left } = calculatePopupPosition(1024); // LinkedIn width

        expect(window.open).toHaveBeenCalledWith(
            `https://www.linkedin.com/feed/?shareActive=true&text=${encodeURIComponent(
                linkWithUtm
            )}`,
            '_blank',
            `noopener,noreferrer,width=1024,height=600,menubar=no,toolbar=no,location=yes,status=no,resizable=yes,scrollbars=yes,top=${top},left=${left}`
        );
    });

    it('adds UTM parameters to links', () => {
        const testLink = 'https://example.com/property/123';
        render(<ShareButton link={testLink} />, {
            wrapper: ({ children }) => (
                <NotifyProvider>{children}</NotifyProvider>
            ),
        });
        openDropDown();

        const copyButton = screen.getByText('label.copy_link');
        copyButton.click();

        const expectedLinkWithUtm =
            'https://example.com/property/123?utm_source=crm&utm_campaign=share_link';
        expect(copyToClipboard).toHaveBeenCalledWith(expectedLinkWithUtm);
    });

    it('preserves existing query parameters when adding UTM params', () => {
        const testLink = 'https://example.com/property/123?existing=param';
        render(<ShareButton link={testLink} />, {
            wrapper: ({ children }) => (
                <NotifyProvider>{children}</NotifyProvider>
            ),
        });
        openDropDown();

        const copyButton = screen.getByText('label.copy_link');
        copyButton.click();

        const expectedLinkWithUtm =
            'https://example.com/property/123?existing=param&utm_source=crm&utm_campaign=share_link';
        expect(copyToClipboard).toHaveBeenCalledWith(expectedLinkWithUtm);
    });

    it('calculates popup position correctly', () => {
        // Test the calculation logic used in the component
        const testCases = [
            { width: 480, expectedLeft: 820, expectedTop: 340 }, // WhatsApp/X
            { width: 1024, expectedLeft: 548, expectedTop: 340 }, // Facebook/LinkedIn
            { width: 1200, expectedLeft: 460, expectedTop: 340 }, // Messenger
        ];

        testCases.forEach(({ width, expectedLeft, expectedTop }) => {
            const { top, left } = calculatePopupPosition(width);
            expect(left).toBe(expectedLeft);
            expect(top).toBe(expectedTop);
        });
    });

    it('shows success notification after copying link', async () => {
        renderShareButton();
        openDropDown();

        const copyButton = screen.getByText('label.copy_link');
        fireEvent.click(copyButton);

        // Wait for the notification to appear in the DOM
        const notification = await screen.findByText('label.copy_link_success');
        expect(notification).toBeInTheDocument();
        expect(notification).toBeVisible();
    });

    it('closes dropdown after clicking actions', () => {
        const expectDropdownIsOpen = () => {
            expect(screen.getByText('label.copy_link')).toBeInTheDocument();
            expect(screen.getByText('WhatsApp')).toBeInTheDocument();
            expect(screen.getByText('Messenger')).toBeInTheDocument();
            expect(screen.getByText('Facebook')).toBeInTheDocument();
            expect(screen.getByText('X')).toBeInTheDocument();
            expect(screen.getByText('LinkedIn')).toBeInTheDocument();
        };
        const expectDropdownIsClosed = () => {
            expect(screen.queryByText('WhatsApp')).not.toBeInTheDocument();
            expect(screen.queryByText('Facebook')).not.toBeInTheDocument();
            expect(screen.queryByText('Messenger')).not.toBeInTheDocument();
            expect(screen.queryByText('X')).not.toBeInTheDocument();
            expect(screen.queryByText('LinkedIn')).not.toBeInTheDocument();
            expect(
                screen.queryByText('label.copy_link')
            ).not.toBeInTheDocument();
        };

        renderShareButton();

        const testDropdownItemClick = (actionName: string) => {
            openDropDown();
            // Verify dropdown is open
            expectDropdownIsOpen();
            // Click on Action
            fireEvent.click(screen.getByText(actionName));
            // Verify dropdown is closed
            expectDropdownIsClosed();
        };

        testDropdownItemClick('WhatsApp');
        testDropdownItemClick('Messenger');
        testDropdownItemClick('Facebook');
        testDropdownItemClick('X');
        testDropdownItemClick('LinkedIn');
        testDropdownItemClick('label.copy_link');
    });
});
