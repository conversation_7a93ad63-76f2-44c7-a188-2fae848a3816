import { trans } from '@pepita-i18n/babelfish';
import { PropsWithChildren, useMemo } from 'react';
import { withErrorBoundary } from 'react-error-boundary';
import { Skeleton } from './Skeleton';

function CrmSummaryResultsFallback() {
    return (
        <div className="crm-table-summary__results">
            0 {trans('label.results')}
        </div>
    );
}

function CrmSummaryResults(props: {
    pageIndex: number;
    pageSize: number;
    itemsLength: number;
    pageCount: number;
}) {
    const { start, end, singleResult } = useMemo(() => {
        const start = props.pageIndex * props.pageSize + 1;
        const end = Math.min(start + props.itemsLength - 1, props.pageCount);
        return {
            start,
            end,
            singleResult: start === end,
        };
    }, [props.pageIndex, props.pageSize, props.itemsLength, props.pageCount]);

    if (props.pageCount === 0) {
        return <CrmSummaryResultsFallback />;
    }

    return (
        <div className="crm-table-summary__results">
            {trans(`label.displayed${singleResult ? '' : '_plural'}`)}{' '}
            {singleResult ? start : `${start} - ${end}`}{' '}
            {trans('label.out_of_2')} {props.pageCount}
        </div>
    );
}

function CrmSummaryResultsSkeleton() {
    return (
        <div className="crm-table-summary__results">
            <Skeleton width={100} />
        </div>
    );
}

function CrmSummaryActions(props: PropsWithChildren) {
    return <div className="crm-table-summary__actions">{props.children}</div>;
}

export function CrmSummary(props: PropsWithChildren) {
    return <div className="crm-table-summary">{props.children}</div>;
}

CrmSummary.Results = withErrorBoundary(CrmSummaryResults, {
    FallbackComponent: CrmSummaryResultsFallback,
});

CrmSummary.Actions = CrmSummaryActions;
CrmSummary.Skeleton = CrmSummaryResultsSkeleton;
