import type { ColumnOrderState } from '@tanstack/react-table';

/**
 * Get default column order based on persistent columns and actual columns
 * @param givenColumns The columns coming from saved state object
 * @param actualColumns The actual columns that are available
 * @example const defaultColumnOrder = getDefaultColumnOrder(persistentColumns, actualColumns);
 * @returns
 */
export const getDefaultColumnOrder = (givenColumns: ColumnOrderState, actualColumns: ColumnOrderState) => {
    // If keys are not the same, return the actual columns
    if (givenColumns.length !== actualColumns.length) {
        return actualColumns;
    }

    // If keys in persistent columns are not in actual columns, remove them
    const filteredColumns = givenColumns.filter((column) => actualColumns.includes(column));

    // If actual columns are not in persistent columns, add them
    const missingColumns = actualColumns.filter((column) => !filteredColumns.includes(column));

    return [...filteredColumns, ...missingColumns];
};
