import { describe, it, expect } from 'vitest';
import { draggableUpdater } from './helpers';

describe('draggableUpdater', () => {
    it('should move an item from one position to another in the array', () => {
        const columnOrder = ['col1', 'col2', 'col3', 'col4', 'col5'];

        // Move col2 to position of col4
        const updater = draggableUpdater('col2', 'col4');
        const result = updater(columnOrder);

        expect(result).toEqual(['col1', 'col3', 'col4', 'col2', 'col5']);
    });

    it('should move an item to the beginning of the array', () => {
        const columnOrder = ['col1', 'col2', 'col3', 'col4', 'col5'];

        // Move col4 to position of col1
        const updater = draggableUpdater('col4', 'col1');
        const result = updater(columnOrder);

        expect(result).toEqual(['col4', 'col1', 'col2', 'col3', 'col5']);
    });

    it('should move an item to the end of the array', () => {
        const columnOrder = ['col1', 'col2', 'col3', 'col4', 'col5'];

        // Move col2 to position of col5
        const updater = draggableUpdater('col2', 'col5');
        const result = updater(columnOrder);

        expect(result).toEqual(['col1', 'col3', 'col4', 'col5', 'col2']);
    });

    it('should move an item one position forward', () => {
        const columnOrder = ['col1', 'col2', 'col3', 'col4', 'col5'];

        // Move col2 to position of col3
        const updater = draggableUpdater('col2', 'col3');
        const result = updater(columnOrder);

        expect(result).toEqual(['col1', 'col3', 'col2', 'col4', 'col5']);
    });

    it('should move an item one position backward', () => {
        const columnOrder = ['col1', 'col2', 'col3', 'col4', 'col5'];

        // Move col3 to position of col2
        const updater = draggableUpdater('col3', 'col2');
        const result = updater(columnOrder);

        expect(result).toEqual(['col1', 'col3', 'col2', 'col4', 'col5']);
    });

    it('should return original array if ids are the same', () => {
        const columnOrder = ['col1', 'col2', 'col3', 'col4', 'col5'];

        const updater = draggableUpdater('col2', 'col2');
        const result = updater(columnOrder);

        expect(result).toEqual(['col1', 'col2', 'col3', 'col4', 'col5']);
    });

    it('should not handle empty arrays', () => {
        const columnOrder: string[] = [];

        const updater = draggableUpdater('col1', 'col2');
        const result = updater(columnOrder);

        // happens because the array is empty and the ids are not in the array.
        // This is a case that should not happen in the app, but we need to know this behavior.
        expect(result).toEqual([undefined]);
    });
});
