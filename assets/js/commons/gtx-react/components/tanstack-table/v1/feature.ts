import {
    functionalUpdate,
    makeStateUpdater,
    OnChangeFn,
    RowData,
    Table,
    TableFeature,
    Updater,
} from '@tanstack/react-table';

export type DragAndDropState = Record<string, boolean>;
export interface DragAndDropTableState {
    dnd: DragAndDropState;
}

// define types for our new feature's table options
export interface DragAndDropOptions {
    /**
     * Enable drag and drop functionality
     * @default false
     */
    enableDnd?: boolean;
    onDndChange?: OnChangeFn<DragAndDropState>;
}

// Define types for our new feature's table APIs
export interface DragAndDropInstance {
    setDnd: (updater: Updater<DragAndDropState>) => void;
    /**
     *
     * @param id - The id of the header
     * @returns The drag and drop state of the header
     */
    getDnd: (id: string) => boolean;
}

export interface DragAndDropHeader {
    getDnd: () => boolean;
    setDnd: (updater: Updater<boolean>) => void;
}

// Feature function

export const DragAndDropFeature: TableFeature<any> = {
    // Use the TableFeature type!!
    // define the new feature's initial state
    getInitialState: (state): DragAndDropTableState => {
        return {
            dnd: {},
            ...state,
        };
    },

    // define the new feature's default options
    getDefaultOptions: <TData extends RowData>(table: Table<TData>): DragAndDropOptions => {
        return {
            enableDnd: true,
            onDndChange: makeStateUpdater('dnd', table),
        } satisfies DragAndDropOptions;
    },
    // if you need to add a default column definition...
    // getDefaultColumnDef: <TData extends RowData>(): Partial<ColumnDef<TData>> => {
    //   return { meta: {} } //use meta instead of directly adding to the columnDef to avoid typescript stuff that's hard to workaround
    // },

    // define the new feature's table instance methods
    createTable: <TData extends RowData>(table: Table<TData>): void => {
        table.setDnd = (updater) => {
            const safeUpdater: Updater<DragAndDropState> = (old) => {
                let newState = functionalUpdate(updater, old);
                return newState;
            };
            return table.options.onDndChange?.(safeUpdater);
        };

        table.getDnd = (id) => {
            // if dnd is disabled globally, return false
            if (table.options.enableDnd === false) {
                return false;
            }

            return table.getState().dnd[id] ?? true;
        };
    },

    createHeader: <TData extends RowData>(header, table: Table<TData>): void => {
        header.getDnd = () => {
            // if dnd is disabled globally, return false
            if (table.options.enableDnd === false) {
                return false;
            }

            return table.getState().dnd[header.id] ?? true;
        };
        header.setDnd = (updater) => {
            const safeUpdater: Updater<boolean> = (old) => {
                let newState = functionalUpdate(updater, old);
                return newState;
            };

            return table.options.onDndChange?.((old) => ({
                ...old,
                [header.id]: safeUpdater(Boolean(old[header.id])),
            }));
        };
    },

    // if you need to add row instance APIs...
    // createRow: <TData extends RowData>(row, table): void => {},
    // if you need to add cell instance APIs...
    // createCell: <TData extends RowData>(cell, column, row, table): void => {},
    // if you need to add column instance APIs...
    // createColumn: <TData extends RowData>(column, table): void => {},
    // if you need to add header instance APIs...
    // createHeader: <TData extends RowData>(header, table): void => {},
};
