import { arrayMove } from '@dnd-kit/sortable';

/**
 * Reorders the columns in the given column order array by moving the column
 * identified by `activeId` to the position of the column identified by `overId`.
 *
 * @param activeId - The ID of the column to be moved.
 * @param overId - The ID of the column to move the active column to.
 * @returns A function that takes the current column order array and returns the new column order array.
 */
export const draggableUpdater = (activeId: string, overId: string) => (columnOrder: string[]) => {
    const oldIndex = columnOrder.indexOf(activeId);
    const newIndex = columnOrder.indexOf(overId);
    return arrayMove(columnOrder, oldIndex, newIndex); //this is just a splice util
};
