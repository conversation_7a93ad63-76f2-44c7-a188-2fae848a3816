import React, { ReactNode } from 'react';
import {
    closestCenter,
    DndContext,
    DragEndEvent,
    DraggableAttributes,
    KeyboardSensor,
    MouseSensor,
    TouchSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import {
    restrictToHorizontalAxis,
    restrictToParentElement,
} from '@dnd-kit/modifiers';
import {
    horizontalListSortingStrategy,
    SortableContext,
} from '@dnd-kit/sortable';
import { Checkbox } from '@gx-design/checkbox';
import { Icon } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import {
    Cell,
    Column,
    flexRender,
    Header,
    Row,
    Table,
} from '@tanstack/react-table';
import clsx from 'clsx';
import {
    ComponentProps,
    createContext,
    PropsWithChildren,
    useContext,
    useMemo,
    useRef,
    useState,
} from 'react';
import {
    ResizeColumnHoverProvider,
    TableContext,
    TableHeaderContext,
    useResizeColumnHoverContext,
    useTableContext,
    useTableHeaderContext,
} from '../contexts';
import { draggableUpdater } from './helpers';
import { useColumnDragging, useCommonPinningStyles } from './hooks';
import { DropdownProvider } from '@gx-design/dropdown';
import { ContentDropdownProvider } from 'gtx-react/components/ContentDropdown/ContentDropdownContext';

// TODO: coordinate with the design team to get the correct styles for this
export const SortableHeaderButton = (
    props: PropsWithChildren<{
        onClick?: () => void;
    }>
) => {
    const header = useTableHeaderContext();
    const isSorted = header.column.getIsSorted();

    if (!header.column.getCanSort()) {
        return <>{props.children}</>;
    }

    return (
        <span
            onClick={(ev) => {
                header.column.toggleSorting();
                if (props.onClick) {
                    props.onClick();
                }
            }}
            className={clsx('sortable', {
                asc: isSorted === 'asc',
                desc: isSorted === 'desc',
            })}
        >
            <span className="gx-text-ellipsis">
                {flexRender(
                    header.column.columnDef.header,
                    header.getContext()
                )}
            </span>
            <Icon name="order" />
        </span>
    );
    // return header.column.getCanSort() ? (
    //     <button
    //         // className="gx-sr-only"
    //         onClick={() => header.column.toggleSorting()}
    //     >
    //         Sort:
    //         {!header.column.getIsSorted() && '＝'}
    //         {header.column.getIsSorted() === 'desc' && '🔽'}
    //         {header.column.getIsSorted() === 'asc' && '🔼'}
    //     </button>
    // ) : null;
};

// TODO: coordinate with the design team to get the correct styles for this
/**
 * This feature is not yet required, but it is a placeholder for future use.
 */
export const PinningControls = () => {
    const header = useTableHeaderContext();

    {
        return (
            !header.isPlaceholder &&
            header.column.getCanPin() && (
                <div>
                    {header.column.getIsPinned() !== 'left' ? (
                        <button
                            className="gx-sr-only"
                            onClick={(evt) => {
                                evt.stopPropagation();
                                header.column.pin('left');
                            }}
                        >
                            {'<='}
                        </button>
                    ) : null}
                    {/* {header.column.getIsPinned() ? (
                        <button
                            className="border rounded px-2"
                            onClick={(evt) => {
                                evt.stopPropagation();
                                header.column.pin(false);
                            }}
                        >
                            X
                        </button>
                    ) : null}
                    {header.column.getIsPinned() !== 'right' ? (
                        <button
                            className="border rounded px-2"
                            onClick={(evt) => {
                                evt.stopPropagation();
                                header.column.pin('right');
                            }}
                        >
                            {'=>'}
                        </button>
                    ) : null} */}
                </div>
            )
        );
    }
};

// TODO: coordinate with the design team to get the correct styles for this
export const ResizebleControls = () => {
    const table = useTableContext();
    const header = useTableHeaderContext();

    const { hoveredColumnId, setHoveredColumnId } =
        useResizeColumnHoverContext();

    if (!header.column.getCanResize()) {
        return null;
    }

    return (
        <div
            {...{
                onClick: (evt) => {
                    evt.stopPropagation();
                },
                onDoubleClick: () => header.column.resetSize(),
                onMouseDown: header.getResizeHandler(),
                onTouchStart: header.getResizeHandler(),
                onMouseEnter: () => setHoveredColumnId(header.column.id),
                onMouseLeave: () => setHoveredColumnId(null),
                className: clsx(
                    `crm-table__resize ${table.options.columnResizeDirection}`,
                    {
                        'is-active':
                            header.column.getIsResizing() ||
                            hoveredColumnId === header.column.id,
                        'crm-table__resize--canResize':
                            header.column.getCanResize,
                    }
                ),
            }}
        />
    );
};

function TdResizebleControls({ column }: { column: Column<any, unknown> }) {
    const { hoveredColumnId, setHoveredColumnId } =
        useResizeColumnHoverContext();
    const table = useTableContext();

    const header = useMemo(() => {
        return table
            .getHeaderGroups()
            .flatMap((group) => group.headers)
            .find((h) => h.column.id === column.id);
    }, [column.id, table]);

    if (!header || !column.getCanResize()) {
        return null;
    }

    return (
        <div
            onMouseDown={header.getResizeHandler()}
            onTouchStart={header.getResizeHandler()}
            onMouseEnter={() => setHoveredColumnId(column.id)}
            onMouseLeave={() => setHoveredColumnId(null)}
            className={clsx(
                `crm-table__resize ${table.options.columnResizeDirection}`,
                {
                    'is-active':
                        header.column.getIsResizing() ||
                        hoveredColumnId === column.id,
                    'crm-table__resize--canResize':
                        header.column.getCanResize(),
                }
            )}
        />
    );
}

// TODO: coordinate with the design team to get the correct styles for this
export const DraggableButton = ({
    className,
    ...props
}: ComponentProps<'button'>) => {
    const header = useTableHeaderContext();
    const { attributes, listeners } = useDraggableTableHeaderContext();

    return (
        header.column.getIsPinned() === false && (
            <button
                {...props}
                {...attributes}
                {...listeners}
                className={clsx('crm-table__dndButton', className)}
            >
                <span className="gx-sr-only">{`Drag ${header.column.id}`}</span>
                <Icon name="dots" />
            </button>
        )
    );
};

// TODO: coordinate with the design team to get the correct styles for this
export function DraggableTableHeader<T extends object>({
    header,
    children,
    className,
    onSort = () => {},
}: PropsWithChildren<{
    header: Header<T, unknown>;
    className?: string;
    onSort?: (header: Header<T, unknown>) => void;
}>) {
    return (
        <TableHeaderContext.Provider value={header}>
            <DraggableTableHeaderElement
                draggableChildren={children}
                header={header}
                className={className}
            >
                {header.isPlaceholder ? null : (
                    <span className="crm-table__cellHeadLabel">
                        <SortableHeaderButton onClick={() => onSort(header)}>
                            {flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                            )}
                        </SortableHeaderButton>
                    </span>
                )}
            </DraggableTableHeaderElement>
        </TableHeaderContext.Provider>
    );
}

function DraggableTableHeaderElement<T extends object>({
    header,
    className,
    children,
    draggableChildren,
}: PropsWithChildren<{
    header: Header<T, unknown>;
    className?: string;
    draggableChildren?: React.ReactNode;
}>) {
    const { attributes, listeners, setNodeRef, style, isDragging } =
        useColumnDragging(header.column, !header.getDnd());

    const commonPinningStyles = useCommonPinningStyles(header.column);

    const providerValue = useMemo(
        () => ({ attributes, listeners }),
        [attributes, listeners]
    );

    return (
        <th
            className={clsx(
                'cursor-pointer',
                {
                    'crm-table__cellHead--dragging': isDragging,
                    'crm-table__cellHead--pinned': header.column.getIsPinned(),
                },
                className
            )}
            colSpan={header.colSpan}
            ref={setNodeRef}
            style={{ ...style, ...commonPinningStyles }}
            data-column-id={header.column.id}
        >
            <div className="crm-table__cellHead">
                <div className="crm-table__cellHeadEllipsis">
                    {children}
                    <DraggableTableHeaderContext.Provider value={providerValue}>
                        {draggableChildren}
                    </DraggableTableHeaderContext.Provider>
                </div>
            </div>
        </th>
    );
}

const DraggableTableHeaderContext = createContext<
    | {
          attributes: DraggableAttributes;
          listeners: SyntheticListenerMap | undefined;
      }
    | undefined
>(undefined);

function useDraggableTableHeaderContext() {
    const context = useContext(DraggableTableHeaderContext);
    if (!context) {
        throw new Error(
            'useDraggableTableHeaderContext must be used within a DraggableTableHeader'
        );
    }

    return context;
}

function DraggableTableHorizontal(
    props: PropsWithChildren<{ items: Array<string> }>
) {
    return (
        <SortableContext
            items={props.items}
            strategy={horizontalListSortingStrategy}
        >
            {props.children}
        </SortableContext>
    );
}

export function DraggableTableData<T extends object>({
    isLoading,
    skeletonElement,
    cell,
    children = null,
}: PropsWithChildren<{
    cell: Cell<T, unknown>;
    isLoading?: boolean;
    skeletonElement?: ReactNode;
}>) {
    return (
        <DraggableTableElement cell={cell}>
            <div className="crm-table__cellContent">
                {isLoading
                    ? skeletonElement
                    : flexRender(cell.column.columnDef.cell, cell.getContext())}
            </div>
            {children}
        </DraggableTableElement>
    );
}

function DraggableTableElement<T extends object>(
    props: PropsWithChildren<{
        cell: Cell<T, unknown>;
    }>
) {
    const commonPinningStyles = useCommonPinningStyles(props.cell.column);

    const [isDropdownOpen, setIsDropdownOpen] = useState(false);

    const tdRef = useRef<HTMLTableCellElement>(null);
    return (
        <>
            <ContentDropdownProvider
                value={{
                    dropdownAnchorElement: tdRef,
                    onDropdownChange: setIsDropdownOpen,
                }}
            >
                <DropdownProvider
                    onDropdownChange={setIsDropdownOpen}
                    dropdownAnchorElement={tdRef}
                >
                    <td
                        className={clsx('crm-table__cell', {
                            'crm-table__cell--pinned':
                                props.cell.column.getIsPinned(),
                            'crm-table__cell--dropdownOpen': isDropdownOpen,
                        })}
                        style={commonPinningStyles}
                        ref={tdRef}
                        data-column-id={props.cell.column.id}
                    >
                        {props.children}
                    </td>
                </DropdownProvider>
            </ContentDropdownProvider>
        </>
    );
}

//
/**
 * A wrapper component for DndContext that provides default drag-and-drop functionality
 * specifically configured for table interactions.
 *
 * This component sets up:
 * - Mouse, touch, and keyboard sensors for drag detection
 * - Closest center collision detection
 * - Horizontal axis movement restriction
 *
 * @param props - Props to pass to the underlying DndContext component. Note that
 * 'sensors', 'modifiers', and 'collisionDetection' props are configured internally
 * and cannot be overridden.
 *
 * @example
 * ```tsx
 * <DraggableTableContext onDragEnd={handleDragEnd}>
 *   <TableComponent />
 * </DraggableTableContext>
 * ```
 *
 * @returns A DndContext component with pre-configured drag-and-drop behavior for tables
 */
export function DraggableTableContext(
    props: Omit<
        ComponentProps<typeof DndContext>,
        'sensors' | 'modifiers' | 'collisionDetection'
    >
) {
    const sensors = useSensors(
        useSensor(MouseSensor, {}),
        useSensor(TouchSensor, {}),
        useSensor(KeyboardSensor, {})
    );

    return (
        <DndContext
            collisionDetection={closestCenter}
            modifiers={[restrictToHorizontalAxis, restrictToParentElement]}
            sensors={sensors}
            {...props}
        >
            {props.children}
        </DndContext>
    );
}

export function CrmTable<TData extends unknown>(
    props: PropsWithChildren<{
        table: Table<TData>;
    }>
) {
    // reorder columns after drag & drop
    function handleDragEnd(event: DragEndEvent) {
        const { active, over } = event;

        if (over?.data?.current?.onDropOverDisabled) {
            // do nothing if the over element has onDropOverDisabled
            return;
        }

        if (active && over && active.id !== over.id) {
            props.table.setColumnOrder(
                draggableUpdater(active.id as string, over.id as string)
            );
        }
    }

    return (
        <div className="crm-table-wrapper">
            <TableContext.Provider value={props.table}>
                <ResizeColumnHoverProvider>
                    <DraggableTableContext onDragEnd={handleDragEnd}>
                        <table
                            className="crm-table"
                            style={{
                                width: props.table.getTotalSize(),
                            }}
                        >
                            {props.children}
                        </table>
                    </DraggableTableContext>
                </ResizeColumnHoverProvider>
            </TableContext.Provider>
        </div>
    );
}

function TableBodyRow<T>(props: PropsWithChildren<{ row: Row<T> }>) {
    return (
        <tr
            className={clsx('crm-table__row', {
                'crm-table__row--selected': props.row.getIsSelected(),
            })}
        >
            {props.children}
        </tr>
    );
}

export function TableHead(props: PropsWithChildren<{}>) {
    return <thead className="crm-table__head">{props.children}</thead>;
}

export function TableRowSelectable(props: PropsWithChildren<{}>) {
    const table = useTableContext();

    return (
        <tr
            className={clsx('crm-table__headActions', {
                'is-visible': table.getSelectedRowModel().rows.length > 0,
            })}
        >
            <td>
                <div className="crm-table__headActionsContent">
                    <Checkbox
                        indeterminate={
                            table.getIsSomeRowsSelected() &&
                            !table.getIsAllRowsSelected()
                        }
                        checked={table.getSelectedRowModel().rows.length > 0}
                        onChange={() => table.toggleAllRowsSelected()}
                    />
                    <span className="gx-body-small">
                        {table.getSelectedRowModel().rows.length}{' '}
                        {trans(
                            table.getSelectedRowModel().rows.length > 1
                                ? 'label.selected_plural_2'
                                : 'label.selected'
                        )}
                    </span>
                    {props.children}
                </div>
            </td>
            <td colSpan={table.getAllColumns().length - 1}>
                <div className="crm-table__headActionsContent"></div>
            </td>
        </tr>
    );
}

function TableRow({ className, ...props }: ComponentProps<'tr'>) {
    return (
        <tr className={clsx('crm-table__row', className)} {...props}>
            {props.children}
        </tr>
    );
}

TableRowSelectable.Actions = TableRowActions;

function TableRowActions(props: PropsWithChildren<{}>) {
    return (
        <div className="crm-table__headActionsContent">{props.children}</div>
    );
}

function TableBody({ className, ...props }: ComponentProps<'tbody'>) {
    return (
        <tbody className={clsx('crm-table__body', className)} {...props}>
            {props.children}
        </tbody>
    );
}

DraggableTableHorizontal.displayName = 'CrmTable.HorizontalDropping';
TableBodyRow.displayName = 'CrmTable.BodyRow';
TableHead.displayName = 'CrmTable.Thead';
TableRow.displayName = 'CrmTable.Tr';
TableRowSelectable.displayName = 'CrmTable.TrSelectable';
TableBody.displayName = 'CrmTable.TableBody';
DraggableTableHeader.displayName = 'CrmTable.Th';
ResizebleControls.displayName = 'CrmTable.Th.ResizebleControls';
PinningControls.displayName = 'CrmTable.Th.PinningControls';
DraggableButton.displayName = 'CrmTable.Th.DraggableButton';
DraggableTableData.displayName = 'CrmTable.Td';

TdResizebleControls.displayName = 'CrmTable.Td.ResizebleControls';

DraggableTableHeader.ResizebleControls = ResizebleControls;
DraggableTableHeader.PinningControls = PinningControls;
DraggableTableHeader.DraggableButton = DraggableButton;

DraggableTableData.ResizebleControls = TdResizebleControls;
CrmTable.HorizontalDropping = DraggableTableHorizontal;
CrmTable.BodyRow = TableBodyRow;
CrmTable.Thead = TableHead;
CrmTable.Tr = TableRow;
CrmTable.TableBody = TableBody;
CrmTable.Th = DraggableTableHeader;
CrmTable.TrSelectable = TableRowSelectable;
CrmTable.Td = DraggableTableData;
