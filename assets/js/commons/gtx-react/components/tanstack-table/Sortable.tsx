import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { Dropdown } from '@gx-design/dropdown';
import { Checkbox } from '@gx-design/checkbox';
import { useOnClickOutside } from '@gx-design/use-on-click-outside';
import { trans } from '@pepita-i18n/babelfish';
import { SortableList } from 'gtx-react/components/SortableList';
import {
    createContext,
    PropsWithChildren,
    ReactNode,
    useContext,
    useRef,
    useState,
} from 'react';
import clsx from 'clsx';
import { Table } from '@tanstack/react-table';
import { TableContext, useTableContext } from './contexts';
import { Tooltip } from '@gx-design/tooltip';
import useTable from '../GxTable/hooks/useTable';

type SortableProps = {
    defaultItems: string[];
    isOpen?: boolean;
    items: string[];
    onReset: (items: string[]) => void;
    onChange: (value: string[]) => void;
    /**
     *
     * @param item element id
     * @returns
     */
    renderItem: (item: string) => React.ReactNode;
};

const SortableContext = createContext<
    { disabled: boolean; id: string } | undefined
>(undefined);

export function useSortableContext() {
    const context = useContext(SortableContext);

    if (!context) {
        throw new Error(
            'useSortableContext must be used within a SortableContext'
        );
    }

    return context;
}

// TODO: verify with @fciabatta if this is the correct behavior
export function Sortable(props: SortableProps) {
    const table = useTableContext();

    const orderHasBeenChanged =
        !props.defaultItems.every((item, i) => item === props.items[i]) ||
        !table.getIsAllColumnsVisible();

    return (
        <>
            <div className="crm-sortable-list__header">
                <h3 className="crm-sortable-list__title">
                    {trans('label.columns')}
                </h3>
                {orderHasBeenChanged && (
                    <Button
                        variant="ghost"
                        size="small"
                        onClick={() => {
                            table.resetColumnVisibility();
                            props.onReset(props.defaultItems);
                        }}
                    >
                        <Icon name="circular-arrows" />
                        <span>{trans('label.restore')}</span>
                    </Button>
                )}
            </div>
            <SortableList
                className="crm-sortable-list__container"
                items={props.items.map((id) => ({ id }))}
                onChange={(value) => {
                    props.onChange(value.map((item) => item.id));
                }}
                renderItem={({ id }) => props.renderItem(id)}
            />
        </>
    );
}

function SortableItem(
    props: PropsWithChildren<{ id: string; disabled?: boolean }>
) {
    const table = useTableContext();
    const disabled =
        props.disabled ||
        !table.getDnd(props.id) ||
        Boolean(table.getColumn(props.id)?.getIsLastColumn('right'));

    if (disabled) {
        return null;
    }

    return (
        <SortableList.Item
            disabled={props.disabled}
            id={props.id}
            className={clsx('crm-sortable-list__item', {
                'crm-sortable-list__item--disabled': disabled,
            })}
        >
            <SortableContext.Provider
                value={{ disabled: disabled, id: props.id }}
            >
                <SortableList.DragHandler
                    disabled={disabled}
                    className="crm-sortable-list__itemDrag"
                >
                    <Icon name="dots" />
                </SortableList.DragHandler>
                {props.children}
            </SortableContext.Provider>
        </SortableList.Item>
    );
}

export function SortableButton<T>(
    props: Omit<SortableProps, 'isOpen'> & { table: Table<T> }
) {
    const [isOpen, setIsOpen] = useState(false);

    const containerRef = useRef<HTMLDivElement>(null);

    useOnClickOutside({
        refs: [containerRef],
        handler: () => setIsOpen(false),
        shouldListen: isOpen,
    });

    return (
        <TableContext.Provider value={props.table}>
            <Dropdown
                position="bottomRight"
                buttonIsIconOnly
                className="crm-sortable-listDropdown"
                buttonClassName="gx-button--small"
                buttonContent={
                    <Tooltip
                        text={trans('label.reorder_columns')}
                        position="topRight"
                    >
                        <Icon name="table" />
                    </Tooltip>
                }
            >
                <div className="crm-sortable-list" ref={containerRef}>
                    <Sortable
                        defaultItems={props.defaultItems}
                        items={props.items}
                        onChange={props.onChange}
                        isOpen={isOpen}
                        onReset={props.onReset}
                        renderItem={props.renderItem}
                    />
                </div>
            </Dropdown>
        </TableContext.Provider>
    );
}

export function SortableButtonCheckbox(props: {
    label?: ReactNode;
    onChange?: (isVisible: boolean) => void;
}) {
    const table = useTableContext();
    const { disabled, id } = useSortableContext();

    return (
        <Checkbox
            checked={Boolean(table.getColumn(id)?.getIsVisible())}
            onChange={(value) => {
                props.onChange && props.onChange(value.target.checked);

                table.getColumn(id)?.toggleVisibility();
            }}
            disabled={disabled}
            label={props.label || trans(`label.${id}`)}
        />
    );
}

Sortable.Item = SortableItem;
Sortable.Checkbox = SortableButtonCheckbox;

SortableButton.Item = SortableItem;
SortableButton.Checkbox = SortableButtonCheckbox;
