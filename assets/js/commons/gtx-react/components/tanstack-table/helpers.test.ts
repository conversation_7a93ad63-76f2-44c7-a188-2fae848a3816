import { describe, it, expect } from 'vitest';
import { getDefaultColumnOrder } from './helpers';

describe('getDefaultColumnOrder', () => {
    it('returns actualColumns when lengths are different', () => {
        const givenColumns = ['a', 'b'];
        const actualColumns = ['a', 'b', 'c'];

        const result = getDefaultColumnOrder(givenColumns, actualColumns);

        expect(result).toEqual(actualColumns);
    });

    it('filters out columns from givenColumns not present in actualColumns', () => {
        const givenColumns = ['a', 'b', 'c', 'd'];
        const actualColumns = ['a', 'c', 'e'];

        const result = getDefaultColumnOrder(givenColumns, actualColumns);

        expect(result).toEqual(['a', 'c', 'e']);
    });

    it('preserves order from givenColumns while adding missing columns', () => {
        const givenColumns = ['c', 'a', 'b'];
        const actualColumns = ['a', 'b', 'c', 'd'];

        const result = getDefaultColumnOrder(givenColumns, actualColumns);

        expect(result).toEqual(['a', 'b', 'c', 'd']);
    });

    it('works with empty arrays', () => {
        const givenColumns: string[] = [];
        const actualColumns: string[] = [];

        const result = getDefaultColumnOrder(givenColumns, actualColumns);

        expect(result).toEqual([]);
    });
});
