import type { Header, Table } from '@tanstack/react-table';
import { createContext, useContext, useState } from 'react';

/**
 * React context for storing a TanStack Table instance.
 * This context allows table instances to be passed down the component tree
 * without props drilling.
 */
export const TableContext = createContext<Table<any> | undefined>(undefined);

/**
 * Custom hook to access the current TanStack Table instance.
 *
 * @returns {Table<any>} The current table instance from context
 * @throws {Error} Throws an error if used outside of a TableContext provider
 * @example
 * ```tsx
 * const Component = () => {
 *   const table = useTableContext();
 *   // Use table instance methods and properties
 *   return <div>{table.getRowModel().rows.length} rows</div>;
 * };
 * ```
 */
export const useTableContext = <TData extends unknown>() => {
    const context = useContext(TableContext);
    if (!context) {
        throw new Error('useTableContext must be used within a TableContext');
    }
    return context as Table<TData>;
};

export const TableHeaderContext = createContext<Header<any, any> | undefined>(
    undefined
);
/**
 * Custom hook to access the current TanStack Table header instance.
 *
 * @returns {Header<any, any>} The current table header instance from context
 * @throws {Error} Throws an error if used outside of a TableHeaderContext provider
 * @example
 * ```tsx
 * const Component = () => {
 *   const header = useTableHeaderContext();
 *   // Use header instance methods and properties
 *   return <div>{header.id}</div>;
 * };
 * ```
 */
export const useTableHeaderContext = <
    TData = unknown,
    TValue = unknown,
>(): Header<TData, TValue> => {
    const context = useContext(TableHeaderContext);
    if (!context) {
        throw new Error(
            'useTableHeaderContext must be used within a TableHeaderContext'
        );
    }
    return context;
};

export const ResizeColumnHoverContext = createContext<
    | {
          hoveredColumnId: string | null;
          setHoveredColumnId: (id: string | null) => void;
      }
    | undefined
>(undefined);

export const useResizeColumnHoverContext = (): {
    hoveredColumnId: string | null;
    setHoveredColumnId: (id: string | null) => void;
} => {
    const context = useContext(ResizeColumnHoverContext);
    if (!context) {
        throw new Error(
            'useResizeColumnHoverContext must be used within a ResizeColumnHoverContext'
        );
    }
    return context;
};

export const ResizeColumnHoverProvider = ({
    children,
}: {
    children: React.ReactNode;
}) => {
    const [hoveredColumnId, setHoveredColumnId] = useState<string | null>(null);

    return (
        <ResizeColumnHoverContext.Provider
            value={{ hoveredColumnId, setHoveredColumnId }}
        >
            {children}
        </ResizeColumnHoverContext.Provider>
    );
};

TableHeaderContext.displayName = 'TableHeaderContext';
TableContext.displayName = 'TableContext';
