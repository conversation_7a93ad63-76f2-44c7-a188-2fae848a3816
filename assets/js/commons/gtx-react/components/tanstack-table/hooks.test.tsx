import { act, renderHook } from '#tests/react/testing-library-enhanced';
import { describe, expect, it } from 'vitest';
import {
    useDefaultOrderingColumns,
    useFiltersSearchParams,
    usePaginationSearchParams,
    useSortingSearchParams,
} from './hooks';
import { MemoryRouter, useSearchParams } from 'react-router-dom';
import { createColumnHelper } from '@tanstack/react-table';

const useTestPaginationParams = () => {
    return {
        pagination: usePaginationSearchParams({ pageIndex: 0, pageSize: 10 }),
        searchParams: useSearchParams(),
    };
};

describe('tanstack-table helpers', () => {
    it('useFiltersSearchParams should work properly', () => {
        const { result } = renderHook(() => useFiltersSearchParams(), {
            wrapper: ({ children }) => (
                <MemoryRouter initialEntries={['/?test=1&page=1']}>
                    {children}
                </MemoryRouter>
            ),
        });

        expect(result.current[0]).toBeInstanceOf(URLSearchParams);
        expect(result.current[0].get('test')).toBe('1');

        // Expect is a function
        expect(result.current[1]).toBeInstanceOf(Function);

        // Change the search params
        act(() => {
            result.current[1]({ test: '2', page: 2 });
        });

        // Check if the search params have changed
        expect(result.current[0].get('test')).toBe('2');
        // Check if the page has been changed too
        expect(result.current[0].get('page')).toBe('2');

        // Check if the reset function is a function
        expect(result.current[2]).toBeInstanceOf(Function);
        act(() => {
            result.current[2](['test'])();
        });

        expect(result.current[0].has('test')).toBe(false);
        // Check if the page has been reset to 1
        expect(result.current[0].get('page')).toBe('1');
    });

    it('useFiltersSearchParams should not arbitrarly set page number', () => {
        const { result } = renderHook(() => useFiltersSearchParams(), {
            wrapper: ({ children }) => (
                <MemoryRouter initialEntries={['/?test=1']}>
                    {children}
                </MemoryRouter>
            ),
        });

        expect(result.current[0]).toBeInstanceOf(URLSearchParams);
        expect(result.current[0].get('page')).toBe(null);

        // Change the search params
        act(() => {
            result.current[1]({ test: '2' });
        });

        expect(result.current[0].get('page')).toBe(null);

        // Check reset
        act(() => {
            result.current[2](['test'])();
        });

        expect(result.current[0].has('page')).toBe(false);
    });

    it('useFiltersSearchParams should works with array', () => {
        const { result } = renderHook(() => useFiltersSearchParams(), {
            wrapper: ({ children }) => (
                <MemoryRouter initialEntries={['/?test=1']}>
                    {children}
                </MemoryRouter>
            ),
        });

        expect(result.current[0]).toBeInstanceOf(URLSearchParams);
        expect(result.current[0].get('page')).toBe(null);

        // Change the search params
        act(() => {
            result.current[1]({ test: ['2', '3'] });
        });

        expect(result.current[0].getAll('test')).toStrictEqual(['2', '3']);

        // Check reset
        act(() => {
            result.current[2](['test'])();
        });

        expect(result.current[0].has('page')).toBe(false);
    });

    it('usePaginationSearchParams should work properly', () => {
        const { result } = renderHook(() => useTestPaginationParams(), {
            wrapper: ({ children }) => (
                <MemoryRouter initialEntries={['/?page=1']}>
                    {children}
                </MemoryRouter>
            ),
        });

        expect(result.current.pagination[0].pageIndex).toBe(0);
        expect(result.current.pagination[0].pageSize).toBe(10);

        expect(result.current.searchParams[0]).toBeInstanceOf(URLSearchParams);
        expect(result.current.searchParams[0].get('page')).toBe('1');

        // Expect is a function
        expect(result.current.pagination[1]).toBeInstanceOf(Function);

        // Change the search params
        act(() => {
            result.current.pagination[1]({ pageIndex: 1, pageSize: 20 });
        });
        // Check if the search params have changed
        expect(result.current.pagination[0].pageIndex).toBe(1);
        expect(result.current.pagination[0].pageSize).toBe(20);
        // This is the correct behavior because we are using the pageIndex as 0-based
        expect(result.current.searchParams[0].get('page')).toBe('2');
    });

    it('useSortingSearchParams should work properly', () => {
        const { result } = renderHook(
            () => useSortingSearchParams([{ id: 'id', desc: true }]),
            {
                wrapper: ({ children }) => (
                    <MemoryRouter initialEntries={['/']}>
                        {children}
                    </MemoryRouter>
                ),
            }
        );
        // Check the default behavior
        expect(result.current[0]).toStrictEqual([{ id: 'id', desc: true }]);

        // Change the sorting state

        act(() => {
            result.current[1]([
                { id: 'name', desc: false },
                { id: 'id', desc: true },
            ]);
        });

        // Check if the sorting state has changed
        expect(result.current[0]).toStrictEqual([
            { id: 'name', desc: false },
            { id: 'id', desc: true },
        ]);
    });

    function getColumns() {
        const columnHelper = createColumnHelper();

        return [
            columnHelper.accessor('id', {
                header: 'ID',
                cell: (info) => info.getValue(),
            }),
            columnHelper.accessor('name', {
                header: 'Name',
                cell: (info) => info.getValue(),
            }),
            columnHelper.accessor('age', {
                header: 'Age',
                cell: (info) => info.getValue(),
            }),
        ];
    }

    it('useDefaultOrderingColumns should work properly', () => {
        const { result } = renderHook(() =>
            useDefaultOrderingColumns(getColumns())
        );

        expect(result.current).toStrictEqual(['id', 'name', 'age']);
    });
});
