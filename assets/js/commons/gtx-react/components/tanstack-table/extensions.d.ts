import { RowData } from '@tanstack/react-table';
import { <PERSON>ag<PERSON><PERSON><PERSON><PERSON>Header, DragAndDropInstance, DragAndDropOptions, DragAndDropTableState } from './v1/feature';

// Use declaration merging to add our new feature APIs and state types to TanStack Table's existing types.
declare module '@tanstack/react-table' {
    // or whatever framework adapter you are using
    //merge our new feature's state with the existing table state
    interface TableState extends DragAndDropTableState {}
    //merge our new feature's options with the existing table options
    interface TableOptionsResolved<TData extends RowData> extends DragAndDropOptions {}
    //merge our new feature's instance APIs with the existing table instance APIs
    interface Table<TData extends RowData> extends DragAndDropInstance {}
    // if you need to add cell instance APIs...
    // interface Cell<TData extends RowData, TValue> extends DensityCell
    // if you need to add row instance APIs...
    // interface Row<TData extends RowData> extends DensityRow
    // if you need to add column instance APIs...
    // interface Column<TData extends RowData, TValue> extends DensityColumn
    // if you need to add header instance APIs...
    interface Header<TData extends RowData, TValue> extends DragAndDropHeader {}
    interface ColumnMeta<TData extends RowData, TValue> {
        /**
         * The label for the column, used in the header and other UI elements.
         */
        label: string;
    }

    // Note: declaration merging on `ColumnDef` is not possible because it is a complex type, not an interface.
    // But you can still use declaration merging on `ColumnDef.meta`
}
