import { Tag, TagProps } from '@gx-design/tag';
import { trans } from '@pepita/babelfish';

export const FILE_TYPE = 'file';
export const PHOTO_TYPE = 'photo';
export const VIDEO_TYPE = 'video';

type AttachmentTagProps = {
    /**
     * Attachment type
     */
    type: 'file' | 'photo' | 'video';
    /**
     * Attachment count
     */
    count?: number;
    /**
     * Close click handler
     */
    onCloseClick?: () => void;
};

export const AttachmentTag: React.FC<AttachmentTagProps> = ({
    type,
    count,
    onCloseClick,
}) => {
    const getTextAndIcon = () => {
        switch (type) {
            case FILE_TYPE:
                return {
                    text: trans('label.file'),
                    icon: 'docs',
                };
            case PHOTO_TYPE:
                return {
                    text: trans('label.photo'),
                    icon: 'image',
                };
            case VIDEO_TYPE:
                return {
                    text: trans('label.video'),
                    icon: 'video',
                };
            default:
                return {
                    text: trans('label.photo'),
                    icon: 'image',
                };
        }
    };

    const { text, icon } = getTextAndIcon();

    return (
        <Tag
            dismissable
            icon={icon as TagProps['icon']}
            text={`${count ?? ''} ${text}`}
            onCloseClick={onCloseClick}
        />
    );
};
