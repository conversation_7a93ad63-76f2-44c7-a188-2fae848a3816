import gtxConstants from '@getrix/common/js/gtx-constants';

import { GeographyInformation } from 'lib/REST/types/detail-property';
import {
    generateGeodesicCircle,
    getRandomizedCenter,
    PolygonPoint,
} from 'lib/staticMapUtils';

// TODO: change the window.globalVars.STATIC_MAP_BASE_URL with the one used here
// const STATIC_PIN_MAP_URL = window.globalVars.STATIC_MAP_BASE_URL;
const STATIC_MAP_BASE_URL = 'https://maps.im-cdn.it/static';

const BORDER_WEIGHT = 2;
const BORDER_COLOR = '0x0074c1FF';
const FILL_COLOR = '0x0074c133';

/** Map server cannot use a marker with a url pointing on our personal environments; default marker will be shown (different blue) */
const MARKER_URL = `${gtxConstants(
    'VHOST_URL_HOST'
)}/bundles/base/getrix/common/img/<EMAIL>`;
// const MARKER_URL = 'https://gestionale.immobiliare.it/bundles/base/getrix/common/img/<EMAIL>';

/** Mapping for the `flagAddress` field contained into ad's geography information */
export const FLAG_ADDRESS_MAP_MODE_MAPPING: Record<
    GeographyInformation['flagAddress'],
    StaticImgMapProps['mode']
> = {
    0: 'map-only',
    1: 'pin',
    2: 'approximate-circle',
};
/** Mapping to show a proper translation for the `flagAddress` field contained into ad's geography information */
export const FLAG_PIN_TRANSLATIONS_MAP: Record<
    GeographyInformation['flagAddress'],
    string
> = {
    0: 'insert_map.no_information',
    1: 'insert_map.show_marker_on_map',
    2: 'insert_map.show_area_near_property',
};

type CommonProps = {
    /** Map mode, see component's doc for more info */
    mode: 'pin' | 'map-only' | 'approximate-circle' | 'polygon';
    /** Map's width */
    width: number;
    /** Map's height */
    height: number;
} & Omit<
    React.ImgHTMLAttributes<HTMLImageElement>,
    'width' | 'height' | 'scale'
>;

type WithPolygonProps = {
    mode: 'polygon';
    /**
     * Array of Coordinates ({ latitude: number; longitude: number }):
     * these points will be connected to draw a polygon
     */
    coordinates: Array<PolygonPoint>;
    /** Width of the polygon's border, defaults to `2` */
    borderWeight?: number;
    /** Color of the polygon's border, defaults to `0x0074c1FF` */
    borderColor?: string;
    /** Color of the polygon's area inside the border, defaults to `0x0074c133` */
    fillColor?: string;
};

type WithPinProps = {
    mode: 'pin';
    /** Marker's latitude */
    latitude: number;
    /** Marker's longitude */
    longitude: number;
    /** Marker's scale, defaults to `2` */
    scale?: number;
    /** Custom marker's url */
    markerUrl?: string;
    /** Map's zoom */
    zoom: number;
};

type WithMapOnlyProps = {
    mode: 'map-only';
    /** Latitude representing the center of the map */
    latitude: number;
    /** Longitude representing the center of the map */
    longitude: number;
    /** Map's zoom */
    zoom: number;
};

type WithCircleProps = {
    mode: 'approximate-circle';
    /**
     * Latitude passed to `getRandomizedCenter`;
     * resulting circle will be at the center of the map
     */
    latitude: number;
    /**
     * Longitude passed to `getRandomizedCenter`;
     * resulting circle will be at the center of the map
     */
    longitude: number;
    /** Map's zoom */
    zoom: number;
    /** Width of the circle's border, defaults to `2` */
    borderWeight?: number;
    /** Color of the circle's border, defaults to `0x0074c1FF` */
    borderColor?: string;
    /** Color of the circle's area inside the border, defaults to `0x0074c133` */
    fillColor?: string;
};

export type StaticImgMapProps = CommonProps &
    (WithPolygonProps | WithPinProps | WithMapOnlyProps | WithCircleProps);

/**
 * Renders a static map image with different display modes, with the possibility to customize the image size (width and height), map's zoom and marker (image and scale, for `pin` mode only).
 *
 * Available `mode` values:
 *
 * 1. "pin"
 *
 * ```text
 * ┌────────────────────┐
 * │                    │
 * │         📍         │  ← pin in the center
 * │                    │
 * └────────────────────┘
 * ```
 *
 * 2. "map-only"
 *
 * ```text
 * ┌────────────────────┐
 * │                    │
 * │                    │  ← empty map
 * │                    │
 * └────────────────────┘
 * ```
 *
 * 3. "approximate-circle"
 *
 * ```text
 * ┌────────────────────┐
 * │       ____         │
 * │    .-'    `-.      │
 * │  .'          `.    │
 * │ /              \   │  ← circular-like polygon in the center of the map
 * │ \              /   │
 * │  `.          .'    │
 * │    `-.____.-'      │
 * └────────────────────┘
 * ```
 *
 * 4. "polygon"
 *
 * ```text
 * ┌────────────────────┐
 * │        ●           │
 * │       / \          │
 * │      /   \         │
 * │     ●     ●        │  ← polygon in the center of the map
 * │      \   /         │
 * │       \ /          │
 * │        ●           │
 * └────────────────────┘
 * ```
 */
export const StaticImgMap: React.FC<StaticImgMapProps> = (props) => {
    const { width, height, mode, ...otherProps } = props;
    const commonParams = {
        size: `${width}x${height}`,
    };

    switch (mode) {
        case 'pin': {
            const {
                scale = 2,
                markerUrl = MARKER_URL,
                latitude,
                longitude,
                zoom,
                ...rest
            } = otherProps as WithPinProps;
            const params = new URLSearchParams({
                ...commonParams,
                zoom: `${zoom}`,
                markers: `scale:${scale}|icon:${markerUrl}|${latitude},${longitude}`,
            });

            return <img {...rest} src={`${STATIC_MAP_BASE_URL}?${params}`} />;
        }
        case 'map-only': {
            const { latitude, longitude, zoom, ...rest } =
                otherProps as WithMapOnlyProps;
            const params = new URLSearchParams({
                ...commonParams,
                zoom: `${zoom}`,
                center: `${latitude},${longitude}`,
            });

            return <img {...rest} src={`${STATIC_MAP_BASE_URL}?${params}`} />;
        }
        case 'approximate-circle': {
            const {
                latitude,
                longitude,
                zoom,
                fillColor = FILL_COLOR,
                borderColor = BORDER_COLOR,
                borderWeight = BORDER_WEIGHT,
                ...rest
            } = otherProps as WithCircleProps;
            const coordinates = generateGeodesicCircle(
                getRandomizedCenter({ latitude, longitude }, 290),
                300
            );
            const params = new URLSearchParams({
                ...commonParams,
                zoom: `${zoom}`,
                path: `color:${borderColor}|fillcolor:${fillColor}|weight:${borderWeight}|${coordinates}`,
            });

            return <img {...rest} src={`${STATIC_MAP_BASE_URL}?${params}`} />;
        }
        case 'polygon': {
            const {
                coordinates,
                fillColor = FILL_COLOR,
                borderColor = BORDER_COLOR,
                borderWeight = BORDER_WEIGHT,
                ...rest
            } = otherProps as WithPolygonProps;
            const points = coordinates?.reduce(
                (prev, curr) => `${prev}|${curr.latitude},${curr.longitude}`,
                ''
            );
            const params = new URLSearchParams({
                ...commonParams,
                path: `color:${borderColor}|fillcolor:${fillColor}|weight:${borderWeight}${points}`,
            });

            return <img {...rest} src={`${STATIC_MAP_BASE_URL}?${params}`} />;
        }
        default:
            return null;
    }
};
