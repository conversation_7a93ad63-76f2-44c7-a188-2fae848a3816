import React, { useRef, useState, useEffect, createRef } from 'react';
import classNames from 'classnames';
import useCardList from './hooks/useCardList';
import { Checkbox } from '@gx-design/checkbox';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { Dropdown } from '@gx-design/dropdown';
import { ActionList, ActionListItem } from '@gx-design/action-list';
import { EmptyState } from '@gx-design/empty-state';
import useClickWithExcludedElements from 'gtx-react/hooks/useClickWithExcludedElements';
import {
    Card as CardType,
    CardListOptions,
    CardItem,
    CardAction,
    CardField,
    CardListConfigs,
    CardListActions,
    CardListLabels,
    MenuAction,
} from './types';
import { ApiCallOnVisibleProvider } from './ApiCallOnVisibleProvider';
import { useApiCallOnVisibileRef } from './hooks/useApiCallOnVisibleRef';
import { Tooltip } from '@gx-design/tooltip';

const DEFAULT_LABELS: CardListLabels = {
    singleCardSelected: 'selected',
    moreCardsSelected: 'selected',
    showMore: 'Show more',
    showLess: 'Show less',
};

type BulkActionsProps = {
    /**
     * Bulk actions list.
     */
    actions: CardListActions['bulk'] | null;
    /**
     * UI labels.
     */
    labels: CardListLabels;
    /**
     * Ids selected cards list.
     */
    selectedCards: CardType['id'][];
};

type CardProps<T = unknown> = {
    actions: Pick<CardListActions, 'main' | 'quick' | 'menu'> | null;
    actionsHelper?: (...props: any) => void | null;
    data: CardItem;
    fields: CardField<T>[];
    labels: CardListLabels;
    mainFieldKey?: string;
    onCardSelectionChange: (data: CardType['id']) => void;
    selectedCards: CardType['id'][];
    selectionAction: CardListConfigs['itemSelection'];
    twoColumnsView: boolean;
    displayShowMoreButton?: boolean;
    configs: CardListConfigs;
};

function getSelectedLabel(
    selectedCards: CardType['id'][],
    labels: CardListConfigs['labels']
): string {
    let selectedLabel = labels?.singleCardSelected?.toLowerCase();

    if (selectedCards && selectedCards.length && selectedCards.length > 1) {
        selectedLabel = labels?.moreCardsSelected?.toLowerCase();
    }

    return `${selectedCards.length} ${selectedLabel}`;
}

export const CardList = <T,>({
    actions,
    cardFields,
    cardsData,
    configs: configIn,
    twoColumnsView,
    noInitialItems,
    displayShowMoreButton = true,
}: CardListOptions<T>) => {
    const {
        bulkActions,
        mainFieldKey,
        onCardSelection,
        cards,
        cardActions,
        selectedCards: selectedCardsIn,
    } = useCardList<T>({ actions, cardFields, cardsData });

    const configs = typeof configIn === 'undefined' ? {} : configIn;

    const selectedCards = selectedCardsIn.filter((itm) => {
        if (typeof configs.itemSelection === 'function') {
            const itemSelectionObj = configs?.itemSelection(itm.toString());

            return itemSelectionObj.status === true;
        } else {
            return true;
        }
    });

    const labels = configs.labels ?? DEFAULT_LABELS;
    let emptyButton = (
        <Button onClick={configs.emptyState?.buttonOnClick}>
            {configs.emptyState?.buttonLabel}
        </Button>
    );

    const showEmptyButton: boolean = !!(
        noInitialItems &&
        configs.emptyState?.buttonOnClick &&
        configs.emptyState?.buttonLabel
    );

    return (
        <>
            {!cards.length && configs.emptyState ? (
                <EmptyState
                    title={configs.emptyState.text}
                    img={configs.emptyState.image}
                >
                    {showEmptyButton ? emptyButton : <></>}
                </EmptyState>
            ) : (
                <div className="gx-card-list">
                    <ApiCallOnVisibleProvider>
                        {cards && cards.length
                            ? cards.map((card: CardItem) => (
                                  <Card
                                      configs={configs}
                                      key={`row_${card.extra.id}`}
                                      data={card}
                                      fields={cardFields}
                                      actions={cardActions}
                                      selectionAction={configs.itemSelection}
                                      actionsHelper={configs.itemActionsHelper}
                                      labels={labels}
                                      onCardSelectionChange={onCardSelection}
                                      selectedCards={selectedCards}
                                      mainFieldKey={mainFieldKey}
                                      twoColumnsView={twoColumnsView}
                                      displayShowMoreButton={
                                          displayShowMoreButton
                                      }
                                  />
                              ))
                            : null}
                    </ApiCallOnVisibleProvider>

                    <BulkActions
                        actions={bulkActions}
                        labels={labels}
                        selectedCards={selectedCards}
                    />
                </div>
            )}
        </>
    );
};

const BulkActions: React.FC<BulkActionsProps> = ({
    actions,
    labels,
    selectedCards,
}) => {
    if (!selectedCards.length) {
        return null;
    }

    return (
        <div className="gx-card-list__bulkActions">
            <span>{getSelectedLabel(selectedCards, labels)}</span>
            {actions &&
                actions.map((bulkAction: CardAction) => (
                    <Button
                        key={`header_bulk_action_${bulkAction.icon}`}
                        variant="ghost"
                        onClick={() => bulkAction.action(selectedCards)}
                        iconOnly
                    >
                        <Icon name={bulkAction.icon as any} />
                    </Button>
                ))}
        </div>
    );
};

const Card = <T,>({
    actions,
    actionsHelper,
    data,
    fields,
    labels,
    mainFieldKey,
    onCardSelectionChange,
    selectedCards,
    selectionAction = false,
    twoColumnsView,
    displayShowMoreButton,
    configs,
}: CardProps<T>) => {
    const id = data.extra.id;

    const cardRef = useRef<HTMLDivElement | null>(null);
    const [hiddenAdditionalInfo, setHiddenAdditionalInfo] = useState(
        twoColumnsView ? false : true
    );
    const showMoreLessButtonRef = useRef(null);
    const cardHeaderRef = useRef(null);
    const menuActionsRefs = useRef(data.rows.map(() => createRef()));

    const mainAction = actionsHelper
        ? actionsHelper(id, actions, 'main')
        : actions?.main;
    const quickActions = actionsHelper
        ? actionsHelper(id, actions, 'quick')
        : actions?.quick;
    const menuActions = actionsHelper
        ? actionsHelper(id, actions, 'menu')
        : actions?.menu;

    const onCardClick = useClickWithExcludedElements(
        [cardHeaderRef, showMoreLessButtonRef, ...menuActionsRefs.current],
        () => (mainAction ? mainAction.action(id) : null)
    );

    const handleResize = () => {
        if (twoColumnsView) {
            setHiddenAdditionalInfo(false);
            return;
        }

        setHiddenAdditionalInfo(true);
    };

    useEffect(() => {
        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, [twoColumnsView]);

    if (!id) {
        return null;
    }

    const isCardSelected = selectedCards && selectedCards.includes(id);

    const getFieldLabel = (key: CardField['key']): string => {
        const fieldData = fields.find(
            (field: CardField<T>) => field.key === key
        );

        if (!fieldData) {
            return '';
        }

        return fieldData.label || '';
    };

    let showCheckbox = false;
    let isCheckBoxDisabled = false;
    let checkBoxTooltipText = '';
    if (selectionAction && typeof selectionAction === 'function') {
        const itemSelectionFuncValue = selectionAction(id);
        showCheckbox =
            itemSelectionFuncValue.status === 'disabled' ||
            !!itemSelectionFuncValue.status;
        isCheckBoxDisabled = itemSelectionFuncValue.status === 'disabled';
        checkBoxTooltipText = itemSelectionFuncValue.tooltip || '';
    } else {
        showCheckbox = !!configs.itemSelection;
    }

    return (
        <div
            ref={cardRef}
            className={classNames('gx-card', {
                'gx-card--col2': twoColumnsView,
            })}
            onClick={onCardClick}
        >
            <div className="gx-card__header" ref={cardHeaderRef}>
                {showCheckbox &&
                    (checkBoxTooltipText ? (
                        <Tooltip text={checkBoxTooltipText} position="top">
                            <div>
                                <Checkbox
                                    checked={isCardSelected}
                                    disabled={isCheckBoxDisabled}
                                    onChange={() => onCardSelectionChange(id)}
                                />
                            </div>
                        </Tooltip>
                    ) : (
                        <Checkbox
                            checked={isCardSelected}
                            disabled={isCheckBoxDisabled}
                            onChange={() => onCardSelectionChange(id)}
                        />
                    ))}

                <div className="gx-card__headerActions">
                    {quickActions
                        ? quickActions.map((quickAction: CardAction) => (
                              <Button
                                  key={`cell_action_${quickAction.icon}_${id}`}
                                  variant="ghost"
                                  as={quickAction.link ? 'a' : 'button'}
                                  onClick={() => quickAction?.action(id)}
                                  href={
                                      quickAction.link
                                          ? quickAction.link(id)
                                          : null
                                  }
                                  iconOnly
                              >
                                  <Icon name={quickAction.icon as any} />
                              </Button>
                          ))
                        : null}
                    {menuActions ? (
                        <Dropdown
                            buttonIsIconOnly={true}
                            buttonVariant="ghost"
                            buttonContent={<Icon name="ellipsis" />}
                            position="bottomRight"
                        >
                            <ActionList>
                                {menuActions.map(
                                    (menuAction: MenuAction, index) => (
                                        <span
                                            key={`span_wrap_action_list_item_${menuAction.label}`}
                                            ref={menuActionsRefs.current[index]}
                                            onClick={(e) => {
                                                e?.preventDefault();
                                                menuAction.action(id);
                                            }}
                                        >
                                            <ActionListItem
                                                text={menuAction?.label || ''}
                                                onClick={() =>
                                                    menuAction.action(id)
                                                }
                                            />
                                        </span>
                                    )
                                )}
                            </ActionList>
                        </Dropdown>
                    ) : null}
                </div>
            </div>
            <div
                className={classNames(
                    'gx-card__content gx-card__content--noPadding',
                    {
                        'is-close': !twoColumnsView && hiddenAdditionalInfo,
                        'is-open': !hiddenAdditionalInfo,
                    }
                )}
            >
                {data.rows.map((row: CardItem['rows']) => {
                    if (!row?.visible) {
                        return;
                    }

                    return row.key === mainFieldKey ? (
                        <div
                            key={`row_${row.key}_${id}`}
                            className="gx-card-row gx-card-row--property"
                        >
                            <div className="gx-card-photo">{row.content}</div>
                        </div>
                    ) : (
                        <div
                            key={`row_${row.key}_${id}`}
                            className="gx-card-row"
                        >
                            <div className="gx-card-row__label">
                                {getFieldLabel(row.key)}
                            </div>
                            {row.asyncContent ? (
                                <AsyncRow row={row} cardId={id} />
                            ) : (
                                <div className="gx-card-row__value">
                                    {row.content}
                                </div>
                            )}
                        </div>
                    );
                })}
                {!twoColumnsView && displayShowMoreButton && (
                    <div
                        ref={showMoreLessButtonRef}
                        className="gx-card__showMore"
                    >
                        <Button
                            variant="ghost"
                            onClick={() =>
                                setHiddenAdditionalInfo(!hiddenAdditionalInfo)
                            }
                        >
                            <span>
                                {hiddenAdditionalInfo
                                    ? labels.showMore
                                    : labels.showLess}
                            </span>
                            <Icon
                                name={
                                    hiddenAdditionalInfo
                                        ? 'arrow-down'
                                        : 'arrow-top'
                                }
                            />
                        </Button>
                    </div>
                )}
            </div>
        </div>
    );
};

const AsyncRow = ({ cardId, row }) => {
    const rowRef = useRef<HTMLDivElement | null>(null);
    const { results, isLoading, apiCallError } = useApiCallOnVisibileRef(
        rowRef,
        cardId,
        row.asyncContent,
        {
            abortOnOutOfView: true,
        }
    );

    return (
        <div ref={rowRef}>
            {isLoading ? (
                <Icon name="loader" className="gx-spin" />
            ) : (
                <>{apiCallError ? '---' : results}</>
            )}
        </div>
    );
};
