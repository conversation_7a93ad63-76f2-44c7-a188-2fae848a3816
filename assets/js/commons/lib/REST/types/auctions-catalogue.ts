import { LookupItem, LookupItemList, Pagination } from './shared';

export type AuctionsCatalogueQueryType = {
    filters: FiltersType;
    ordering: SortingType;
    pagination: Pagination;
    auctions: AuctionType[];
};

export type FiltersType = {
    region?: string | null;
    province?: string | null;
    city?: string | null;
    zones?: string | null;
    category?: string | null;
    typology?: string | null;
    courts?: string[] | null;
    procedureType?: string | null;
    procedureNumber?: string | null;
    procedureYear?: string | null;
    minimumOffer_from?: string | null;
    minimumOffer_to?: string | null;
    price_from?: string | null;
    price_to?: string | null;
    expirationDate_from?: string | null;
    expirationDate_to?: string | null;
    importStatus?: string | null;
};

export type AuctionType = {
    id: string;
    court: string;
    procedureNumber: string;
    procedureYear: number;
    expirationDate: string;
    procedureType: string;
    procedureAdType: string | null;
    procedureReferent: string | null;
    procedureCode: string | null;
    urlPvp: string | null;
    procedureRegister: string | null;
    procedureBatchNumber: string | null;
    procedureBatchCategory: string | null;
    procedureRitual: string | null;
    otherOrganization: string | null;
    saleLocation: string | null;
    saleLocationAddress: string | null;
    saleReferent: string | null;
    securityDeposit: string | null;
    expenseAccountDeposit: string | null;
    minimumRise: string | null;
    auctionMinimumRise: string | null;
    presentationLocation: string | null;
    debtReservedExpenses: string | null;
    contributionNotDue: string | null;
    exemptionReason: string | null;
    expertiseValue: string | null;
    depositExpenses: string | null;
    depositMode: string | null;
    partecipationNotes: string | null;
    minimumOffer: string;
    price: string;
    mainImage: string | null;
    images: string[] | null;
    imagesInternal: string[] | null;
    properties: PropertyType[];
    sales: SaleType[] | null;
    documents: DocumentType[] | null;
    imported: boolean;
    updated: boolean | null;
    printableTypology: string;
    printableExpirationDate: string;
    printableExpirationEnd: string;
    propertiesMaps: {
        standard: string;
        double: string;
    };
    geographyInformations: {
        city: CityType | null;
        macroZone: MacroZoneType | null;
        address: {
            street: string;
            number: string | null;
        };
        coordinates: {
            lat: number;
            lon: number;
        } | null;
        showAddress: string | null;
        printableCity: string | null;
        printableAddress: string;
        printableZone: string | null;
        macrozone: string | null;
    };
};

export type DocumentType = {
    type: string;
    name: string;
    mimeType: string;
    urlInternal: string;
};

export type DocumentMutationType = {
    name: string;
    mimeType: string;
    url: string;
};

export type SaleType = {
    date: string;
    type: string;
    basePrice: string;
    mode: string;
    status: string;
};

export type PropertyType = {
    id: string | null;
    reference: string | null;
    typologyV2: TypologyType;
    propertyTypology: string | null;
    geographyInformation: {
        city: CityType | null;
        macroZone: MacroZoneType | null;
        address: {
            street: string;
            number: string | null;
        };
        coordinates: {
            lat: number;
            lon: number;
        } | null;
        showAddress: string | null;
        printableCity: string | null;
        printableAddress: string;
    };
    propertyStatus: string | null;
    composition: string | null;
    features: string | null;
    imagesList: string[] | null;
    plansList: string[] | null;
    consistences: string | null;
    condominiumInformation: string | null;
    contractInformation: string | null;
    heating: string | null;
    cadastralData: string | null;
    descriptions: string | null;
    energyClass: string | null;
    batch: string | null;
    office: string | null;
    garage: string | null;
    land: string | null;
    industrial: string | null;
    shop: string | null;
    sharedApartmentInformation: string | null;
    isMain: boolean;
    surface: string | null;
    rooms: number;
    bathrooms: number | null;
    floor: number | null;
};

export type TypologyType = {
    id: number;
    name: string;
    parent: TypologyType | null;
};

export type CityType = {
    id: number;
    name: string;
    province: ProvinceType;
    cityMacroZoneType: any;
    chiefTown: string | null;
    istatCode: string | null;
    coordinates: {
        lat: number;
        lon: number;
    } | null;
};

export type ProvinceType = {
    id: string;
    name: string;
};

export type MacroZoneType = {
    id: number;
    name: string;
    nameSn: string | null;
    keyUrl: string | null;
    city: string | null;
};

export type SortingType = {
    field: string;
    direction: string;
};

export type ContentType = {
    categories: LookupItemList;
    resultsPaginationOptions: LookupItemList;
    orderingItems: {
        field: string;
        label: string;
    }[];
    auctionImagePlaceholder: string;
    status: {
        imported: LookupItem;
        to_import: LookupItem;
        update: LookupItem;
    };
};
