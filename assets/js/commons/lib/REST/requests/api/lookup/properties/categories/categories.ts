import { withSearchParams } from 'lib/api';
import { validateToJson } from 'lib/REST/helpers/utils';
import { LookupItemList, RESTArgs } from 'lib/REST/types/shared';

getTypologyByCategory.endpoint = '/api/lookup/properties/categories/:categoryId/typologies' as const;

export async function getTypologyByCategory(args: RESTArgs<{ params: { categoryId: string | number } }>) {
    return validateToJson<LookupItemList>(
        fetch(getTypologyByCategory.endpoint.replace(':categoryId', `${args.params.categoryId}`))
    );
}

getCategories.endpoint = '/api/lookup/properties/categories' as const;

export async function getCategories(
    args?: RESTArgs<{
        query?: {
            excludedCategories?: Array<number>;
        };
    }>
) {
    return validateToJson<LookupItemList>(fetch(withSearchParams(getCategories.endpoint, args?.query, { mode: 'comma' })));
}
