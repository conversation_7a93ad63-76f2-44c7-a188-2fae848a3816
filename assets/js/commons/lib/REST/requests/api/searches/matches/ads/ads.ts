import { withSearchParams } from 'lib/api';
import { validateTo<PERSON><PERSON> } from 'lib/REST/helpers/utils';
import { RESTArgs } from 'lib/REST/types/shared';
import { Stat } from './types';

getStatsByProperties.endpoint = '/api/searches/matches/ads/stats' as const;

export function getStatsByProperties(
    args: RESTArgs<{
        query: {
            propertyIds: Array<string> | Array<number>;
        };
    }>
) {
    return validateToJson<Array<Stat>>(
        fetch(
            withSearchParams(getStatsByProperties.endpoint, { propertyIds: args.query.propertyIds }, { mode: 'comma' })
        )
    );
}
