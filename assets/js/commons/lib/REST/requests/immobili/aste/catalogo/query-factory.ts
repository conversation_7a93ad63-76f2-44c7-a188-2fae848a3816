import { RESTArgsOf } from 'lib/REST/types/shared';
import { queryOptions } from '@tanstack/react-query';
import { getAuctionById, getAuctions } from './auctions';
import { getTos } from './tos';

const prefix = ['immobili', 'aste', 'catalogo'];

export const createAuctionsQueryOptions = (args: RESTArgsOf<typeof getAuctions>) => {
    return queryOptions({
        queryKey: [...prefix, 'auctions', args?.query],
        queryFn: () => getAuctions(args),
    });
};

export const createAuctionByIdQueryOptions = (args: RESTArgsOf<typeof getAuctionById>) => {
    return queryOptions({
        queryKey: [...prefix, args?.params.auctionId, 'detail'],
        queryFn: () => getAuctionById(args),
    });
};

export const createTosQueryOptions = () => {
    return queryOptions({
        queryKey: [...prefix, 'tos'],
        queryFn: () => getTos(),
    });
};
