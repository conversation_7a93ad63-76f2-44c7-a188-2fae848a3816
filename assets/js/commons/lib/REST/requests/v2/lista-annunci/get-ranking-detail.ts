import { withSearchParams } from 'lib/api';
import { validateLegacyToJson } from 'lib/REST/helpers/utils';
import { RankingDetailsApiResponse } from 'lib/REST/types/rankingDetailModal';
import { RESTArgs } from 'lib/REST/types/shared';

getRankingDetail.endpoint = '/v2/lista-annunci/get-ranking-detail' as const;

export function getRankingDetail(args: RESTArgs<{ query: { adId: string } }>) {
    return validateLegacyToJson<RankingDetailsApiResponse>(
        fetch(withSearchParams(getRankingDetail.endpoint, args?.query))
    );
}
