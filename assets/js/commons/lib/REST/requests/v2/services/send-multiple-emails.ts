import { withFormUrlEncodedData } from 'lib/api';
import { validateLegacyTo<PERSON><PERSON> } from 'lib/REST/helpers/utils';
import { RESTArgs } from 'lib/REST/types/shared';
import { SendEmailData, SendEmailResponse } from 'lib/REST/types/contacts';

sendMultipleEmails.endpoint = '/v2/customer/send-multiple-mail' as const;

export function sendMultipleEmails(args: RESTArgs<{ query: SendEmailData }>) {
    return validateLegacyToJson<SendEmailResponse>(
        fetch(sendMultipleEmails.endpoint, {
            method: 'POST',
            body: withFormUrlEncodedData(args.query),
        })
    );
}
