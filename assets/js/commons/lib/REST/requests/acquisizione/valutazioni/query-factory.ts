import { queryOptions } from '@tanstack/react-query';
import { RESTArgsOf } from 'lib/REST/types/shared';
import { getEstimatesById } from './detail';

const prefix = ['acquisizione', 'valutazioni'];

export const createEstimatesByIdQueryOptions = (args: RESTArgsOf<typeof getEstimatesById>) => {
    return queryOptions({
        queryKey: [...prefix, args?.params.estimateId, 'detail'],
        queryFn: () => getEstimatesById(args),
    });
};
