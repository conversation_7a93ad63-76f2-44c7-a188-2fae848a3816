import { withFormData, withSearchParams } from 'lib/api';
import { validateLegacyToJson } from 'lib/REST/helpers/utils';
import {
    ImportPropertyResponseType,
    OrderingType,
    OutcomeType,
    PropertiesQueryType,
} from 'lib/REST/types/acquisition-privates';
import { Pagination, RESTArgs } from 'lib/REST/types/shared';

getProperties.endpoint = '/acquisizione/annunci-privati/properties' as const;

export function getProperties(
    args: RESTArgs<{ query: Partial<PropertiesQueryType['filters'] & Pagination & OrderingType> }>
) {
    return validateLegacyToJson<PropertiesQueryType>(fetch(withSearchParams(getProperties.endpoint, args?.query)));
}

saveOutcome.endpoint = '/acquisizione/annunci-privati/:propertyId/set-outcome' as const;

export async function saveOutcome({
    propertyId,
    outcome,
}: {
    propertyId: string;
    outcome: OutcomeType;
}): Promise<{ success: boolean }> {
    return validateLegacyToJson<{ success: boolean }>(
        fetch(saveOutcome.endpoint.replace(':propertyId', propertyId), {
            method: 'POST',
            body: withFormData(outcome),
        })
    );
}

importProperty.endpoint = '/acquisizione/annunci-privati/:propertyId/import' as const;

export async function importProperty({ propertyId }: { propertyId: string }): Promise<ImportPropertyResponseType> {
    return validateLegacyToJson<{ success: boolean }>(
        fetch(importProperty.endpoint.replace(':propertyId', propertyId), {
            method: 'POST',
            body: withFormData({ id: propertyId }),
        })
    );
}
