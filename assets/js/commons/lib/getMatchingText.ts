export function getMatchingText(text: string, highlights: Array<string>) {
    if (!highlights || highlights.length === 0) {
        return [
            {
                text: text,
                isMatch: false,
                key: 0,
            },
        ];
    }

    // Create a regex pattern from the highlights array.
    // The 'gi' flags are for global search and case-insensitive search.
    // The parentheses around the highlights ensure that the matched text is captured.
    // Example: highlights = ['hello', 'world'] => /(hello|world)/gi
    const regex = new RegExp(`(${highlights.join('|')})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) => {
        // Check if the part is a match from the highlights array.
        // This is case-insensitive.
        const isMatch = highlights.some((highlight) => highlight.toLowerCase() === part.toLowerCase());

        return {
            text: part,
            isMatch,
            key: index,
        };
    });
}
