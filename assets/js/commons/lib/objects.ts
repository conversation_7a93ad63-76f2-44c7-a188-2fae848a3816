import { convertCase } from './strings-formatter';

/**
 * Converts keys of the input object into snack_case, returning a new object; if array of `keys` is specified, only this keys will be converted and returned into the new object.
 * @param input Original object
 * @param keys optional; keys to be converted and returned
 */
export const convertObjectKeysToSnakeCase = (
    input: Record<string, unknown>,
    keys?: string[]
): Record<string, unknown> => {
    const output = {};
    for (let key in input) {
        if ((keys ?? Object.keys(input)).includes(key)) {
            output[convertCase(key, 'snake')] = input[key];
        }
    }
    return output;
};
