import { SwiperCarousel } from '../../../js/commons/gtx-react/components/SwiperCarousel';
import type { Meta, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';

/**
 * New Carousel component, based on `swiperjs`. Three variants have been developed: `gallery`, `tabs` and `cards`,
 * all of them to be found in the new detail modal.
 *
 * List of items will consists into an array of possible props to be passed to the single-slide component
 * (an `<img />` tag for `gallery`, a `<TabsItem />` for `tabs`, and a new `<SwiperCard />` component for `cards` variant.)
 */
const meta: Meta<typeof SwiperCarousel> = {
    title: 'Components/SwiperCarousel',
    component: SwiperCarousel,
    parameters: {
        componentSubtitle: 'Component',
        componentTitle: 'SwiperCarousel',
        componentSlug: 'swiper-carousel',
        componentImport: 'SwiperCarousel',
        packageName: 'gtx-react/components/SwiperCarousel',
    },
    tags: ['autodocs'],
    decorators: [
        (Story) => (
            <div
                style={{
                    padding: '20px',
                    height: '400px',
                    maxWidth: '800px',
                }}
            >
                <Story />
            </div>
        ),
    ],
    argTypes: {
        variant: {
            control: 'select',
            options: ['gallery', 'tabs', 'cards'],
            description: 'The type of carousel variant to display',
        },
        showFade: {
            control: 'boolean',
            description:
                'Set this to `true` to show a transparent fade below the navigation arrows',
        },
        items: {
            control: false,
            description:
                'Array of items to be displayed in the carousel. Type depends on the variant.',
        },
    },
};

export default meta;

type Story = StoryObj<typeof SwiperCarousel>;

// Sample data for different variants
const galleryItems = [
    {
        src: 'https://picsum.photos/800/400?random=1',
        alt: 'Sample image 1',
    },
    {
        src: 'https://picsum.photos/800/400?random=2',
        alt: 'Sample image 2',
    },
    {
        src: 'https://picsum.photos/800/400?random=3',
        alt: 'Sample image 3',
    },
    {
        src: 'https://picsum.photos/800/400?random=4',
        alt: 'Sample image 4',
    },
    {
        src: 'https://picsum.photos/800/400?random=5',
        alt: 'Sample image 5',
    },
];

const tabsItems = [
    {
        text: 'English',
        active: true,
        startElement: <div className="flag-icon flag-icon-gb" />,
        onClick: action('english-tab-clicked'),
    },
    {
        text: 'Italiano',
        active: false,
        startElement: <div className="flag-icon flag-icon-it" />,
        onClick: action('italian-tab-clicked'),
    },
    {
        text: 'Français',
        active: false,
        startElement: <div className="flag-icon flag-icon-fr" />,
        onClick: action('french-tab-clicked'),
    },
    {
        text: 'Deutsch',
        active: false,
        startElement: <div className="flag-icon flag-icon-de" />,
        onClick: action('german-tab-clicked'),
    },
    {
        text: 'Español',
        active: false,
        startElement: <div className="flag-icon flag-icon-es" />,
        onClick: action('spanish-tab-clicked'),
    },
    {
        text: 'Português',
        active: false,
        startElement: <div className="flag-icon flag-icon-pt" />,
        onClick: action('portuguese-tab-clicked'),
    },
    {
        text: '中文',
        active: false,
        startElement: <div className="flag-icon flag-icon-cn" />,
        onClick: action('chinese-tab-clicked'),
    },
    {
        text: '日本語',
        active: false,
        startElement: <div className="flag-icon flag-icon-jp" />,
        onClick: action('japanese-tab-clicked'),
    },
];

const cardsItems = [
    {
        icon: 'home' as const,
        title: 'Living Room',
        fields: [
            { label: 'Floor', value: 'Ground floor' },
            { label: 'Surface', value: '25 m²' },
            { label: 'Counting at', value: '100%' },
            { label: 'Surface type', value: 'Indoor' },
        ],
    },
    {
        icon: 'bed' as const,
        title: 'Master Bedroom',
        fields: [
            { label: 'Floor', value: 'First floor' },
            { label: 'Surface', value: '20 m²' },
            { label: 'Counting at', value: '100%' },
            { label: 'Surface type', value: 'Indoor' },
        ],
    },
    {
        icon: 'bath' as const,
        title: 'Bathroom',
        fields: [
            { label: 'Floor', value: 'First floor' },
            { label: 'Surface', value: '8 m²' },
            { label: 'Counting at', value: '50%' },
            { label: 'Surface type', value: 'Service' },
        ],
    },
    {
        icon: 'car' as const,
        title: 'Garage',
        fields: [
            { label: 'Floor', value: 'Basement' },
            { label: 'Surface', value: '15 m²' },
            { label: 'Counting at', value: '25%' },
            { label: 'Surface type', value: 'Garage' },
        ],
    },
    {
        icon: 'tree' as const,
        title: 'Garden',
        fields: [
            { label: 'Floor', value: 'Ground floor' },
            { label: 'Surface', value: '50 m²' },
            { label: 'Counting at', value: '10%' },
            { label: 'Surface type', value: 'Outdoor' },
        ],
    },
];

/**
 * Gallery variant displays a carousel of images with pagination and navigation arrows.
 * Perfect for showcasing property photos or any image gallery.
 */
export const Gallery: Story = {
    args: {
        variant: 'gallery',
        items: galleryItems,
        showFade: false,
    },
};

/**
 * Gallery variant with fade effect under the navigation arrows for better visual hierarchy.
 */
export const GalleryWithFade: Story = {
    args: {
        variant: 'gallery',
        items: galleryItems,
        showFade: true,
    },
};

/**
 * Tabs variant displays navigational tabs that can be swiped horizontally.
 * Ideal for language selection or category navigation.
 */
export const Tabs: Story = {
    args: {
        variant: 'tabs',
        items: tabsItems,
        showFade: false,
    },
};

/**
 * Tabs variant with fade effect for enhanced visual appeal.
 */
export const TabsWithFade: Story = {
    args: {
        variant: 'tabs',
        items: tabsItems,
        showFade: true,
    },
};

/**
 * Cards variant displays informational cards that can be swiped horizontally.
 * Perfect for showing property details, room information, or feature summaries.
 */
export const Cards: Story = {
    args: {
        variant: 'cards',
        items: cardsItems,
        showFade: false,
    },
};

/**
 * Cards variant with fade effect for better visual hierarchy.
 */
export const CardsWithFade: Story = {
    args: {
        variant: 'cards',
        items: cardsItems,
        showFade: true,
    },
};

/**
 * Minimal gallery example with just two images.
 */
export const MinimalGallery: Story = {
    args: {
        variant: 'gallery',
        items: galleryItems.slice(0, 2),
        showFade: false,
    },
};

/**
 * Single card example to show how the component behaves with minimal content.
 */
export const SingleCard: Story = {
    args: {
        variant: 'cards',
        items: cardsItems.slice(0, 1),
        showFade: false,
    },
};
