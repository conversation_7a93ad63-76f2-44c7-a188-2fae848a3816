# SwiperCarousel Stories

This directory contains Storybook stories for the `SwiperCarousel` component.

## Component Overview

The `SwiperCarousel` is a React component built on top of `swiperjs` that provides three different carousel variants:

- **Gallery**: For displaying image carousels with pagination and navigation
- **Tabs**: For horizontal scrollable tab navigation
- **Cards**: For displaying informational cards in a carousel format

## Available Stories

### Gallery Variants
- `Gallery`: Basic image carousel with navigation arrows
- `GalleryWithFade`: Image carousel with fade effects under navigation arrows
- `MinimalGallery`: Gallery with just two images

### Tabs Variants
- `Tabs`: Language tabs with flag icons
- `TabsWithFade`: Language tabs with fade effects

### Cards Variants
- `Cards`: Property room information cards
- `CardsWithFade`: Cards with fade effects
- `SingleCard`: Single card example
- `CardsWithMinimalData`: Cards with sparse data to test edge cases

## Usage

Each story demonstrates different configurations and data patterns that can be used with the SwiperCarousel component. The stories include realistic sample data for testing various scenarios.

## Props

- `variant`: 'gallery' | 'tabs' | 'cards' - The type of carousel to display
- `items`: Array of objects specific to each variant type
- `showFade`: boolean - Whether to show fade effects under navigation arrows

For detailed prop types, see the component definition in `SwiperCarousel.tsx`.
