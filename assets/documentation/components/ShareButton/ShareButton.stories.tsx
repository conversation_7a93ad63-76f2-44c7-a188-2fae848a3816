import { ShareButton } from '../../../js/commons/gtx-react/components/ShareButton/ShareButton';
import type { Meta, StoryObj } from '@storybook/react';
import { NotifyProvider } from '@gx-design/snackbar';
import { action } from '@storybook/addon-actions';

/**
 * The ShareButton component provides a dropdown interface for sharing links across multiple platforms.
 * It includes options to copy the link to clipboard, and share via WhatsApp, Messenger, Facebook, X (Twitter), and LinkedIn.
 * The component automatically adds UTM parameters to shared links for tracking purposes.
 */
const meta: Meta<typeof ShareButton> = {
    title: 'Components/ShareButton',
    component: ShareButton,
    parameters: {
        componentSubtitle: 'Component',
        componentTitle: 'ShareButton',
        componentSlug: 'share-button',
        componentImport: 'ShareButton',
        packageName: 'gtx-react/components/ShareButton/ShareButton',
    },
    tags: ['autodocs'],
    decorators: [
        (Story) => (
            <NotifyProvider>
                <div
                    style={{
                        padding: '20px',
                        height: '200px',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                >
                    <Story />
                </div>
            </NotifyProvider>
        ),
    ],
    argTypes: {
        link: {
            control: 'text',
            description: 'The URL to be shared',
        },
        buttonVariant: {
            control: 'select',
            options: ['primary', 'secondary', 'ghost', 'danger'],
            description: 'The visual variant of the dropdown button',
        },
        onDropdownButtonClick: {
            action: 'dropdown-clicked',
            description: 'Callback fired when the dropdown button is clicked',
        },
        onShareItemClick: {
            action: 'share-item-clicked',
            description: 'Callback fired when a sharing option is selected',
        },
        buttonClassName: {
            control: 'text',
            description: 'Additional CSS class for the button',
        },
    },
};

export default meta;

type Story = StoryObj<typeof ShareButton>;

export const Default: Story = {
    args: {
        link: '#',
        onDropdownButtonClick: action('dropdown-button-clicked'),
        onShareItemClick: action('share-item-clicked'),
    },
};

export const GhostVariant: Story = {
    args: {
        link: '#',
        buttonVariant: 'ghost',
        onDropdownButtonClick: action('dropdown-button-clicked'),
        onShareItemClick: action('share-item-clicked'),
    },
};

GhostVariant.storyName = 'Ghost Button Variant';

export const AccentVariant: Story = {
    args: {
        link: '#',
        buttonVariant: 'accent',
        onDropdownButtonClick: action('dropdown-button-clicked'),
        onShareItemClick: action('share-item-clicked'),
    },
};

AccentVariant.storyName = 'Accent Button Variant';

export const ChipVariant: Story = {
    args: {
        link: '#',
        buttonVariant: 'chip',
        onDropdownButtonClick: action('dropdown-button-clicked'),
        onShareItemClick: action('share-item-clicked'),
    },
};

ChipVariant.storyName = 'Chip Button Variant';

export const WithCustomClassName: Story = {
    args: {
        link: '#',
        buttonClassName: 'custom-share-button',
        onDropdownButtonClick: action('dropdown-button-clicked'),
        onShareItemClick: action('share-item-clicked'),
    },
};

WithCustomClassName.storyName = 'With Custom CSS Class';
