import { ListCounter } from '../../../js/commons/gtx-react/components/ListCounter/ListCounter';
import type { Meta, StoryObj } from '@storybook/react';
import { Counter } from '../../../js/commons/types/counters';

/**
 * The ListCounter component displays counter information for properties in list views.
 * It shows match and thread counts with new item indicators, and provides navigation to detailed views.
 * The component handles both enabled and disabled states based on data availability.
 */
const meta: Meta<typeof ListCounter> = {
    title: 'Components/ListCounter',
    component: ListCounter,
    parameters: {
        componentSubtitle: 'Component',
        componentTitle: 'ListCounter',
        componentSlug: 'list-counter',
        componentImport: 'ListCounter',
        packageName: 'gtx-react/components/ListCounter/ListCounter',
    },
    tags: ['autodocs'],
    decorators: [
        (Story) => (
            <div
                style={{
                    padding: '20px',
                    height: '200px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                }}
            >
                <Story />
            </div>
        ),
    ],
    argTypes: {
        type: {
            control: 'select',
            options: ['matches', 'threads'],
            description: 'The type of counter to display',
        },
        data: {
            control: 'object',
            description:
                'Counter data object containing match and thread information',
        },
        disabled: {
            control: 'boolean',
            description: 'Whether the counter is disabled',
        },
        href: {
            control: 'text',
            description: 'URL to navigate to when clicking the counter',
        },
    },
};

export default meta;

type Story = StoryObj<typeof ListCounter>;

// Mock counter data
const mockCounterData: Counter = {
    propertyId: '12345',
    matches: {
        total: 15,
        new: 3,
    },
    threads: {
        total: 8,
        new: 2,
    },
};

const mockCounterDataWithoutNew: Counter = {
    propertyId: '12345',
    matches: {
        total: 10,
        new: 0,
    },
    threads: {
        total: 5,
        new: 0,
    },
};

const mockCounterDataEmpty: Counter = {
    propertyId: '12345',
    matches: {
        total: 0,
        new: 0,
    },
    threads: {
        total: 0,
        new: 0,
    },
};

export const MatchesWithNew: Story = {
    args: {
        type: 'matches',
        data: mockCounterData,
        href: '/clienti/match?search=12345',
    },
};

MatchesWithNew.storyName = 'Matches Counter with New Items';

export const MatchesWithoutNew: Story = {
    args: {
        type: 'matches',
        data: mockCounterDataWithoutNew,
        href: '/clienti/match?search=12345',
    },
};

MatchesWithoutNew.storyName = 'Matches Counter without New Items';

export const ThreadsWithNew: Story = {
    args: {
        type: 'threads',
        data: mockCounterData,
        href: '/messaggi/lista?code=12345',
    },
};

ThreadsWithNew.storyName = 'Threads Counter with New Items';

export const ThreadsWithoutNew: Story = {
    args: {
        type: 'threads',
        data: mockCounterDataWithoutNew,
        href: '/messaggi/lista?code=12345',
    },
};

ThreadsWithoutNew.storyName = 'Threads Counter without New Items';

export const DisabledState: Story = {
    args: {
        disabled: true,
    },
};

DisabledState.storyName = 'Disabled Counter';

export const EmptyData: Story = {
    args: {
        type: 'matches',
        data: mockCounterDataEmpty,
        href: '/clienti/match?search=12345',
    },
};

EmptyData.storyName = 'Empty Counter Data';

export const NoData: Story = {
    args: {
        type: 'matches',
        data: undefined,
        href: '/clienti/match?search=12345',
    },
};

NoData.storyName = 'No Counter Data';

export const WithoutHref: Story = {
    args: {
        type: 'matches',
        data: mockCounterData,
    },
};

WithoutHref.storyName = 'Counter without Navigation Link';

export const HighNumbers: Story = {
    args: {
        type: 'matches',
        data: {
            propertyId: '12345',
            matches: {
                total: 999,
                new: 99,
            },
            threads: {
                total: 888,
                new: 88,
            },
        },
        href: '/clienti/match?search=12345',
    },
};

HighNumbers.storyName = 'Counter with High Numbers';

export const InteractiveExample: Story = {
    render: (args) => {
        const sampleData: Counter = {
            propertyId: '12345',
            matches: {
                total: 25,
                new: 5,
            },
            threads: {
                total: 12,
                new: 3,
            },
        };

        return (
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '20px',
                    alignItems: 'center',
                    height: '100%',
                }}
            >
                <h3>Interactive ListCounter Examples</h3>
                <p style={{ textAlign: 'center', maxWidth: '600px' }}>
                    ListCounter components show match and thread counts for
                    properties. When there are new items, a notification dot
                    appears. Disabled counters show when no data is available.
                </p>

                <div
                    style={{
                        display: 'flex',
                        gap: '20px',
                        alignItems: 'center',
                    }}
                >
                    <div style={{ textAlign: 'center' }}>
                        <div
                            style={{
                                marginBottom: '8px',
                                fontSize: '14px',
                                fontWeight: 'bold',
                            }}
                        >
                            Matches (with new)
                        </div>
                        <ListCounter
                            type="matches"
                            data={sampleData}
                            href="/clienti/match?search=12345"
                        />
                    </div>

                    <div style={{ textAlign: 'center' }}>
                        <div
                            style={{
                                marginBottom: '8px',
                                fontSize: '14px',
                                fontWeight: 'bold',
                            }}
                        >
                            Threads (with new)
                        </div>
                        <ListCounter
                            type="threads"
                            data={sampleData}
                            href="/messaggi/lista?code=12345"
                        />
                    </div>

                    <div style={{ textAlign: 'center' }}>
                        <div
                            style={{
                                marginBottom: '8px',
                                fontSize: '14px',
                                fontWeight: 'bold',
                            }}
                        >
                            Disabled
                        </div>
                        <ListCounter disabled />
                    </div>
                </div>

                <div
                    style={{
                        fontSize: '14px',
                        color: '#666',
                        textAlign: 'center',
                        maxWidth: '600px',
                    }}
                >
                    <strong>Features demonstrated:</strong>
                    <ol style={{ textAlign: 'left' }}>
                        <li>Red notification dot when there are new items</li>
                        <li>
                            Shows new count when available, total count
                            otherwise
                        </li>
                        <li>
                            Clickable when href is provided and total count{' '}
                            {'>'} 0
                        </li>
                        <li>
                            Disabled state when no data or disabled prop is true
                        </li>
                        <li>Responsive sizing based on screen size</li>
                        <li>Icon-only button styling</li>
                    </ol>
                </div>
            </div>
        );
    },
};

InteractiveExample.storyName = 'Interactive Example';
