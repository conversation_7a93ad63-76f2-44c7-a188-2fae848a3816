import { PerformanceBadge } from '../../../js/commons/gtx-react/components/PerformanceBadge/PerformanceBadge';
import type { Meta, StoryObj } from '@storybook/react';

/**
 * Modern-looking badge introduced in Performance section.
 * Can be colored in green/yellow/red indicating an high/medium/low performance.
 * Its content can be specified via props or children.
 *
 * The badge supports two ways of specifying content:
 * 1. Via props: `boldContent` (optional) and `normalContent` (required)
 * 2. Via children: Any React content can be passed as children
 */
const meta: Meta<typeof PerformanceBadge> = {
    title: 'Components/PerformanceBadge',
    component: PerformanceBadge,
    parameters: {
        componentSubtitle: 'Component',
        componentTitle: 'PerformanceBadge',
        componentSlug: 'performance-badge',
        componentImport: 'PerformanceBadge',
        packageName: 'gtx-react/components/PerformanceBadge/PerformanceBadge',
    },
    tags: ['autodocs'],
    decorators: [
        (Story) => (
            <div
                style={{
                    padding: '20px',
                    display: 'flex',
                    gap: '16px',
                    flexWrap: 'wrap',
                    alignItems: 'center',
                }}
            >
                <Story />
            </div>
        ),
    ],
    argTypes: {
        performance: {
            control: 'select',
            options: ['high', 'medium', 'low'],
            description:
                'Makes the badge colored green/yellow/red respectively',
        },
        large: {
            control: 'boolean',
            description:
                'If `true`, it will have a height of 36px and bigger text inside it',
        },
        boldContent: {
            control: 'text',
            description: 'Text shown on the left, bigger and bold (optional)',
        },
        normalContent: {
            control: 'text',
            description:
                'Normal text content, required if there is no children',
        },
        className: {
            control: 'text',
            description: 'Custom className to be applied on the wrapper-div',
        },
        children: {
            control: false,
            description:
                'Custom content rendered inside the badge. Not allowed when using props `normalContent` and `boldContent`',
        },
    },
};

export default meta;

type Story = StoryObj<typeof PerformanceBadge>;

/**
 * High performance badge with bold and normal content.
 * Green colored badge indicating excellent performance.
 */
export const HighPerformance: Story = {
    args: {
        performance: 'high',
        boldContent: '95%',
        normalContent: 'Performance Score',
        large: false,
    },
};

/**
 * Medium performance badge with bold and normal content.
 * Yellow colored badge indicating average performance.
 */
export const MediumPerformance: Story = {
    args: {
        performance: 'medium',
        boldContent: '65%',
        normalContent: 'Performance Score',
        large: false,
    },
};

/**
 * Low performance badge with bold and normal content.
 * Red colored badge indicating poor performance.
 */
export const LowPerformance: Story = {
    args: {
        performance: 'low',
        boldContent: '25%',
        normalContent: 'Performance Score',
        large: false,
    },
};

/**
 * Large high performance badge.
 * Bigger size variant with 36px height and larger text.
 */
export const LargeHighPerformance: Story = {
    args: {
        performance: 'high',
        boldContent: '98%',
        normalContent: 'Excellent Rating',
        large: true,
    },
};

/**
 * Large medium performance badge.
 * Bigger size variant with yellow coloring.
 */
export const LargeMediumPerformance: Story = {
    args: {
        performance: 'medium',
        boldContent: '72%',
        normalContent: 'Good Rating',
        large: true,
    },
};

/**
 * Large low performance badge.
 * Bigger size variant with red coloring.
 */
export const LargeLowPerformance: Story = {
    args: {
        performance: 'low',
        boldContent: '18%',
        normalContent: 'Needs Improvement',
        large: true,
    },
};

/**
 * Badge with only normal content (no bold text).
 * Shows how the badge looks without the bold content.
 */
export const OnlyNormalContent: Story = {
    args: {
        performance: 'high',
        normalContent: 'Top Performer',
        large: false,
    },
};

/**
 * Large badge with only normal content.
 * Large variant without bold text.
 */
export const LargeOnlyNormalContent: Story = {
    args: {
        performance: 'medium',
        normalContent: 'Average Performance',
        large: true,
    },
};

/**
 * Badge with custom children content.
 * Demonstrates using children instead of props for content.
 */
export const WithCustomChildren: Story = {
    args: {
        performance: 'high',
        large: false,
    },
    render: (args) => (
        <PerformanceBadge
            performance={args.performance}
            large={args.large}
            className={args.className}
        >
            <span>🏆 Best in Category</span>
        </PerformanceBadge>
    ),
};

/**
 * Large badge with complex children content.
 * Shows how to use children with icons and complex markup.
 */
export const LargeWithCustomChildren: Story = {
    args: {
        performance: 'medium',
        large: true,
    },
    render: (args) => (
        <PerformanceBadge
            performance={args.performance}
            large={args.large}
            className={args.className}
        >
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <span>⭐</span>
                <strong>4.2</strong>
                <span>/5</span>
            </div>
        </PerformanceBadge>
    ),
};

/**
 * Badge with custom className.
 * Demonstrates applying custom CSS classes.
 */
export const WithCustomClassName: Story = {
    args: {
        performance: 'low',
        boldContent: '15%',
        normalContent: 'Custom Styled',
        className: 'my-custom-badge',
        large: false,
    },
};

/**
 * Showcase of all performance levels.
 * Displays all three performance levels side by side for comparison.
 */
export const AllPerformanceLevels: Story = {
    render: () => (
        <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
            <PerformanceBadge
                performance="high"
                boldContent="95%"
                normalContent="High"
            />
            <PerformanceBadge
                performance="medium"
                boldContent="65%"
                normalContent="Medium"
            />
            <PerformanceBadge
                performance="low"
                boldContent="25%"
                normalContent="Low"
            />
        </div>
    ),
};

/**
 * Showcase of all sizes.
 * Displays regular and large sizes for comparison.
 */
export const AllSizes: Story = {
    render: () => (
        <div
            style={{
                display: 'flex',
                gap: '16px',
                flexWrap: 'wrap',
                alignItems: 'center',
            }}
        >
            <PerformanceBadge
                performance="high"
                boldContent="95%"
                normalContent="Regular"
            />
            <PerformanceBadge
                performance="high"
                boldContent="95%"
                normalContent="Large"
                large
            />
        </div>
    ),
};

/**
 * Real-world property performance examples.
 * Shows realistic usage scenarios for property performance metrics.
 */
export const PropertyPerformanceExamples: Story = {
    render: () => (
        <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
            <PerformanceBadge
                performance="high"
                boldContent="142"
                normalContent="Views today"
            />
            <PerformanceBadge
                performance="medium"
                boldContent="8"
                normalContent="Contacts"
            />
            <PerformanceBadge
                performance="low"
                boldContent="2%"
                normalContent="Conversion rate"
            />
            <PerformanceBadge
                performance="high"
                boldContent="€450K"
                normalContent="Market value"
                large
            />
        </div>
    ),
};
