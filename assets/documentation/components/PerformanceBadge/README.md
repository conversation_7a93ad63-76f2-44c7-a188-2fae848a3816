# PerformanceBadge Stories

This directory contains Storybook stories for the `PerformanceBadge` component.

## Component Overview

The `PerformanceBadge` is a modern-looking badge component introduced in the Performance section. It provides visual indicators for performance levels with color-coded styling and flexible content options.

## Features

- **Performance Levels**: Three color-coded performance levels
  - `high` - Green colored badge for excellent performance
  - `medium` - Yellow colored badge for average performance  
  - `low` - Red colored badge for poor performance

- **Size Variants**: 
  - Regular size (default)
  - Large size (36px height with bigger text)

- **Content Options**:
  - **Props-based**: Use `boldContent` (optional) and `normalContent` (required)
  - **Children-based**: Pass any React content as children

## Available Stories

### Basic Performance Levels
- `HighPerformance`: Green badge with 95% performance score
- `MediumPerformance`: Yellow badge with 65% performance score
- `LowPerformance`: Red badge with 25% performance score

### Large Size Variants
- `LargeHighPerformance`: Large green badge with excellent rating
- `LargeMediumPerformance`: Large yellow badge with good rating
- `LargeLowPerformance`: Large red badge needing improvement

### Content Variations
- `OnlyNormalContent`: Badge without bold text
- `LargeOnlyNormalContent`: Large badge without bold text
- `WithCustomChildren`: Badge using children for custom content
- `LargeWithCustomChildren`: Large badge with complex children markup

### Customization
- `WithCustomClassName`: Badge with custom CSS classes

### Showcase Stories
- `AllPerformanceLevels`: Side-by-side comparison of all performance levels
- `AllSizes`: Comparison of regular vs large sizes
- `PropertyPerformanceExamples`: Real-world usage examples with property metrics

## Usage Examples

### Props-based content:
```tsx
<PerformanceBadge 
  performance="high" 
  boldContent="95%" 
  normalContent="Performance Score" 
/>
```

### Children-based content:
```tsx
<PerformanceBadge performance="high">
  <span>🏆 Best in Category</span>
</PerformanceBadge>
```

## Props

- `performance`: 'high' | 'medium' | 'low' - Performance level determining color
- `large`: boolean - Whether to use large size variant (36px height)
- `boldContent`: string - Bold text content (only with props-based content)
- `normalContent`: string - Normal text content (required with props-based content)
- `children`: ReactNode - Custom content (cannot be used with boldContent/normalContent)
- `className`: string - Custom CSS classes

For detailed prop types, see the component definition in `PerformanceBadge.tsx`.
