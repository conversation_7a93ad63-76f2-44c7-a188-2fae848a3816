@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$crm-autocomplete: 'crm-autocomplete';

.#{$crm-autocomplete} {
  position: relative;
  display: inline-flex;
  flex-direction: column;

  &__input {
    @include typography(body-small);
    display: flex;
    align-items: center;
    width: 30rem;
    height: 3.2rem;
    padding-left: space(sm);
    padding-right: 3.6rem;
    background-color: color(background-alt);
    border: 1px solid color(background-alt);
    border-radius: radius(md);
    color: color(content-medium);
    outline: none;
    transition: all 0.3s ease-in-out;

    &:hover {
      border-color: color(border-selected);
    }

    &:focus {
      background-color: color(background-main);
      border-color: color(border-selected);
      color: color(conten-high);
    }

    &.is-selected {
      background-color: color(background-selected);
      border-color: color(background-selected);
      color: color(content-selected);
    }
  }

  &__loading {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: space(sm);

    svg {
      @include icon-size(sm);
      color: color(background-brand);
    }
  }

  &__iconSearch {
    @include icon-size(sm);
    position: absolute;
    top: 0.6rem;
    right: 0.6rem;
    transform: scale(1);
    opacity: 1;
    transition: all 0.3s ease-in-out;
    color: color(content-medium);

    &.is-hidden {
      transform: scale(0);
      opacity: 0;
    }
  }

  &__reset {
    @include icon-size(xs);
    position: absolute;
    top: 0.6rem;
    right: 0.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    width: 2rem;
    height: 2rem;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transform: scale(1);
    opacity: 1;
    transition: all 0.3s ease-in-out;
    color: color(content-high);

    .#{$crm-autocomplete}__input.is-selected ~ & {
      color: color(content-action);
    }

    &.is-hidden {
      transform: scale(0);
      opacity: 0;
    }

    svg {
      flex-shrink: 0;
    }

    .#{$crm-autocomplete}:focus & {
      color: color(content-high);
    }

    .#{$crm-autocomplete}.is-selected & {
      color: color(content-selected);
    }
  }

  &__options {
    position: absolute;
    top: calc(100% + #{space(xs)});
    overflow: hidden;
    min-width: 100%;
    max-height: calc(100vh - 28rem);
    overflow-y: auto;
    padding: space(sm) 0;
    border-radius: radius(md);
    box-shadow: elevation(fixed-top);
    display: none;
    background-color: color(background-main);
    @include z-index(dropdown);

    &.is-open {
      display: block;
      min-height: 4.8rem;
    }

    @include media('>=#{breakpoint(sm)}') {
      min-width: 44rem;
    }

    li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: space(sm);
      height: 4.8rem;
      padding: 0 space(lg);
      background-color: color(background-main);
      font-size: 1.4rem;
      cursor: pointer;

      &:hover,
      &[aria-selected='true'] {
        background-color: color(background-alt);
      }

      svg {
        @include icon-size(sm);
        flex-shrink: 0;
      }
    }
  }

  &__optionsText {
    margin-right: auto;
  }

  &__enterIcon {
    color: color(content-action);
  }

  &__enterText {
    color: color(content-action);
    text-transform: uppercase;
    font-weight: bold;
  }
}
