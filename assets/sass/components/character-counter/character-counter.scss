// ==========================================================================
// Character counter - Components
// ==========================================================================
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$counter-colors: (
  positive: 'content-success',
  negative: 'content-error',
);

.gx-character-counter {
  display: flex;
  justify-content: flex-end;
  margin-top: space(sm);
  @include typography(body-tiny);
  color: color(content-low);

  @each $counter-color, $color in $counter-colors {
    &--#{$counter-color} {
      color: color($color);
    }
  }
}
