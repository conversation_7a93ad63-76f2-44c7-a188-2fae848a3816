@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.popover-help {
  position: relative;
  display: inline-block;
  vertical-align: top;
  margin-left: 0.5rem;
  top: -0.1rem;

  .popover {
    width: 90%;
    width: 33rem;
  }
}

.popover-help__btn {
  width: ($gx-unit-size * 3);
  height: ($gx-unit-size * 3);
  padding: 0;
  border-radius: radius(rounded);
  background: transparent;
  border: none;
  font-size: ($gx-unit-size * 3);
  line-height: 0;
  color: color(content-info);
  outline: none;
  font-weight: normal;
}

.popover {
  padding: 0;
  border: none;
  border-radius: 0;
  box-shadow: elevation(raised);

  &-title {
    padding: 1rem 2rem;
    background-color: color(background-main);
    border-bottom-color: color(border-main);
  }

  &.size-sm {
    max-width: 17rem;
  }

  &.size-md {
    max-width: 33rem;
  }

  &.info-message,
  &.alert-message {
    text-align: center;
    padding: 0.3rem;

    .popover-title {
      border-bottom: none;
      background-color: color(content-accent);
      font-weight: 600;
      padding-bottom: 0;
    }
  }

  &.alert-message {
    .popover-title {
      color: color(content-error);
    }
  }

  &-content {
    padding: 2rem;

    ul {
      list-style: disc;
      list-style-position: outside;
      margin-left: space(xl);

      li + li {
        margin-top: space(xs);
      }
    }

    a {
      font-weight: bold;
      cursor: pointer;
    }

    > * + a {
      margin-left: space(xs);
    }
  }

  &.foto-preview {
    max-width: 50rem;

    .popover-title {
      color: color(content-medium);
      font-weight: normal;
    }

    .popover-content {
      padding: 0;

      .loader-box {
        position: relative;
        width: 40rem;
        height: 15rem;

        .gx-loader {
          position: absolute;
          top: 5rem;
          left: 18rem;
        }
      }

      .gtx-gallery-img {
        display: table;
        float: left;
        margin: 0.6rem;
        margin-left: 0;
        width: 20rem;
        height: 15rem;
        text-align: center;
        background: #333333;

        > .img-wrap {
          display: table-cell;
          vertical-align: middle;
          text-align: center;

          > img {
            max-width: 20rem;
            max-height: 15rem;
          }
        }

        &:first-child {
          margin-left: 0.6rem;
        }
      }
    }
  }

  &.zone-map {
    max-width: 40rem;

    .gtx-req-preview-zone-map {
      width: inherit;
      min-width: 16rem;
      height: 15rem;

      img {
        width: 15rem;
        height: 15rem;
      }
    }

    .gtx-req-preview-zone-list {
      overflow: auto;
      max-width: 25rem;

      ul {
        padding-left: 1.7rem;
        padding-right: 1.1rem;
      }

      .text-info {
        padding-left: 1.7rem;
      }
    }
  }
}
