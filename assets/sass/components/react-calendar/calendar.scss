@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$react-calendar: react-calendar;

.#{$react-calendar} {
  width: 32rem;
  max-width: 100%;
  padding: space(md);
  border-radius: radius(md);
  background: color(background-main);
  box-shadow: elevation(raised);
  overflow: hidden;

  .react-calendar-range-wrapper & {
    padding-top: space(sm);
    box-shadow: none;
  }

  button {
    border: none;
  }

  abbr {
    text-decoration: none;
  }

  &--doubleView {
    width: 64rem;

    .#{$react-calendar}__viewContainer {
      display: flex;
      margin: -0.5em;

      > * {
        width: 50%;
        margin: 0.5em;
      }
    }
  }

  &__navigation {
    display: flex;
    margin-bottom: space(md);

    button {
      padding: 0;
      background: none;
      cursor: pointer;

      &:disabled {
        cursor: default;
      }
    }

    &__arrow:disabled {
      .gx-button {
        opacity: 0.65;
        cursor: default;

        &:hover {
          background-image: none;
        }
      }
    }

    &__label {
      @include typography(body-small);
      color: color(content-medium);
      font-weight: bold;
    }
  }

  &__tile {
    max-width: 100%;
    padding: space(sm);
    background: color(background-selectable);
    color: color(content-selectable);
    text-align: center;
    cursor: pointer;

    &:disabled {
      opacity: 0.65;
      cursor: default;

      &:hover,
      &:focus {
        background: color(background-selectable);
        background-color: none;
        background-image: none;
        color: color(content-selectable);
      }
    }

    &:hover,
    &--active {
      background-color: color(background-selected);
      color: color(content-selected);
    }

    &--hover {
      border-radius: 0;

      &:first-child {
        border-top-left-radius: radius(md);
        border-bottom-left-radius: radius(md);
      }
      &:last-child {
        border-top-right-radius: radius(md);
        border-bottom-right-radius: radius(md);
      }
    }

    &--hasActive,
    &--rangeStart,
    &--rangeEnd,
    &--hasActive:hover,
    &--rangeStart:hover,
    &--rangeEnd:hover {
      background-color: color(background-selected-high);
      color: color(content-selected-high);
    }

    &--rangeStart {
      border-top-left-radius: radius(md);
      border-bottom-left-radius: radius(md);
    }
    &--rangeEnd {
      border-top-right-radius: radius(md);
      border-bottom-right-radius: radius(md);
    }

    &:hover:not(&--range),
    &--rangeBothEnds {
      border-radius: radius(md);
    }
  }

  &__month-view {
    &__weekdays {
      color: color(content-low);
      font-weight: bold;
      text-align: center;
      @include typography(body-small);

      &__weekday {
        padding: space(xs);
      }
    }

    &__weekNumbers .react-calendar__tile {
      display: flex;
      align-items: center;
      justify-content: center;
      color: color(content-low);
      font-size: 0.75em;
      font-weight: bold;

      &:hover {
        background-color: color(background-main);
        color: color(content-low);
        cursor: default;
      }
    }

    &__days__day--neighboringMonth {
      color: color(content-low);
    }
  }

  &-range {
    &__arrow {
      display: block;
      position: absolute;
      left: space(2xl);
      width: ($gx-unit-size * 2);
      height: ($gx-unit-size * 2);
      transform: rotate(-135deg) translateY(-50%);
      transform-origin: top;
      transition: 0.3s left ease-in-out;
      background-color: color(background-main);
      box-shadow: elevation(raised);
    }

    &::before {
      position: absolute;
      left: 0;
      width: 100%;
      height: ($gx-unit-size * 2);
      border-radius: radius(md);
      background-color: color(background-main);
      content: '';
      @include z-index(base, 1);
    }

    &:has(.starts-from-bottom)::before {
      top: 0;
    }
    &:has(.starts-from-top)::before {
      bottom: 0;
    }
  }
}
