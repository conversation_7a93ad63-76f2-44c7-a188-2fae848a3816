@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.gx-form-group {
  display: flex;

  > * {
    flex-grow: 1;

    & + * {
      margin-left: space(md);
    }
  }
}

.gx-form-section {
  & + & {
    padding-top: space(lg);
    border-top: 0.1rem solid color(border-main);
  }
}

.form-group-col2 {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  .filter-box__section__item {
    cursor: pointer;
    width: 100%;
    margin-bottom: space(lg);

    & + .filter-box__section__item {
      margin-top: 0;
    }

    @include media('screen', '>=#{breakpoint(sm)}') {
      width: calc(50% - #{$gx-unit-size * 2});
    }

    @include media('screen', '>=#{breakpoint(md)}') {
      & + .filter-box__section__item {
        margin-left: 0;
      }
    }
  }
}

.styled-checkbox {
  position: relative;
  input {
    display: none;
  }

  input:checked + .styled-check,
  &.check-replica .styled-check {
    i {
      position: relative;
      &:before {
        display: inline-block;
      }
    }

    &.check-primary {
      background-color: color(background-brand);
      border-color: color(background-brand);
      color: #ffffff;
    }
  }

  label {
    font-weight: normal;
    display: inline-block;
    vertical-align: top;
    position: relative;
    padding: 0;
    margin: 0;
    cursor: pointer;
  }

  .styled-check {
    + span {
      display: inline-block;
      vertical-align: top;
      position: relative;
      padding: 0;
      margin: 0;
      cursor: pointer;
      margin-left: 1.8em;
    }
    i {
      text-align: center;
      vertical-align: top;
      font-size: 100%;
      line-height: 142%;
      margin-top: -1px;
      width: 100%;
      display: inline-block;
      &:before {
        display: none;
      }
    }
  }
  .styled-check {
    position: absolute;
    display: inline-block;
    vertical-align: middle;
    width: 1.3em;
    height: 1.3em;
    background-color: #ffffff;
    border: 1px solid color(border-main);
    border-radius: radius(sm);
    cursor: pointer;
    &.check-single {
      position: relative;
    }

    &.nobox-check {
      background: none !important;
      border-color: transparent !important;
      i {
        font-size: 1.2em;
        line-height: 115%;
      }
      &.pull-right {
        position: relative;
        & + span {
          margin-left: 0;
          margin-right: 0.5em;
        }
      }
    }

    &.round-check {
      border-radius: 50%;
      i {
        font-size: 90%;
        line-height: 1.6em;
      }
    }
  }

  &.form-control {
    box-shadow: none;
    label {
      display: block;
      height: 100%;
    }

    .styled-check {
      + span {
        margin-left: 1.8em;
      }
      &.pull-right {
        right: 0;
        top: 3%;
        + span {
          margin-left: 0;
        }
      }
    }
  }

  &.disabled {
    opacity: 0.65;
    cursor: not-allowed;
    label,
    span,
    .styled-check {
      cursor: not-allowed;
    }
  }
  &.lead {
    margin-bottom: 0;
  }

  &.check-replica {
    label {
      cursor: auto;
    }
    .styled-check {
      &,
      + span {
        cursor: auto;
      }
    }
  }
}

.styled-checkbox {
  .gx-checkbox-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
  }
}

.styled-checkbox .styled-check {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.7rem;
  height: 1.7rem;

  &.pull-right {
    top: 14.5%;
  }

  .gx-icon {
    display: none;
  }

  &.gx-checkbox--checked {
    background-color: color(background-brand);
    color: color(content-accent);
    border-color: color(border-selected);

    .gx-icon {
      display: block;
      font-size: 2rem;
    }
  }
}

.styled-checkbox .styled-check .gx-icon {
  display: none;
}

// TODO: da rivedere una volta sostituiti tutti i checkbox
.styled-checkbox input:checked + .styled-check,
.styled-checkbox.check-replica .styled-check {
  background-color: color(background-brand);
  border-color: color(border-selected);

  .gx-icon {
    display: inline-block;
    color: color(content-accent);
    @include icon-size(sm);
  }

  .fa {
    color: color(content-accent);
    line-height: 1;
  }
}

.styled-checkbox input:checked + .styled-check.check-primary,
.styled-checkbox.check-replica .styled-check.check-primary {
  background-color: color(background-brand);
  color: color(content-accent);
  border-color: color(border-selected);

  .gx-icon {
    display: block;
    @include icon-size(sm);
  }
}

.styled-checkbox.form-control .styled-check.pull-right {
  top: 17%;
}

.styled-check {
  svg {
    fill: color(content-accent);
  }
}
