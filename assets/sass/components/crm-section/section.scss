@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.crm-section {
  display: flex;
  height: 100%;

  &__content {
    position: relative;
    flex: 1 1 0%;
    overflow-y: auto;
    transition: margin 0.3s ease-in-out;
    
    .crm-sidebar + & {
      margin-left: 28rem;
    }

    &:has(+ .crm-sidebar) {
      margin-right: 28rem;
    }

    &Header {
      position: sticky;
      left: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 5.6rem;
      padding: 0 space(lg);

      > div {
        display: flex;
        align-items: center;
        gap: space(sm);

        .gx-button + .gx-button {
          margin-left: 0;
        }
      }
    }
    .section-properties-list & {
      padding-bottom: space(4xl);
    }

    &List {
      padding: 0 space(lg);
    }

    .gx-paginationBar {
      position: sticky;
      left: 0;
      margin-top: space(lg);
    }

    &EmptyState {
      padding: 0 space(lg);
      position: sticky;
      left: 0;

      .gx-empty-state {
        max-width: 35rem;
        text-align: center;
        margin: 0 auto;
       
      }
    }
  }

  &__backButton {
    .crm-sidebar__openButton.is-closed + & {
      display: flex;
      align-items: center;
      gap: space(sm);

      &::before {
        content: '';
        height: 2.4rem;
        border-left: 1px solid color(border-main);
      }
    }
  }
}
