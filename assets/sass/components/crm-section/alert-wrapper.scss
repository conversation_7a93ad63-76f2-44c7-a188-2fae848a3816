@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.crm-alert-wrapper {
  padding: 0 space(lg) space(md);
  position: sticky;
  left: 0;
}

.activation-error-item {
  margin-top: space(md);

  ul {
    margin-top: space(sm);
  }

  li {
    list-style: disc;
    list-style-position: inside;

    & + li {
      margin-top: space(xs);
    }
  }
}
