@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$padding-header-sm: 14rem;
$padding-header-md: 31rem;
$spacing-header-btn: 4rem;
$bg-images: adlisting, agenda, auctions-catalogue, customers, estimates, managedrequests, projects, sales-requests,
  web-services;

//MODULE-ACTIVATION HEADER
.module-activation-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: 100%;
  padding: space(2xl) space(md) $padding-header-sm;
  background-color: #e0ebf1;
  background-image: url('/bundles/base/getrix/common/img/module-activation/getrix/background.png');
  background-position: center bottom;
  background-repeat: no-repeat;
  background-size: 81.6rem;
  border-bottom: 0.3rem solid #afc9cf;

  &__title {
    max-width: 128rem;
    margin-bottom: space(sm);
    @include typography(title-1);
  }

  p {
    max-width: 128rem;
    margin-bottom: 0;
    @include typography(body);
  }

  .gx-button {
    margin-top: space(lg);
  }

  @each $value in $bg-images {
    &--#{$value} {
      background-image: url('/bundles/base/getrix/common/img/module-activation/#{$value}/background.png');
    }
  }

  &--with-bg {
    padding: space(2xl) space(md) 22rem;
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    padding-bottom: $padding-header-md;
    background-size: 130rem;

    &__title {
      @include typography(display-2);
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    padding-top: space(2xl);
  }
}

///////////////////

//MODULE-ACTIVATION IMAGE
.module-activation-img {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin-top: -($padding-header-sm - $spacing-header-btn);
  padding: 0 space(md);

  img {
    width: 100%;
    max-width: 50rem;
  }

  &--shadow {
    img {
      box-shadow: (color(border-main)) 0 ($gx-unit-size) ($gx-unit-size + 4) ($gx-unit-size / 2);
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    margin-top: -($padding-header-md - $spacing-header-btn);
  }
}

///////////////////

//MODULE-ACTIVATION GETRIX
.module-activation-getrix {
  padding: 0 space(xl);
  margin: 0 auto;
  text-align: center;

  &__logo {
    width: 13rem;
    height: 4rem;
    margin: space(2xl) auto space(md);
    background-image: url('/bundles/base/getrix/common/img/country/it/logo-gtx/logo.svg');
    background-size: cover;
    text-indent: -999rem;
  }
}

///////////////////

//MODULE-ACTIVATION FEATURE
.module-activation-feature {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: center;
  max-width: 128rem;
  margin: space(2xl) auto 0;

  &__item {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
    max-width: 36rem;
    padding: space(xl);

    p {
      @include typography(body);
    }

    h4 {
      @include typography(display-subtitle);
    }
  }

  &__icon {
    width: ($gx-unit-size * 9);
    height: ($gx-unit-size * 9);
    margin: 0 auto space(md);
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    margin-top: space(2xl);
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    flex-direction: row;

    &__item {
      width: calc(50% - #{$gx-unit-size * 2});
      padding-bottom: space(2xl);
    }
  }

  @include media('screen', '<#{breakpoint(md)}') {
    align-items: center;
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    margin-top: space(2xl);
    flex-direction: row;

    &__item {
      width: calc(33% - #{space(md)});
    }
  }

  @include media('screen', '>=#{breakpoint(lg)}') {
    &__item {
      padding-bottom: space(2xl);
    }
  }
}

///////////////////

//MODULE-ACTIVATION FORM
.module-activation-contact {
  display: flex;
  flex-direction: column;
  max-width: 63.2rem;
  padding: space(2xl) space(md);
  margin-left: auto;
  margin-right: auto;

  .title {
    margin-bottom: space(xl);
  }

  &__box {
    &+& {
      margin-top: space(xl);
    }

    &Title {
      margin-bottom: space(md);
    }

    &Phone {
      margin-bottom: space(md);
      @include typography(body-small);

      svg {
        margin-right: space(xs);
        color: color(content-action);
        @include icon-size(md);
      }
    }

    &Time {
      @include typography(body-tiny);
      color: color(content-low);
    }

    &ChangeUser {
      display: inline-block;
      margin-bottom: space(md);
      cursor: pointer;
    }
  }

  &--subtitle {
    max-width: 28rem;
    padding-bottom: space(xl);
    margin: auto;
    text-align: center;

    & .tel {
      white-space: nowrap;
    }
  }

  &__row {
    position: relative;
    flex-wrap: wrap;

    &+& {
      margin-top: space(lg);
    }

    &.hidden+& {
      margin-top: 0;
    }
  }

  &__field {

    input,
    textarea {
      width: 100%;
      height: ($gx-unit-size * 5);
      padding: 0 space(md);
      font-size: space(md);
      border: 0.1rem solid color(border-main);
      border-radius: radius(sm);
    }

    textarea {
      height: ($gx-unit-size * 10);
      padding-top: space(sm);
      padding-bottom: space(sm);
    }

    @include media('screen', '<#{breakpoint(sm)}') {
      &+& {
        margin-top: space(lg);
      }
    }
  }

  &__icon {
    position: absolute;
    top: 1rem;
    left: -(space(2xl));
    font-size: 2rem;
    color: color(content-low);
  }

  &__checkbox--text-padding {
    padding-left: space(xl);
  }

  &__submit {
    margin-top: space(md);

    @include media('screen', '<#{breakpoint(sm)}') {
      width: 100%;
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    padding-top: space(2xl);

    .title {
      width: 100%;
    }

    &__box {
      &+& {
        margin-top: 0;
      }

      &--call {
        width: calc(30% - #{$gx-unit-size * 2});
      }

      &--form {
        width: calc(70% - #{$gx-unit-size * 2});
      }
    }

    &__row {
      &--group {
        display: flex;
        justify-content: space-between;

        .module-activation-contact__field {
          width: calc(50% - #{$gx-unit-size});

          &+.module-activation-contact__field {
            margin-top: 0;
          }
        }
      }
    }

    &--subtitle {
      max-width: 34rem;
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    padding-top: space(2xl);

    &--subtitle {
      max-width: 32rem;
    }
  }

  @include media('screen', '>=#{breakpoint(lg)}') {
    padding-top: space(2xl);

    &--subtitle {
      max-width: 40.8rem;
    }

    &__row {
      &--group {
        .module-activation-form__field {
          width: calc(50% - #{$gx-unit-size * 2});
        }
      }
    }
  }
}