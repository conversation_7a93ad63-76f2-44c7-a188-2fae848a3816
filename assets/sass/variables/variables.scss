@use '@gx-design/theme/styles' as *;

@mixin img-retina($file-1x, $file-2x, $width-1x, $height-1x) {
  background-image: url('#{$file-1x}');

  @media only screen and (-webkit-min-device-pixel-ratio: 2),
  only screen and (min--moz-device-pixel-ratio: 2),
  only screen and (-o-min-device-pixel-ratio: 2/1),
  only screen and (min-device-pixel-ratio: 2),
  only screen and (min-resolution: 192dpi),
  only screen and (min-resolution: 2dppx) {
    background-image: url('#{$file-2x}');
    background-size: $width-1x $height-1x;
  }
}

$gx-unit-size: 0.8rem;

$gx-header-size: (
  $gx-unit-size * 7
);
$gx-side-menu-widthMobile: 32rem;
$gx-side-menu-width: 24rem;
$gx-new-menu-width: 8.8rem;
$gx-new-menu-header-height: 6.4rem;

$gx-multi-agency-banner-height: 6rem;

$login-logo-width: 11.2rem !default;

// Appointment colors
$appointment-type-1: #a0ce00;
$appointment-type-2: #0c889b;
$appointment-type-3: #a36922;
$appointment-type-4: #a52a2a;
$appointment-type-5: #dd5625;
$appointment-type-6: #5f9ea0;
$appointment-type-7: #6495ed;
$appointment-type-8: #483d8b;
$appointment-type-9: #878585;
$appointment-type-10: #1e90ff;
$appointment-type-11: #228b22;
$appointment-type-12: #daa520;

// Dimensioni delle immagini
$img-nw-w: 17rem;
$img-nw-h: 12.5rem;

$gtx-common-base-path-img: '~@getrix/common/img/';
$flag-icon-css-path: '~flag-icon-css/flags';

$gx-logo-width: 8rem !default;
$gx-logo-height: 2.4rem !default;