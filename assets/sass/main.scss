@use 'sass:meta';

$gtx-common-base-path-img: '../getrix/common/img/' !default;

// Reset
@include meta.load-css('@gx-design/core/reset');

// General styles
@include meta.load-css('@gx-design/theme/custom-properties');
@include meta.load-css('@gx-design/layout/styles');
@include meta.load-css('@gx-design/typography/styles');
@include meta.load-css('@gx-design/tools/styles');
@include meta.load-css('@gx-design/utilities/styles');

// Gx-design Components
@include meta.load-css('@gx-design/icon/styles');
@include meta.load-css('@gx-design/label/styles');
@include meta.load-css('@gx-design/accordion/styles');
@include meta.load-css('@gx-design/action-list/styles');
@include meta.load-css('@gx-design/alert/styles');
@include meta.load-css('@gx-design/avatar/styles');
@include meta.load-css('@gx-design/badge/styles');
@include meta.load-css('@gx-design/button/styles');
@include meta.load-css('@gx-design/dropdown/styles');
@include meta.load-css('@gx-design/empty-state/styles');
@include meta.load-css('@gx-design/energetic-tag/styles');
@include meta.load-css('@gx-design/helper-text/styles');
@include meta.load-css('@gx-design/list/styles');
@include meta.load-css('@gx-design/modal/styles');
@include meta.load-css('@gx-design/notification-badge/styles');
@include meta.load-css('@gx-design/pager/styles');
@include meta.load-css('@gx-design/pagination-bar/styles');
@include meta.load-css('@gx-design/popover/styles');
@include meta.load-css('@gx-design/summary-item/styles');
@include meta.load-css('@gx-design/tabs/styles');
@include meta.load-css('@gx-design/tag/styles');
@include meta.load-css('@gx-design/tooltip/styles');
@include meta.load-css('@gx-design/input/styles');
@include meta.load-css('@gx-design/snackbar/styles');
@include meta.load-css('@gx-design/checkbox/styles');
@include meta.load-css('@gx-design/icon-input/styles');
@include meta.load-css('@gx-design/radio/styles');
@include meta.load-css('@gx-design/select/styles');
@include meta.load-css('@gx-design/textarea/styles');
@include meta.load-css('@gx-design/toolbar/styles');
@include meta.load-css('@gx-design/tooltip/styles');
@include meta.load-css('@gx-design/loader/styles');
@include meta.load-css('@gx-design/table-data/styles');
@include meta.load-css('@gx-design/toggle/styles');

// Pepita Components
@include meta.load-css('@pepita-component/chart/index.scss');

// Nugget Components
@include meta.load-css('@nugget/carousel/nd-carousel');

// Getrix Common
@include meta.load-css('@getrix/common/js/phone-verification/sass/phone-verification');

// Layout
@include meta.load-css('layout/header');
@include meta.load-css('layout/head-section');
@include meta.load-css('layout/container');
@include meta.load-css('layout/content');
@include meta.load-css('layout/menu');
@include meta.load-css('layout/page');

// MLS Components
@include meta.load-css('components/section/section');
@include meta.load-css('components/contract-box/contract-box');
@include meta.load-css('components/customer-service/customer-service');
@include meta.load-css('components/table/table-old');
@include meta.load-css('components/table/table-new');
@include meta.load-css('components/crm-table/crm-table');
@include meta.load-css('components/crm-autocomplete/crm-autocomplete');
@include meta.load-css('components/table/table');
@include meta.load-css('components/modal/modal');
@include meta.load-css('components/ask-help/ask-help');
@include meta.load-css('components/card/card');
@include meta.load-css('components/list/list');
@include meta.load-css('components/appointment/main');
@include meta.load-css('components/react-calendar/calendar');
@include meta.load-css('components/financial-advice/main');
@include meta.load-css('components/attached-box/attached-box');
@include meta.load-css('components/autocomplete/autocomplete');
@include meta.load-css('components/character-counter/character-counter');
@include meta.load-css('components/flags/flags');
@include meta.load-css('components/beamer/beamer');
@include meta.load-css('components/form-group/form-group');
@include meta.load-css('components/generic-list/generic-list');
@include meta.load-css('components/input-group/input-group');
@include meta.load-css('components/property-item/property-item');
@include meta.load-css('components/popover-old/popover-old');
@include meta.load-css('components/steps/steps');
@include meta.load-css('components/error/error');
@include meta.load-css('components/error-page/error-page');
@include meta.load-css('components/map-box/map-box');
@include meta.load-css('components/nav-slider/nav-slider');
@include meta.load-css('components/multiselect/multiselect');
@include meta.load-css('components/parsley/parsley');
@include meta.load-css('components/loader/loader');
@include meta.load-css('components/summary-list/summary-list');
@include meta.load-css('components/password-meter/password-meter');
@include meta.load-css('components/toolbar/toolbar');
@include meta.load-css('components/img-editor/imgeditor_overrides');
@include meta.load-css('components/leaflet/leaflet');
@include meta.load-css('components/notify/notify');
@include meta.load-css('components/crm-section/section');
@include meta.load-css('components/crm-section/alert-wrapper');
@include meta.load-css('components/crm-section/sidebar');
@include meta.load-css('components/crm-section/crm-visibility-summary');
@include meta.load-css('components/crm-data-variation/crm-data-variation');
@include meta.load-css('components/swiper-carousel/swiper-carousel');
@include meta.load-css('components/feature-section/feature-section');
@include meta.load-css('components/send-email-form/send-email-form');
@include meta.load-css('components/content-dropdown/content-dropdown');

//Pages
@include meta.load-css('pages/acquisition/estimate/main');
@include meta.load-css('pages/acquisition//privates/main');
@include meta.load-css('pages/matches/matches');
@include meta.load-css('pages/messaging/messaging');
@include meta.load-css('pages/customer/main');
@include meta.load-css('pages/research/research');
@include meta.load-css('pages/youdomus/youdomus');
@include meta.load-css('pages/multisend/multisend');
@include meta.load-css('pages/dashboard/dashboard');
@include meta.load-css('pages/immovisita/scheduled');
@include meta.load-css('pages/settings/settings');
@include meta.load-css('pages/settings/administration');
@include meta.load-css('pages/login/login');
@include meta.load-css('pages/login/a2f');
@include meta.load-css('pages/login/client_app_login');
@include meta.load-css('pages/performance/performance');
@include meta.load-css('pages/auctions-catalogue/main');
@include meta.load-css('pages/property/main');
@include meta.load-css('pages/landing-page/module-activations');
@include meta.load-css('pages/accept-page/accept-page');
@include meta.load-css('pages/zones-manager/zones-manager');
@include meta.load-css('pages/active-searches/active-searches');
@include meta.load-css('pages/notification-settings/notification-settings');
@include meta.load-css('pages/report-insights/report-insights');

// Print
@include meta.load-css('print/print');
