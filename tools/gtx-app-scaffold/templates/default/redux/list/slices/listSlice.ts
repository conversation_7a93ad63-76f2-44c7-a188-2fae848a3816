import { createSlice } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import type { {{capitalizedName}}ListRootState } from '../store'
import { I{{capitalizedName}}ListItem } from '../../../types/list'



// Define a type for the slice state
type {{capitalizedName}}ListState = I{{capitalizedName}}ListItem[]

// Define the initial state using that type
const initialState: {{capitalizedName}}ListState = []

export type {{capitalizedName}}ItemsUpdateOptions = {
  ids: I{{capitalizedName}}ListItem['id'][],
  data: Partial<Omit<I{{capitalizedName}}ListItem, 'id'>>
}

export const {{camelCasedName}}ListSlice = createSlice({
  name: "list",
  // `createSlice` will infer the state type from the `initialState` argument
  initialState,
  reducers: {
    setList: (state, action: PayloadAction<{{capitalizedName}}ListState>) => {
      // return action.payload.map(item => {
      //   item.id = item.id
      //   return item
      // })

      return action.payload
    },
    removeListItems: (state, action: PayloadAction<number[]>) => {
      return state.filter(item => !action.payload.includes(item.id))
    },
    itemUpdate: ( state, action: PayloadAction<{{capitalizedName}}ItemsUpdateOptions>)=> {
      const { ids, data} = action.payload
      return state.map(itm => {
        if(ids.includes(itm.id)){
          return {
            ...itm,
            ...data
          }
        }else{
          return itm
        }
      })
    }
  }
})

export const { setList, removeListItems, itemUpdate } = {{camelCasedName}}ListSlice.actions

// Other code such as selectors can use the imported `RootState` type
export const select{{capitalizedName}}List = (state: {{capitalizedName}}ListRootState) => state.list

export default {{camelCasedName}}ListSlice.reducer
