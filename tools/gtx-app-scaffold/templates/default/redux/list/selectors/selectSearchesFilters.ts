import { selectFilters, WithFilters } from "gtx-react/rtk/slices/lists/filtersSlice";

export interface IListFilters {
    email: string,
    preferred: boolean,
    category: any,
    contract: any
    q?: string
    status: 'active' | 'archived'
}

export type IArchivedListFilters = Pick<
    IListFilters,
    'category'| 'contract' | 'email' | 'status' | 'q'
    >

export type IListFiltersKeys = keyof IListFilters

export const selectSearchesFilters = (state: WithFilters<unknown, IListFilters|IArchivedListFilters>) => selectFilters(state)
