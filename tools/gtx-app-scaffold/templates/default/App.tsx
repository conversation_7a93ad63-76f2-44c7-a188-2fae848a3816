
import React from 'react';
import { {{ camelCasedName }}ListPage } from './pages/{{capitalizedName}}ListPage';
import { {{ camelCasedName }}DetailPage } from './pages/{{capitalizedName}}DetailPage';
import { {{ camelCasedName }}CreatePage } from './pages/{{capitalizedName}}CreatePage';
import { GtxApp } from 'gtx-react/components/GtxApp/GtxApp';

export const App = () => {
    return <GtxApp
        pages={[
            {{ camelCasedName }}ListPage,
            {{ camelCasedName }}CreatePage,
            {{ camelCasedName }}DetailPage
        ]}
/>
}

