import * as React from "react";
import { {{ camelCasedName }}Store } from "../redux/list/store";



import { LIST_PATH } from "../web-api/endpoints";

import { trans } from "@pepita/babelfish";

import { GtxPageWithStore } from "gtx-react/components/GtxApp/GtxApp";
import { {{capitalizedName}}ListView } from "../views/{{capitalizedName}}ListView";

export const {{ camelCasedName }}ListPage: GtxPageWithStore = {
    store: {{ camelCasedName }}Store,
    container: {{capitalizedName}}ListView,
    wrapper: ({ children }) => <div>{children}</div>,
    path: LIST_PATH,
    initFunc: () => new Promise(resolve => setTimeout(resolve, 1000)),
    header: {
        actions: () => <>you action component</>,
        hideOnMobile: false,
        customTitle: trans('label.active_searches_list.title')
    }
}
