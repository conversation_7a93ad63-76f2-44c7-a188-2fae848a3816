const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

function getUncommittedJSAndTSXDirectories() {
    try {
        const commandOutput = execSync('git status --porcelain', { encoding: 'utf-8' });
        const lines = commandOutput.split('\n');
        const uncommittedDirectories = new Set();

        for (const line of lines) {
            const match = line.match(/^\s*[AM?]+\s+(.*\.(ts|tsx))$/);
            if (match) {
                const filePath = match[1];
                const directory = path.dirname(filePath);
                uncommittedDirectories.add(directory);
            }
        }

        const uncommittedDirectoriesArray = Array.from(uncommittedDirectories);

        return uncommittedDirectoriesArray;
    } catch (error) {
        console.error('An error occurred while running the Git command:', error.message);
        return [];
    }
}

function getUncommittedJSAndTSXFiles() {
    try {
        const commandOutput = execSync('git status --porcelain', { encoding: 'utf-8' });
        const lines = commandOutput.split('\n');
        const uncommittedFiles = new Set();

        for (const line of lines) {
            const match = line.match(/^\s*[AM?]+\s+(.*\.(ts|tsx))$/);
            if (match) {
                const filePath = match[1];
                uncommittedFiles.add(filePath);
            }
        }

        const uncommittedFilesArray = Array.from(uncommittedFiles);

        return uncommittedFilesArray;
    } catch (error) {
        console.error('An error occurred while running the Git command:', error.message);
        return [];
    }
}

function getIncludedDirectoriesSet(includedDirectories) {
    const includedDirectoriesSet = new Set();
    for (const directory of includedDirectories) {
        includedDirectoriesSet.add(directory);
    }

    return includedDirectoriesSet;
}

function includeNewDirectories(includedDirectoriesSet, uncommittedDirectories) {
    return uncommittedDirectories.reduce((acc, directory) => {
        if (!includedDirectoriesSet.has(directory)) {
            includedDirectoriesSet.add(directory);
            return acc + 1;
        }
        return acc;
    }, 0);
}

function updateTsconfig(tsconfig, includedDirectoriesSet) {
    fs.writeFileSync(
        // directory da dove viene eseguito il comando
        './include-only.tsconfig.json',
        JSON.stringify(
            {
                ...tsconfig,
                include: Array.from(includedDirectoriesSet),
            },
            null,
            4
        )
    );
}

function getArgument(argName) {
    const index = process.argv.findIndex((arg) => arg === `--${argName}`);
    if (index === -1) {
        throw new Error(`Argument --${argName} not found`);
    }
    return process.argv[index + 1];
}

module.exports = {
    getUncommittedJSAndTSXDirectories,
    getIncludedDirectoriesSet,
    includeNewDirectories,
    updateTsconfig,
    getUncommittedJSAndTSXFiles,
    getArgument,
};
