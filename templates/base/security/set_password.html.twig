{% extends 'base/security/base.html.twig' %}

{% block inner_content %}
	<div class="login-content">
		{{ form_start(
			form,
			{ 'attr':
				{
					'class': 'form-account form-account--set-password',
					'data-component': 'form-password',
					'autocomplete': 'off',
					'data-parsley-errors-messages-disabled': 'true'
				}
			})
		}}
		<h1 class="gx-title-1">{{ panelTitle }}</h1>
		<p>{{ panelSubTitle }}</p>
		{% for flashMessage in app.session.flashbag.get('error') %}
			<div class="gx-alert gx-alert--margin gx-alert--error">
					<svg class="gx-icon gx-icon--negative" xmlns="http://www.w3.org/2000/svg" id="cross-circle" viewBox="0 0 24 24" fill-rule="evenodd">
					<path d="M7.22 7.22a.75.75 0 0 1 1.06 0L12 10.94l3.72-3.72a.75.75 0 1 1 1.06 1.06L13.06 12l3.72 3.72a.75.75 0 0 1-1.06 1.06L12 13.06l-3.72 3.72a.75.75 0 1 1-1.06-1.06L10.94 12 7.22 8.28a.75.75 0 0 1 0-1.06Z"/>
					<path d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm10-8.5a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17Z"/>
					</svg>
					<span>
							{{ flashMessage }}
					</span>
			</div>
		{% endfor %}
		<div class="alert-container"></div>
		{# trick per impedire l'autocomplete del campo password #}
		<input type="password" name="fake-password" class="hidden" />
		<div class="gx-input-wrapper">
			{{ form_label(
				form.password.first, form.password.first,
				{'label_attr':
					{
						'class': 'gx-sr-only'
					}
				})
			}}
			<div class="gx-input-addon-wrapper">
				<div class="gx-input-addon gx-input-addon--withIcon">
					<svg class="gx-icon" xmlns="http://www.w3.org/2000/svg" id="lock" viewBox="0 0 24 24" fill-rule="evenodd">
						<path d="M9 6.5a3 3 0 1 1 6 0V11H9V6.5ZM7.5 11V6.5a4.5 4.5 0 0 1 9 0V11h.75A2.75 2.75 0 0 1 20 13.75v5.5A2.75 2.75 0 0 1 17.25 22H6.75A2.75 2.75 0 0 1 4 19.25v-5.5A2.75 2.75 0 0 1 6.75 11h.75Zm-2 2.75c0-.69.56-1.25 1.25-1.25h10.5c.69 0 1.25.56 1.25 1.25v5.5c0 .69-.56 1.25-1.25 1.25H6.75c-.69 0-1.25-.56-1.25-1.25v-5.5Z"/>
					</svg>
				</div>
			{{ form_widget(
				form.password.first,
				{'attr':
					{
						'class': 'gx-input gx-input--withAddon',
						'data-component': 'password-new',
						'placeholder': trans('label.insert_password_1', {}, gtxConstants.DOMAIN_CMS),
						'data-parsley-required': 'true',
						'data-parsley-required-message': trans('label.insert_password', {}, gtxConstants.DOMAIN_CMS),
						'data-parsley-minlength': '6',
						'data-parsley-minlength-message': trans('profile.new_password_minlength', {}, gtxConstants.DOMAIN_CMS),
						'autofocus': 'true',
						'autocomplete': 'off'
					}
				})
			}}
			</div>
			<div class="gx-password-meter" id="gx-password-meter" style="display: none"></div>
		</div>
		<div class="gx-input-wrapper">
			{{ form_label(
				form.password.second, form.password.second,
				{'label_attr':
					{
						'class': 'gx-sr-only'
					}
				})
			}}
				<div class="gx-input-addon-wrapper">
					<div class="gx-input-addon gx-input-addon--withIcon">
						<svg class="gx-icon" xmlns="http://www.w3.org/2000/svg" id="lock" viewBox="0 0 24 24" fill-rule="evenodd">
							<path d="M9 6.5a3 3 0 1 1 6 0V11H9V6.5ZM7.5 11V6.5a4.5 4.5 0 0 1 9 0V11h.75A2.75 2.75 0 0 1 20 13.75v5.5A2.75 2.75 0 0 1 17.25 22H6.75A2.75 2.75 0 0 1 4 19.25v-5.5A2.75 2.75 0 0 1 6.75 11h.75Zm-2 2.75c0-.69.56-1.25 1.25-1.25h10.5c.69 0 1.25.56 1.25 1.25v5.5c0 .69-.56 1.25-1.25 1.25H6.75c-.69 0-1.25-.56-1.25-1.25v-5.5Z"/>
						</svg>
					</div>
					{{ form_widget(
						form.password.second,
						{'attr':
							{
								'class': 'gx-input gx-input--withAddon',
								'data-component': 'password-repeat',
								'placeholder': trans('label.repeat_password_3', {}, gtxConstants.DOMAIN_CMS),
								'data-parsley-required': 'true',
								'data-parsley-required-message': trans('label.repeat_password_2', {}, gtxConstants.DOMAIN_CMS),
								'data-parsley-equalto': '#form_password_first',
								'data-parsley-equalto-message': trans('label.repeat_new_password_correct', {}, gtxConstants.DOMAIN_CMS),
								'autofocus': 'true',
								'autocomplete': 'off'
							}
						})
					}}
					</div>
		</div>
		<div class="form-account__footer clearfix">
			<button type="button" class="gx-button gx-button--accent gx-button--fullWidth invia" data-action="submit">
				{{ "label.confirm"|trans({}, gtxConstants.DOMAIN_CMS) }}
			</button>
			<a class="back-to-link" href="{{ path('app_login') }}">{{ "label.back_to_login"|trans({}, gtxConstants.DOMAIN_CMS) }}</a>
		</div>
		{{ form_end(form) }}
	</div>
	{% include 'base/security/footer.html.twig' %}
{% endblock %}

{% block javascripts %}
	{{ parent() }}
	{{ webpack_asset('base/set-password') }}
{% endblock %}
