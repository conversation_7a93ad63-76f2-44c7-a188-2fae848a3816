{% extends 'mail/base-restyled.html.twig' %}

{% block content %}

    <table width="100%" cellpadding="0" cellspacing="0" border="0" class="wrapper" bgcolor="#ffffff" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse !important;">
        <tbody>
        <tr>
            <td height="20" style="border-collapse:collapse;"></td>
        </tr>
        <tr>
            <td align="center" style="border-collapse:collapse; padding: 0 20px;">
                <table width="100%" cellpadding="0" cellspacing="0" border="0" class="container" align="center" style="border-collapse: collapse !important; max-width: 590px; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                    <tbody>
                    <tr>
                        <td style="border-collapse:collapse;font-family: arial; font-size: 14px; line-height: 1.6; color: #666666;" align="left">
                            {{ trans('mail.registration.text1', {
                                '%PARAM_1%': '<b style="color:#151515;">'~companyName|title~'</b>',
                                '%PARAM_2%': '<a href="'~globalConfigs.immobiliare.baseUrl~'" style="color: #666666; text-decoration: none;" target="_blank">'~gtxConstants.AD_PORTAL~'</a>'
                            }, gtxConstants.DOMAIN_CMS)|raw }}
                        </td>
                    </tr>
                    <tr>
                        <td height="20" style="border-collapse:collapse;"></td>
                    </tr>
                    <tr>
                        <td>
                            <table width="100%" cellpadding="0" cellspacing="0" border="0" class="data-table" align="center" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse !important; background: #fafafa;">
                                <tbody>
                                <tr>
                                    <td height="20" style="border-collapse:collapse;"></td>
                                </tr>
                                <tr>
                                    <td class="data-table__title" style="color: #666666; font-family: Arial, 'Raleway', sans; font-size: 16px; font-weight: 600; padding: 0 20px;">
                                        {{ trans('label.your_data', {}, gtxConstants.DOMAIN_CMS) }}
                                    </td>
                                </tr>
                                <tr>
                                    <td height="20" style="border-collapse:collapse;"></td>
                                </tr>
                                <tr>
                                    <td style="padding: 0 20px;">
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" align="center" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse !important; background: #fafafa;">
                                            <tbody>
                                            <tr>
                                                <td class="data-table__row" style="border-bottom: 1px solid #dddddd; padding: 6px 0;">
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse !important;">
                                                        <tr>
                                                            <td width="140">
                                                    <![endif]-->
                                                    <div class="name" style="color: #666666; display: inline-block; font-family: Arial, sans-serif; font-size: 12px; min-width: 140px; padding: 4px 0; text-transform: uppercase;">
                                                        {{ trans('label.name', {}, gtxConstants.DOMAIN_CMS) }}
                                                    </div>
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    </td>
                                                    <td>
                                                    <![endif]-->
                                                    <div class="value" style="color: #333333; display: inline-block; font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; min-width: 240px; padding: 4px 0;">
                                                        {{ firstName|title }}
                                                    </div>
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    </td>
                                                    </tr>
                                                    </table>
                                                    <![endif]-->
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="data-table__row" style="border-bottom: 1px solid #dddddd; padding: 6px 0;">
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse !important;">
                                                        <tr>
                                                            <td width="140">
                                                    <![endif]-->
                                                    <div class="name" style="color: #666666; display: inline-block; font-family: Arial, sans-serif; font-size: 12px; min-width: 140px; padding: 4px 0; text-transform: uppercase;">
                                                        {{ trans('label.surname', {}, gtxConstants.DOMAIN_CMS) }}
                                                    </div>
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    </td>
                                                    <td>
                                                    <![endif]-->
                                                    <div class="value" style="color: #333333; display: inline-block; font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; min-width: 240px; padding: 4px 0;">
                                                        {{ lastName|title }}
                                                    </div>
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    </td>
                                                    </tr>
                                                    </table>
                                                    <![endif]-->
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="data-table__row" style="border-bottom: 1px solid #dddddd; padding: 6px 0;">
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse !important;">
                                                        <tr>
                                                            <td width="140">
                                                    <![endif]-->
                                                    <div class="name" style="color: #666666; display: inline-block; font-family: Arial, sans-serif; font-size: 12px; min-width: 140px; padding: 4px 0; text-transform: uppercase;">
                                                        {{ trans('label.mail', {}, gtxConstants.DOMAIN_CMS) }}
                                                    </div>
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    </td>
                                                    <td>
                                                    <![endif]-->
                                                    <div class="value" style="color: #333333; display: inline-block; font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; min-width: 240px; padding: 4px 0;">
                                                        <a href="mailto:{{ email }}" style="color: #216A95; text-decoration: none;">{{ email }}</a>
                                                    </div>
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    </td>
                                                    </tr>
                                                    </table>
                                                    <![endif]-->
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="data-table__row last" style="border-bottom: none; padding: 6px 0;">
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse !important;">
                                                        <tr>
                                                            <td width="140">
                                                    <![endif]-->
                                                    <div class="name" style="color: #666666; display: inline-block; font-family: Arial, sans-serif; font-size: 12px; min-width: 140px; padding: 4px 0; text-transform: uppercase;">
                                                        {{ trans('label.phone', {}, gtxConstants.DOMAIN_CMS) }}
                                                    </div>
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    </td>
                                                    <td>
                                                    <![endif]-->
                                                    <div class="value" style="color: #333333; display: inline-block; font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; min-width: 240px; padding: 4px 0;">
                                                        <a href="tel:{{ phonePrefix }}{{ phoneNumber }}" class="tel" style="color: #333333; text-decoration: none;">{{ phonePrefix }}{{ phoneNumber }}</a>
                                                    </div>
                                                    <!--[if (gte mso 9)|(lte ie 8)]>
                                                    </td>
                                                    </tr>
                                                    </table>
                                                    <![endif]-->
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td height="10" style="border-collapse:collapse;"></td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>

        <tr>
            <td style="padding: 0 20px;">
                <table width="100%" cellpadding="0" cellspacing="0" border="0" class="container" align="center" style="border-collapse: collapse !important; max-width: 590px; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                    <tbody>
                    <tr>
                        <td height="30" style="border-collapse:collapse;"></td>
                    </tr>
                    <tr>
                        <td class="content-p" style="color: #666666; font-family: Arial, sans; font-size: 14px; line-height: 1.5; text-align: left;">
                            {{ trans('mail.registration.text2', {
                                '%PARAM_1%': '<a href="tel:'~gtxConstants.TEL_SERVIZIO_CLIENTI~'" class="link-hidden" style="color: #666666; text-decoration: none;"><b>'~gtxConstants.TEL_SERVIZIO_CLIENTI~'</b></a>',
                                '%PARAM_2%': '<a href="'~globalConfigs.immobiliare.baseUrl~'" style="color: #666666; text-decoration: none;" target="_blank">'~gtxConstants.AD_PORTAL~'</a>',
                                '%PARAM_3%': '<a href="'~globalConfigs.immobiliare.baseUrl~'" class="link-hidden" style="color: #666666; text-decoration: none;" target="_blank"><b>'~gtxConstants.APP_FIRMA_EMAIL~'</b></a>'
                            }, gtxConstants.DOMAIN_CMS)|raw }}
                        </td>
                    </tr>
                    <tr>
                        <td height="20" style="border-collapse:collapse;"></td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        </tbody>
    </table>

{% endblock %}

{% block footer %}
    {% include 'mail/settings/footer.html.twig' %}
{% endblock %}
