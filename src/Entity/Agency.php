<?php

namespace App\Entity;

use App\Component\Module\ModuleFactory;
use App\Component\Module\ModuleList;
use App\Constants\Base\GtxConstants;
use App\Constants\Base\LanguagesConstants;
use App\Exception\AgencyValidateException;
use App\Helper\Base\Validator;
use App\Model\Agency\AgencyAddress;
use App\Model\Agency\AgencyLanguage;
use App\Model\Agency\TwoFactorAuthentication;
use App\Service\Base\Validation\VATNumber\Pattern\IsItalian;
use App\Service\Base\Validation\VATNumber\VATNumberValidatorFactory;
use App\Utils\ArrayUtil;
use Services\Getrix\AgenzieTypes\Agenzia;
use Services\Getrix\AgenzieTypes\Associazione;
use Services\Getrix\AgenzieTypes\Comune;
use Services\Getrix\AgenzieTypes\DescrizioneMultilingua;
use Services\Getrix\AgenzieTypes\Estensione;
use Services\Getrix\AgenzieTypes\Franchising;
use Services\Getrix\AgenzieTypes\OrariAgenzia;
use Services\Getrix\AgenzieTypes\Provincia;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Translation\TranslatorInterface;

class Agency extends Agenzia
{
    const ITALY_KEY = 'IT';

    /** @var array */
    public $extensions = [];

    /** @var AgencyVideo */
    public $video;

    /** @var ModuleList */
    public $modules;

    /** @var AgencyContract */
    public $contract;

    /** @var AgencyAddress */
    public $address;

    /** @var AgencyLanguage|null */
    public $language;

    /** @var array */
    protected $agencyFields = ['nome', 'email', 'telefono1', 'telefono2', 'cellulare', 'fax', 'web', 'paginaFacebook', 'descrizioniMultilingua'];

    /** @var array */
    protected $sensitiveData = ['email', 'telefono1', 'telefono2', 'cellulare', 'fax', 'emailPec'];

    protected $defaultLanguageKey = LanguagesConstants::ITALIANO;

    /** @var array */
    public $agencyFlags = [];

    /** @var array */
    protected $sensitiveDataCleanValues = [
        'email' => null,
        'telefono1' => null,
        'telefono2' => '',
        'cellulare' => '',
        'fax' => '',
        'emailPec' => '',
    ];

    public ?TwoFactorAuthentication $twoFactorAuthentication = null;

    public function __construct($agencyExt)
    {
        if (\is_array($agencyExt)) {
            parent::__construct($agencyExt);
        } else {
            parent::__construct();
            foreach (\get_object_vars($agencyExt) as $prop => $value) {
                $this->{$prop} = $value;
            }
        }
    }

    public function getParentObj()
    {
        $agencyExt = new Agenzia();

        foreach (\get_object_vars($this) as $prop => $value) {
            if (\property_exists($agencyExt, $prop)) {
                $agencyExt->{$prop} = $value;
            }
        }

        return $agencyExt;
    }

    /**
     * @param array $extensions
     */
    public function setExtensions($extensions = [])
    {
        $this->extensions = $extensions;

        $moduleList = new ModuleList();
        foreach ($this->extensions as $extension) {
            $moduleList->addModule(ModuleFactory::create($extension->nome, (bool) $extension->stato, $extension->extraInfo));
        }

        $this->setModules($moduleList);
    }

    /**
     * @param AgencyVideo $video
     */
    public function setVideo($video)
    {
        $this->video = $video;
    }

    /**
     * @param $langKey
     */
    public function setDefaultLanguageKey($langKey)
    {
        $this->defaultLanguageKey = $langKey;
    }

    public function setModules(ModuleList $modules)
    {
        $this->modules = $modules;
    }

    public function getModules(): ModuleList
    {
        return $this->modules;
    }

    public function getAddress(): AgencyAddress
    {
        return $this->address;
    }

    public function setAddress(AgencyAddress $address)
    {
        $this->address = $address;
    }

    public function getLanguage()
    {
        return $this->language;
    }

    public function setLanguage(AgencyLanguage $language = null)
    {
        $this->language = $language;
    }

    /**
     * @param $extensionId
     *
     * @return bool
     */
    public function hasExtensions($extensionId)
    {
        if (!empty($this->extensions)) {
            /** @var Estensione $extension */
            foreach ($this->extensions as $k => $extension) {
                if ($extension->id === $extensionId && $extension->stato) {
                    return true;
                }
            }
        }

        return false;
    }

    /** @return int|null */
    public function getExtensionVersion(int $extensionId)
    {
        if (empty($this->extensions)) {
            return null;
        }

        foreach ($this->extensions as $extension) {
            if ($extension instanceof Extension
                && $extension->id === $extensionId
                && null !== $extension->version
            ) {
                return $extension->version->version;
            }
        }

        return null;
    }

    /**
     * Validazione dei campi nella tab "Dati agenzia o costruttore".
     * Se la validazione fallisce viene lanciata un'eccezione altrimenti restituisce true.
     *
     * @throws AgencyValidateException
     * @return bool
     */
    public function validateGeneralData(array $parameters, TranslatorInterface $translator)
    {
        $parameters = ArrayUtil::trimAndStripValue($parameters);

        $this->nome = isset($parameters['nome']) ? $parameters['nome'] : null;
        $this->web = isset($parameters['web']) ? $parameters['web'] : null;
        $this->email = !empty($parameters['email']) ? $parameters['email'] : $this->email;
        $this->telefono1 = !empty($parameters['telefono1']) ? $parameters['telefono1'] : $this->sensitiveDataCleanValues['telefono1'];
        $this->telefono2 = !empty($parameters['telefono2']) ? $parameters['telefono2'] : $this->sensitiveDataCleanValues['telefono2'];
        $this->cellulare = !empty($parameters['cellulare']) ? $parameters['cellulare'] : $this->sensitiveDataCleanValues['cellulare'];

        if (empty($this->franchising)) {
            $this->franchising = new Franchising();
        }
        $this->franchising->idFranchising = isset($parameters['idFranchising']) ? $parameters['idFranchising'] : null;

        if (!empty($parameters['languageId'])) {
            $this->language = new AgencyLanguage();
        }
        $this->language->setId($parameters['languageId']);

        if (empty($this->associazione)) {
            $this->associazione = new Associazione();
        }
        $this->associazione->idAssociazione = isset($parameters['idAssociazione']) ? $parameters['idAssociazione'] : null;

        if (empty($this->associazioneProvincia)) {
            $this->associazioneProvincia = new Provincia();
        }
        $this->associazioneProvincia->idProvincia = !empty($parameters['idProvinciaAssociazione']) ? $parameters['idProvinciaAssociazione'] : null;

        if (!empty($parameters['descrizioniMultilingua'])) {
            $this->descrizioniMultilingua = [];
            foreach ($parameters['descrizioniMultilingua'] as $value) {
                if ($value['lang'] == LanguagesConstants::KEYS[$this->defaultLanguageKey]['locale']) {
                    //setto il campo descrizione agenzia dall'array descrizioniMultilingua
                    $this->descrizione = $value['description'];
                }

                $descrizioneMultilingua = new DescrizioneMultilingua();
                $descrizioneMultilingua->lingua = $value['lang'];
                $descrizioneMultilingua->testo = $value['description'];
                $descrizioneMultilingua->idTesto = $value['idTesto'];
                $this->descrizioniMultilingua[] = $descrizioneMultilingua;
            }
        }

        if (!empty($this->descrizioniMultilingua)) {
            foreach ($this->descrizioniMultilingua as $desc) {
                if (!empty($desc->testo)) {
                    if (\mb_strlen(\str_replace("\r", '', $desc->testo), 'UTF-8') > GtxConstants::DESCRIPTION_MAX_LENGTH_AGENCY) {
                        throw new AgencyValidateException($translator->trans('agency.exception.desc_max_length', ['%DESCRIPTION_MAX_LENGTH_AGENC%' => GtxConstants::DESCRIPTION_MAX_LENGTH_AGENCY], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
                    }

                    if (!\preg_match('/' . GtxConstants::REGEX_DESC_MIN_WORDS_LEN . '/', $desc->testo)) {
                        throw new AgencyValidateException($translator->trans('agency.exception.desc_min_words', ['%DESCRIPTION_MIN_WORDS_AGENCY%' => GtxConstants::DESCRIPTION_MIN_WORDS_AGENCY], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
                    }
                }
            }
        }

        $this->comunicazioni = isset($parameters['comunicazioni']) && \filter_var($parameters['comunicazioni'], \FILTER_VALIDATE_BOOLEAN) ? 1 : 0;

        if (empty($this->nome)) {
            throw new AgencyValidateException($translator->trans('agency.exception.name_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (!empty($this->associazione->idAssociazione) && '-1' !== $this->associazione->idAssociazione && empty($this->associazioneProvincia->idProvincia)) {
            throw new AgencyValidateException($translator->trans('agency.exception.province_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (!empty($this->descrizione)) {
            if (\mb_strlen(\str_replace("\r", '', $this->descrizione), 'UTF-8') > GtxConstants::DESCRIPTION_MAX_LENGTH_AGENCY) {
                throw new AgencyValidateException($translator->trans('agency.exception.desc_max_length', ['%DESCRIPTION_MAX_LENGTH_AGENC%' => GtxConstants::DESCRIPTION_MAX_LENGTH_AGENCY], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
            }

            if (!\preg_match('/' . GtxConstants::REGEX_DESC_MIN_WORDS_LEN . '/', $this->descrizione)) {
                throw new AgencyValidateException($translator->trans('agency.exception.desc_min_words', ['%DESCRIPTION_MIN_WORDS_AGENCY%' => GtxConstants::DESCRIPTION_MIN_WORDS_AGENCY], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
            }
        }

        if (!empty($this->email) && !Validator::isEmail($this->email)) {
            throw new AgencyValidateException($translator->trans('agency.exception.valid_email', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if ((!empty($this->telefono1) && !$this->validatePhoneNumber($this->telefono1)) || ((!empty($this->telefono2) && !$this->validatePhoneNumber($this->telefono2)))) {
            throw new AgencyValidateException($translator->trans('agency.exception.valid_phone', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (!empty($this->cellulare) && !$this->validatePhoneNumber($this->cellulare)) {
            throw new AgencyValidateException($translator->trans('agency.exception.valid_mobile', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        return true;
    }

    /**
     * @param $phone
     *
     * @return bool
     */
    protected function validatePhoneNumber($phone)
    {
        return Validator::isPhoneNumber($phone, true) && \strlen($phone) >= GtxConstants::PHONE_MIN_LENGTH;
    }

    /**
     * Verifica che gli orari di apertura chiusura siano validi, ossia che:
     *   - siano tutte nel formato xx:xx, compreso tra 00:00 e 23:59
     *   - non sia omesso un orario di apertura e di chiusura per una fascia
     *   - non vi siano orari di apertura successivi a quelli di chiusura
     *   - non vi siano orari sovrapposti per la stessa giornata
     *
     * @throws AgencyValidateException
     */
    protected function checkOpeningTime(array $giorni, TranslatorInterface $translator)
    {
        foreach ($giorni as $idGiorno => $giorno) {
            foreach ($giorno['orari'] as $key => $orari) {
                $openingTime = $orari['inizio'];
                $closingTime = $orari['fine'];

                if (empty($openingTime)) {
                    throw new AgencyValidateException($translator->trans('agency.exception.opening_time_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
                }

                if (empty($closingTime)) {
                    throw new AgencyValidateException($translator->trans('agency.exception.closing_time_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
                }

                if (!Validator::isTime($openingTime)) {
                    throw new AgencyValidateException($translator->trans('agency.exception.opening_time', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
                }

                if (!Validator::isTime($closingTime)) {
                    throw new AgencyValidateException($translator->trans('agency.exception.closing_time', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
                }

                if ($openingTime > $closingTime) {
                    throw new AgencyValidateException($translator->trans('agency.exception.closing_before_opening_2', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
                }

                if ($openingTime == $closingTime) {
                    throw new AgencyValidateException($translator->trans('agency.exception.closing_equal_opening_2', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
                }

                foreach ($giorno['orari'] as $keyCmp => $orariCmp) {
                    if ($key !== $keyCmp && $openingTime >= $orariCmp['inizio'] && $openingTime <= $orariCmp['fine']) {
                        throw new AgencyValidateException($translator->trans('agency.exception.overlapping_times', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
                    }
                }
            }
        }
    }

    /**
     * @throws AgencyValidateException
     * @return bool
     */
    public function validateHeadquarters(array $parameters, TranslatorInterface $translator)
    {
        if (empty($this->comune)) {
            $this->comune = new Comune();
        }
        $this->comune->idComune = isset($parameters['idComune']) ? $parameters['idComune'] : null;
        $this->indirizzo = isset($parameters['indirizzo']) ? \html_entity_decode($parameters['indirizzo']) : null;
        $this->numeroCivico = isset($parameters['numeroCivico']) ? $parameters['numeroCivico'] : null;
        $this->cap = isset($parameters['cap']) ? $parameters['cap'] : null;
        $this->latitudine = isset($parameters['latitudine']) ? $parameters['latitudine'] : null;
        $this->longitudine = isset($parameters['longitudine']) ? $parameters['longitudine'] : null;
        $this->zoom = isset($parameters['zoom']) ? $parameters['zoom'] : null;

        if (empty($this->comune->idComune)) {
            throw new AgencyValidateException($translator->trans('agency.exception.municipally_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (empty($this->indirizzo)) {
            throw new AgencyValidateException($translator->trans('agency.exception.address_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (empty($this->numeroCivico)) {
            throw new AgencyValidateException($translator->trans('agency.exception.street_number_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (empty($this->cap)) {
            throw new AgencyValidateException($translator->trans('agency.exception.cap_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (empty($this->latitudine) || empty($this->longitudine) || empty($this->zoom)) {
            throw new AgencyValidateException($translator->trans('agency.exception.lat_long_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        $this->disponibilitaAppuntamento = isset($parameters['disponibilitaAppuntamento']) && \filter_var($parameters['disponibilitaAppuntamento'], \FILTER_VALIDATE_BOOLEAN) ? 1 : 0;
        $this->soloAppuntamento = isset($parameters['soloAppuntamento']) ? \filter_var($parameters['soloAppuntamento'], \FILTER_VALIDATE_BOOLEAN) : null;

        $arrayOrari = [];
        if (!empty($parameters['giorni'])) {
            $this->checkOpeningTime($parameters['giorni'], $translator);

            foreach ($parameters['giorni'] as $idGiorno => $giorno) {
                foreach ($giorno['orari'] as $orari) {
                    $orariAgenzia = new OrariAgenzia();
                    $orariAgenzia->idOrariAgenzia = $orari['idOrariAgenzia'];
                    $orariAgenzia->giorno = $idGiorno;
                    $orariAgenzia->inizio = $orari['inizio'];
                    $orariAgenzia->fine = $orari['fine'];
                    $arrayOrari[] = $orariAgenzia;
                }
            }
        }
        $this->orariAgenzia = $arrayOrari;

        return true;
    }

    /**
     * @throws AgencyValidateException
     * @return bool
     */
    public function validateAdministrativeData(array $parameters, TranslatorInterface $translator)
    {
        $this->ragionesociale = isset($parameters['ragionesociale']) ? $parameters['ragionesociale'] : null;
        $this->piva = isset($parameters['piva']) ? $parameters['piva'] : null;
        $this->socioUnico = isset($parameters['socioUnico']) && \filter_var($parameters['socioUnico'], \FILTER_VALIDATE_BOOLEAN) ? 1 : 0;
        $this->codice_fiscale = isset($parameters['codice_fiscale']) ? $parameters['codice_fiscale'] : null;
        $this->codiceSdi = isset($parameters['codiceSdi']) ? \trim($parameters['codiceSdi']) : null;
        $this->emailPec = isset($parameters['emailPec']) ? \trim($parameters['emailPec']) : $this->sensitiveDataCleanValues['emailPec'];

        if (empty($this->comuneSedeLegale)) {
            $this->comuneSedeLegale = new Comune();
        }
        $this->comuneSedeLegale->idComune = !empty($parameters['idComuneSedeLegale']) ? $parameters['idComuneSedeLegale'] : null;

        $this->indirizzoSedeLegale = isset($parameters['indirizzoSedeLegale']) ? $parameters['indirizzoSedeLegale'] : null;
        $this->capSedeLegale = isset($parameters['capSedeLegale']) ? $parameters['capSedeLegale'] : null;

        if (empty($this->registroImprese)) {
            $this->registroImprese = new Comune();
        }
        $this->registroImprese->idComune = !empty($parameters['idComuneRegistroImprese']) ? $parameters['idComuneRegistroImprese'] : null;

        if (empty($this->luogoRepertorioEconomicoAmministrativo)) {
            $this->luogoRepertorioEconomicoAmministrativo = new Comune();
        }
        $this->luogoRepertorioEconomicoAmministrativo->idComune = !empty($parameters['idComuneREA']) ? $parameters['idComuneREA'] : null;

        $this->numeroREA = isset($parameters['numeroREA']) ? $parameters['numeroREA'] : null;
        $this->capitaleSociale = isset($parameters['capitaleSociale']) ? \str_replace(',', '.', $parameters['capitaleSociale']) : null;

        if (!empty($this->emailPec) && !Validator::isEmail($this->emailPec)) {
            throw new AgencyValidateException($translator->trans('agency.exception.not_valid_pec', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (empty($this->ragionesociale)) {
            throw new AgencyValidateException($translator->trans('agency.exception.company_name_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (empty($this->piva)) {
            throw new AgencyValidateException($translator->trans('agency.exception.vat_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (!empty($this->piva) && empty($parameters['idNazioneSedeLegale'])) {
            throw new AgencyValidateException($translator->trans('agency.exception.office_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (self::ITALY_KEY === $parameters['idNazioneSedeLegale']) {
            $validator = VATNumberValidatorFactory::getValidator(new IsItalian());
            if (false === $validator->validate($this->piva)) {
                throw new AgencyValidateException($validator->getErrors()[0], Response::HTTP_BAD_REQUEST);
            }
        }

        if (!empty($this->indirizzoSedeLegale) && !\preg_match('/[a-z]{3,}/i', $this->indirizzoSedeLegale)) {
            throw new AgencyValidateException($translator->trans('agency.exception.office_address_not_valid', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (!empty($this->capitaleSociale) && !\preg_match('/^[0-9]{1,10}[.]{1}[0-9]{2}$/', $this->capitaleSociale)) {
            throw new AgencyValidateException($translator->trans('agency.exception.capital_not_valid', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (!empty($this->codiceSdi) && !Validator::isSdi($this->codiceSdi)) {
            throw new AgencyValidateException($translator->trans('agency.exception.sdi_code_not_valid', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        return true;
    }

    /**
     * @param $parameters
     *
     * @throws AgencyValidateException
     * @return bool
     */
    public function validateAgentOwner($parameters, TranslatorInterface $translator)
    {
        $this->nomeTitolare = isset($parameters['nome']) ? $parameters['nome'] : null;
        $this->cognomeTitolare = isset($parameters['cognome']) ? $parameters['cognome'] : null;
        $this->telefonoTitolare = isset($parameters['telefono']) ? $parameters['telefono'] : null;
        $this->cellulareTitolare = isset($parameters['cellulare']) ? $parameters['cellulare'] : null;

        if (empty($this->nomeTitolare) || empty($this->cognomeTitolare) || empty($this->telefonoTitolare)) {
            throw new AgencyValidateException($translator->trans('agency.exception.personal_data_required', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (!Validator::isPhoneNumber($this->telefonoTitolare)) {
            throw new AgencyValidateException($translator->trans('agency.exception.phone_not_valid', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if (!empty($this->cellulareTitolare) && !Validator::isPhoneNumber($this->cellulareTitolare)) {
            throw new AgencyValidateException($translator->trans('agency.exception.mobile_not_valid', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        return true;
    }

    public function hasSitoWeb()
    {
        return !empty($this->webagencyInfo);
    }

    public function hasGetrixPlus()
    {
        return !empty($this->getrix) && \in_array($this->getrix, [GtxConstants::STATO_GETRIX_PLUS, GtxConstants::STATO_GETRIX_LIGHT]);
    }

    public function hasGetrixLight()
    {
        return !empty($this->getrix) && GtxConstants::STATO_GETRIX_LIGHT == $this->getrix;
    }

    public function isAgencyLight()
    {
        return GtxConstants::TIPOLOGIA_AGENZIA_CONTRATTO === $this->tipologia->idTipologiaAgenzia && GtxConstants::SOTTOTIPOLOGIA_AGENZIA_LIGHT === $this->sottoTipologiaAgenzia->idSottoTipologia;
    }

    public function hasGetrixBusiness()
    {
        return !empty($this->getrix) && GtxConstants::STATO_GETRIX_BUSINESS == $this->getrix;
    }

    /**
     * @param $data
     *
     * @return mixed
     */
    public function cleanSensitiveData($data)
    {
        foreach ($this->sensitiveData as $field) {
            $data[$field] = isset($this->sensitiveDataCleanValues[$field]) ? $this->sensitiveDataCleanValues[$field] : null;
        }

        return $data;
    }

    /**
     * @param $data
     *
     * @return mixed
     */
    public function cleanUnsetSensitiveData($data)
    {
        foreach ($this->sensitiveData as $field) {
            if (isset($data[$field])) {
                unset($data[$field]);
            }
        }

        return $data;
    }

    /**
     * @param $data
     * @param $fields
     *
     * @return mixed
     */
    public function trimValues($data, $fields)
    {
        if (empty($data)) {
            return null;
        }

        foreach ($fields as $field) {
            if (!empty($data[$field])) {
                $data[$field] = \trim($data[$field]);
            }
        }

        return $data;
    }
}
