<?php

namespace App\Entity;

use App\Response\SoaSdk\LoadUserByUsernameAndPasswordResponse;

class LoginUser extends LoadUserByUsernameAndPasswordResponse
{
    private string $deviceFingerprint;
    private ?AuthenticationTokens $authenticationTokens = null;

    public function getDeviceFingerprint(): string
    {
        return $this->deviceFingerprint;
    }

    public function setDeviceFingerprint(string $deviceFingerprint): void
    {
        $this->deviceFingerprint = $deviceFingerprint;
    }

    public function getAuthenticationTokens(): ?AuthenticationTokens
    {
        return $this->authenticationTokens;
    }

    public function setAuthenticationTokens(?AuthenticationTokens $authenticationTokens = null): void
    {
        $this->authenticationTokens = $authenticationTokens;
    }
}
