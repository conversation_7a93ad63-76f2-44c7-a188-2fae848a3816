<?php

namespace App\Entity;

use PNF_Formatter;
use Services\Getrix\AgentiTypes\Contatti;

class UserContact extends Contatti
{
    protected $userContact;
    public $fNumero;
    public $prefisso;
    public $fNumeroSenzaPrefisso;
    public $shortCode;

    protected ?string $defaultShortCode           = null;
    protected ?string $defaultInternationalPrefix = null;

    public function __construct($userContact)
    {
        if (\is_array($userContact)) {
            parent::__construct($userContact);
        } else {
            parent::__construct();
            foreach (get_object_vars($userContact) as $prop => $value) {
                $this->{$prop} = $value;
            }
        }
    }

    /**
     * @return Contatti
     */
    public function getParentObj()
    {
        $contatto = new Contatti();

        foreach (get_object_vars($this) as $prop => $value) {
            if (property_exists($contatto, $prop)) {
                $contatto->{$prop} = $value;
            }
        }

        return $contatto;
    }

    public function setDefaultShortCode(string $shortCode)
    {
        $this->defaultShortCode = $shortCode;
    }

    public function setDefaultInternationalPrefix(string $prefix)
    {
        $this->defaultInternationalPrefix = $prefix;
    }

    /**
     * @return \stdClass
     */
    public function jsonSerialize()
    {
        $json = parent::jsonSerialize();

        list($json->fNumero, $json->prefisso, $json->fNumeroSenzaPrefisso, $json->shortCode) = $this->getNumberComponents();

        return $json;
    }

    public function format()
    {
        list($this->fNumero, $this->prefisso, $this->fNumeroSenzaPrefisso, $this->shortCode) = $this->getNumberComponents();
    }

    /**
     * @return array
     * @psalm-suppress UndefinedDocblockClass
     */
    protected function getNumberComponents()
    {
        $numberComponents = PNF_Formatter::getFormatter()->getComponents($this->numero, [
            'withoutInternationalPrefix' => true,
            'addInternationaShortCode'   => true,
            'splitNationalPrefix'        => false,
            'splitPhone'                 => false, ]);

        $numberFormatted = PNF_Formatter::getFormatter()->getFormattedString($this->numero);

        return [
            $numberFormatted ? $numberFormatted : $this->numero, //numero con prefisso, formattato
            isset($numberComponents['international-prefix']) ? $numberComponents['international-prefix'] : $this->defaultInternationalPrefix, //prefisso internazionale
            isset($numberComponents['number']) ? $numberComponents['number'] : $this->numero, // numero senza prefisso formattato
            isset($numberComponents['short-code']) ? $numberComponents['short-code'] : $this->defaultShortCode, // country short code
        ];
    }
}
