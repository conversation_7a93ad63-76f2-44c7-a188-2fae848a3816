<?php

declare(strict_types=1);

namespace App\Entity\Property;

use Symfony\Component\Validator\Constraints as Assert;

class SubTypology
{
    /**
     * @Assert\NotNull
     */
    private ?int $id;

    /**
     * @Assert\Type("string")
     */
    private ?string $name;

    public function __construct(int $id = null, string $name = null)
    {
        $this->id = $id;
        $this->name = $name;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }
}
