<?php

declare(strict_types=1);

namespace App\Entity\Property;

class AdFactory
{
    public static function create(
        int $id = null,
        Contract $contract = null,
        PropertyTypology $propertyTypology = null
    ): Ad {
        return new Ad($id, $contract, $propertyTypology);
    }

    public static function createFromApiResponse(array $apiResponse): Ad
    {
        return self::create(
            $apiResponse['data']['id'] ?? null,
            self::createContractFromApiResponse($apiResponse),
            self::createPropertyTypologyFromApiResponse($apiResponse)
        );
    }

    /**
     * @return Contract|null
     */
    private static function createContractFromApiResponse(array $apiResponse)
    {
        $data = $apiResponse['data']['contract'] ?? null;
        if (empty($data)) {
            return null;
        }

        return new Contract(
            $data['id'] ?? null,
            $data['name'] ?? null
        );
    }

    /**
     * @return PropertyTypology|null
     */
    private static function createPropertyTypologyFromApiResponse(array $apiResponse)
    {
        $data = $apiResponse['data']['propertyTypology'] ?? null;
        if (empty($data)) {
            return null;
        }

        return new PropertyTypology(
            self::createtTpologyFromApiResponse($apiResponse),
            self::createtSubTypologyFromApiResponse($apiResponse)
        );
    }

    /**
     * @return Typology|null
     */
    private static function createtTpologyFromApiResponse(array $apiResponse)
    {
        $data = $apiResponse['data']['propertyTypology']['typology'] ?? null;
        if (empty($data)) {
            return null;
        }

        return new Typology(
            $data['id'] ?? null,
            $data['name'] ?? null,
            self::createCategoryFromApiResponse($apiResponse)
        );
    }

    /**
     * @return Category|null
     */
    private static function createCategoryFromApiResponse(array $apiResponse)
    {
        $data = $apiResponse['data']['propertyTypology']['typology']['category'] ?? null;
        if (empty($data)) {
            return null;
        }

        return new Category(
            $data['id'] ?? null,
            $data['name'] ?? null
        );
    }

    /**
     * @return SubTypology|null
     */
    private static function createtSubTypologyFromApiResponse(array $apiResponse)
    {
        $data = $apiResponse['data']['propertyTypology']['subTypology'] ?? null;
        if (empty($data)) {
            return null;
        }

        return new SubTypology(
            $data['id'] ?? null,
            $data['name'] ?? null
        );
    }
}
