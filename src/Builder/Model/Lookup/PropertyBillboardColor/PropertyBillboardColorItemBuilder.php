<?php

declare(strict_types=1);

namespace App\Builder\Model\Lookup\PropertyBillboardColor;

use App\Builder\BuilderInterface;
use App\Model\Lookup\PropertyBillboardColor\PropertyBillboardColorItem;

class PropertyBillboardColorItemBuilder implements BuilderInterface
{
    /**
     * @var PropertyBillboardColorItem
     */
    private $instance;

    public static function newBuilder(
        PropertyBillboardColorItem $entity = null
    ): self {
        $builder = new self();
        $builder->create($entity);

        return $builder;
    }

    public function create(
        PropertyBillboardColorItem $entity = null
    ) {
        if (null !== $entity) {
            $this->instance = $entity;
        } else {
            $this->instance = new PropertyBillboardColorItem();
        }
    }

    public function build(): PropertyBillboardColorItem
    {
        return $this->instance;
    }

    public function withId(
        int $value
    ): self {
        $this->instance
            ->setId($value);

        return $this;
    }

    public function withName(
        string $name = null
    ): self {
        $this->instance
            ->setName($name);

        return $this;
    }

    public function withValue(
        string $value = null
    ): self {
        $this->instance
            ->setValue($value);

        return $this;
    }
}
