<?php

namespace App\Builder\Model\Lookup;

use App\Builder\BuilderInterface;
use App\Model\Lookup\LookupResult;
use App\Model\Lookup\LookupResultItem;

class LookupResultBuilder implements BuilderInterface
{
    /** @var LookupResult */
    private $instance;

    public static function newBuilder(LookupResult $entity = null): self
    {
        $builder = new self();
        $builder->create($entity);

        return $builder;
    }

    public function create(LookupResult $entity = null)
    {
        if (null !== $entity) {
            $this->instance = $entity;
        } else {
            $this->instance = new LookupResult();
        }
    }

    public function build(): LookupResult
    {
        return $this->instance;
    }

    public function fromTypeAndApiResponse(string $type, array $data): self
    {
        $this
            ->withType($type)
            ->withData(array_map(function (array $item) {
                return LookupResultItemBuilder::newBuilder()
                    ->withValue($item['value'] ?? null)
                    ->withLabel($item['label'] ?? null)
                    ->withExtra($item['extra'] ?? null)
                    ->build();
            }, $data))
        ;

        return $this;
    }

    public function withType(string $type): self
    {
        $this->instance
            ->setType($type);

        return $this;
    }

    /**
     * @param LookupResultItem[]|null $data
     */
    public function withData(array $data = null): self
    {
        $this->instance
            ->setData($data);

        return $this;
    }
}
