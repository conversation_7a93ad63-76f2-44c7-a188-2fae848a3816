<?php

namespace App\Controller\Base;

use App\Annotation\Module;
use Ekbl\StatsBundle\Annotation\Stats;
use GetrixBe\GrowthbookBundle\GrowthbookProxy;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Translation\TranslatorInterface;

/**
 * Class PortalNewConstructionsController
 *
 * @Route("/")
 * @Module(name="Nuove Costruzioni", template="new-constructions", service=App\Constants\Base\GtxConstants::SERVICE_ADV_SPACES_ANNUNCI_NUOVE_COSTRUZIONI)
 * @Stats
 */
class PortalNewConstructionsController extends Controller
{
    /** @var TranslatorInterface */
    private $translator;

    /** @var TokenStorageInterface */
    private $tokenStorage;

    /** @var GrowthbookProxy */
    private $growthbookProxy;

    public function __construct(
        TranslatorInterface $translator,
        TokenStorageInterface $tokenStorage,
        GrowthbookProxy $growthbookProxy
    ) {
        $this->translator   = $translator;
        $this->tokenStorage = $tokenStorage;
        $this->growthbookProxy = $growthbookProxy;
    }

    /**
     * @Route("/lista", name="new_construction_portal_list", methods={"GET"})
     * @Template("base/new-construction/portal-new-constructions.html.twig")
     */
    public function listIndexAction()
    {
        if ($this->growthbookProxy->IsOn('crm_properties_list')) {
            return $this->redirect('/immobili/lista?typology=new_construction');
        }

        return [
            'title'           => $this->translator->trans('label.new_constructions_list'),
            'site_section'    => 'new-constructions-property',
            'site_subsection' => 'portal-new-constructions-property',
            'agency'          => $this->tokenStorage->getToken()->getUser()->getAgenzia(),
        ];
    }

    /**
     * @Route("/stampa/{id}",
     *      name="new_construction_portal_print_detail",
     *      requirements={"id"="[0-9]+"})
     *      methods={"POST"})
     *
     * @return Response
     */
    public function printDetailAction(Request $request)
    {
        $requestProxy = $this->get('getrix_app.request_proxy');
        $referer      = $this->getParameter('getrix.baseurl');
        $endpoint     = $this->getParameter('app.htmlpdf.baseurl') . '/from-html';
        $html         = $request->get('html');

        $fields = [
            'format' => 'A4',
            'html'   => $html,
            'host'   => $referer,
        ];

        $result = $requestProxy::post([
            'url'     => $endpoint,
            'headers' => ['Content-Type: application/json'],
            'fields'  => $fields,
        ], 'json');

        $response = new Response($result);
        $response->headers->set('Content-Type', 'application/pdf');

        return $response;
    }
}
