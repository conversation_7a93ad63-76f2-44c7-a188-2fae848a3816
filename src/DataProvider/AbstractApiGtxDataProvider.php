<?php

declare(strict_types=1);

namespace App\DataProvider;

use App\Helper\ApiWsseHeaderGenerator;
use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;
use App\Performance\ProfilerInterface;
use App\Serializer\Serializer;
use App\Utils\SessionUtils;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\ServerBag;

abstract class AbstractApiGtxDataProvider
{
    const DEFAULT_REQUEST_HEADERS = [
        'Accept' => 'application/json',
        'connect_timeout' => 127,
    ];

    const JSON_REQUEST_HEADERS = [
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
        'connect_timeout' => 127,
    ];

    const MULTIPART_REQUEST_HEADERS = [
        'Accept' => 'application/json',
        'Content-Type' => 'multipart/form-data',
        'connect_timeout' => 127,
    ];

    const API_VERSION = 'v2';

    protected string $apiBaseurl;
    protected HttpClientHelper $client;
    protected ProfilerInterface $performanceProfiler;
    protected SessionUtils $sessionUtils;

    private ApiWsseHeaderGenerator $apiWsseHeaderGenerator;
    private ServerBag $serverBag;

    public function __construct(
        ApiWsseHeaderGenerator $apiWsseHeaderGenerator,
        HttpClientHelper $client,
        ParametersBag $parametersBag,
        ProfilerInterface $performanceProfiler,
        SessionUtils $sessionUtils
    ) {
        $this->apiBaseurl = (string) $parametersBag->get('getrix.api_baseurl');
        $this->apiWsseHeaderGenerator = $apiWsseHeaderGenerator;
        $this->performanceProfiler = $performanceProfiler;
        $this->sessionUtils = $sessionUtils;
        $this->serverBag = Request::createFromGlobals()->server;

        $this->client = $client;
        $this->client->setTimeout((float) $parametersBag->get('getrix.api_timeout'));
    }

    protected function getDefaultHeaders(): array
    {
        return self::DEFAULT_REQUEST_HEADERS + $this->getAuthorHeaders() + ['X-WSSE' => $this->apiWsseHeaderGenerator->generate()];
    }

    protected function getDefaultJsonHeaders(): array
    {
        return self::JSON_REQUEST_HEADERS + $this->getAuthorHeaders() + ['X-WSSE' => $this->apiWsseHeaderGenerator->generate()];
    }

    protected function getDefaultJsonHeadersWithAuthorId(): array
    {
        return self::JSON_REQUEST_HEADERS + $this->getAuthorHeaders() + ['X-WSSE' => $this->apiWsseHeaderGenerator->generate(), 'Author-id' => $this->sessionUtils->getAgentId()];
    }

    protected function getDefaultMultipartHeaders(): array
    {
        return self::MULTIPART_REQUEST_HEADERS + $this->getAuthorHeaders() + ['X-WSSE' => $this->apiWsseHeaderGenerator->generate()];
    }

    protected function getBaseUri(string $apiVersion = self::API_VERSION): string
    {
        return \sprintf('%s/api/%s', $this->apiBaseurl, $apiVersion);
    }

    private function getAuthorHeaders(): array
    {
        return [
            'author-ip' => $this->serverBag->get('REMOTE_ADDR'),
            'author-user-agent' => $this->serverBag->get('HTTP_USER_AGENT'),
        ];
    }

    protected function prepareRequestPayload(
        array $requests
    ): array {
        $serializer = Serializer::getSerializer();

        return \array_reduce($requests, function (array $value, $request) use ($serializer) {
            return $value + $serializer->normalize($request);
        }, []);
    }
}
