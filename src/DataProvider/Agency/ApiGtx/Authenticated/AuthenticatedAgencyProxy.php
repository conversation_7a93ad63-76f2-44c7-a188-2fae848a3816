<?php

declare(strict_types=1);

namespace App\DataProvider\Agency\ApiGtx\Authenticated;

use App\DataProvider\Agency\AgencyInterface;
use App\DataProvider\Agency\AuthenticatedAgencyInterface;
use App\DTO\DataProvider\Agency\ApiGtx\GetEvaluationZonesRequest;
use App\DTO\DataProvider\Agency\ApiGtx\MultiAgencyListMembersRequest;
use App\DTO\DataProvider\Agency\ApiGtx\PaginationRequest;
use App\DTO\Portal\Portal;
use App\DTO\Portal\SetPortalsOrder;
use App\Exception\ApiException;
use App\Exception\EntityNotFoundException;
use App\Model\Agency\Agency;
use App\Model\Agency\MultiAgencyListMembers;
use App\Model\Portal\PortalDetail;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class AuthenticatedAgencyProxy implements AuthenticatedAgencyInterface
{
    /**
     * @var int
     */
    private $agencyId;

    /** @var AgencyInterface */
    private $agencyProxy;

    public function __construct(
        AgencyInterface $agencyProxy,
        TokenStorageInterface $tokenStorage
    ) {
        $this->agencyId    = $tokenStorage->getToken()->getUser()->getAgenzia()->idAgenzia;
        $this->agencyProxy = $agencyProxy;
    }

    public function getPortalsByStatusAndCategories(
        int $status,
        array $categories = []
    ): array {
        return $this->agencyProxy->getPortalsByStatusAndCategories(
            $this->agencyId,
            $status,
            $categories
        );
    }

    public function getPortalsByContextAndCategories(
        string $context,
        array $categories = []
    ): array {
        return $this->agencyProxy->getPortalsByContextAndCategories(
            $this->agencyId,
            $context,
            $categories
        );
    }

    public function getPortalDetail(
        int $portalId
    ): PortalDetail {
        return $this->agencyProxy->getPortalDetail(
            $this->agencyId,
            $portalId
        );
    }

    public function disablePortal(
        int $portalId
    ) {
        return $this->agencyProxy->disablePortal(
            $this->agencyId,
            $portalId
        );
    }

    public function updatePortalsOrder(
        SetPortalsOrder $setPortalsOrder
    ) {
        return $this->agencyProxy->updatePortalsOrder(
            $this->agencyId,
            $setPortalsOrder
        );
    }

    public function updatePortal(
        int $portalId,
        Portal $portal
    ): PortalDetail {
        return $this->agencyProxy->updatePortal(
            $this->agencyId,
            $portalId,
            $portal
        );
    }

    /**
     * @throws ApiException
     */
    public function multiAgencyListMembers(
        int $typeId,
        PaginationRequest $paginationRequest,
        MultiAgencyListMembersRequest $multiAgencyListMembersRequest
    ): MultiAgencyListMembers {
        return $this->agencyProxy->multiAgencyListMembers(
            $this->agencyId,
            $typeId,
            $paginationRequest,
            $multiAgencyListMembersRequest
        );
    }

    /**
     * @throws ApiException
     * @throws EntityNotFoundException
     */
    public function multiAgencyGetMaster(
        int $typeId,
        int $masterId
    ): Agency {
        return $this->agencyProxy->multiAgencyGetMaster(
            $this->agencyId,
            $typeId,
            $masterId
        );
    }

    /**
     * @throws ApiException
     * @throws EntityNotFoundException
     */
    public function multiAgencyGetMember(
        int $typeId,
        int $memberId
    ): Agency {
        return $this->agencyProxy->multiAgencyGetMember(
            $this->agencyId,
            $typeId,
            $memberId
        );
    }

    public function getEvaluationZones(
        GetEvaluationZonesRequest $evaluationZonesRequest
    ): array {
        return $this->agencyProxy->getEvaluationZones(
            $this->agencyId,
            $evaluationZonesRequest
        );
    }

    /**
     * @throws \Throwable
     */
    public function getECommerceProducts(
    ): array {
        return $this->agencyProxy->getECommerceProducts($this->agencyId);
    }
}
