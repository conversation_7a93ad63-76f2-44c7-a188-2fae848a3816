<?php

declare(strict_types=1);

namespace App\DataProvider\ReceivedSearch\WsGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Searches\GeographyInformation;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class GeographyInformationDataMapper extends AbstractDataMapper
{
    /** @var MacroZoneDataMapper */
    private $macroZoneDataMapper;

    /** @var PolygonAreaDataMapper */
    private $polygonAreaDataMapper;

    public function __construct(
        MacroZoneDataMapper $macroZoneDataMapper,
        PolygonAreaDataMapper $polygonAreaDataMapper
    ) {
        $this->macroZoneDataMapper   = $macroZoneDataMapper;
        $this->polygonAreaDataMapper = $polygonAreaDataMapper;
    }

    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return GeographyInformation::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var array $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('macroZones', function () use ($data) {
                return array_map(function (array $macroZone) {
                    return $this->macroZoneDataMapper->map($macroZone);
                }, $data['macroZones'] ?? []);
            })
            ->forMember('areaPolygon', function () use ($data) {
                return $this->polygonAreaDataMapper->map($data['areaPolygon']);
            })
        ;

        return $config;
    }
}
