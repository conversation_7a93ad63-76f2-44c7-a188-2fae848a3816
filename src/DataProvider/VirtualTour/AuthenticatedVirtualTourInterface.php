<?php

declare(strict_types=1);

namespace App\DataProvider\VirtualTour;

use App\DTO\VirtualTour\SetVirtualTour as SetVirtualTourDTO;
use App\DTO\VirtualTour\VirtualTourSetBlur as VirtualTourSetBlurDTO;
use App\DTO\VirtualTour\VirtualTourSetHotspot as VirtualTourSetHotspotDTO;
use App\DTO\VirtualTour\VirtualTourSetImage as VirtualTourSetImageDTO;
use App\DTO\VirtualTour\VirtualTourSetMultimediaFloor as VirtualTourSetMultimediaFloorDTO;
use App\Model\VirtualTour\VirtualTour;
use App\Model\VirtualTour\VirtualTourBlur;
use App\Model\VirtualTour\VirtualTourEntity;
use App\Model\VirtualTour\VirtualTourHotspot;
use App\Model\VirtualTour\VirtualTourImage;
use App\Model\VirtualTour\VirtualTourMultimediaFloor;

interface AuthenticatedVirtualTourInterface
{
    public function getVirtualTourByAd(int $adId): VirtualTour;

    public function setImage360Blur(int $adId, int $image360Id, VirtualTourSetBlurDTO $blurRequest): VirtualTourBlur;

    public function updateImage360Blur(int $adId, int $image360Id, int $blurId, VirtualTourSetBlurDTO $blurRequest): VirtualTourBlur;

    public function deleteImageBlur(int $adId, int $image360Id, int $blurId): bool;

    public function setImage360Hotspot(int $adId, int $image360Id, VirtualTourSetHotspotDTO $hotspotRequest): VirtualTourHotspot;

    public function updateImage360Hotspot(int $adId, int $image360Id, int $hotspotId, VirtualTourSetHotspotDTO $hotspotRequest): VirtualTourHotspot;

    public function deleteImageHotspot(int $adId, int $image360Id, int $hotspotId): bool;

    public function setMultimediaFloor(int $adId, VirtualTourSetMultimediaFloorDTO $multimediaFloorRequest): VirtualTourMultimediaFloor;

    public function updateMultimediaFloor(int $adId, int $adMultimediaFloorId, VirtualTourSetMultimediaFloorDTO $multimediaFloorRequest): VirtualTourMultimediaFloor;

    public function deleteMultimediaFloor(int $adId, int $adMultimediaFloorId): bool;

    public function setImage(int $adId, VirtualTourSetImageDTO $imageRequest): VirtualTourImage;

    public function updateImage(int $adId, int $image360Id, VirtualTourSetImageDTO $imageRequest): VirtualTourImage;

    public function deleteImage(int $adId, int $image360Id): bool;

    public function setVirtualTour(int $adId, SetVirtualTourDTO $setVirtualTour): VirtualTourEntity;
}
