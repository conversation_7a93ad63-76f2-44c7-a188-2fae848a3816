<?php

declare(strict_types=1);

namespace App\DataProvider\AgencyActiveSearch\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\AgencyActiveSearch\AgencyActiveSearchBulkOperationOutcome;

class AgencyActiveSearchBulkOperationOutcomeDataMapper extends AbstractDataMapper
{
    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return AgencyActiveSearchBulkOperationOutcome::class;
    }
}
