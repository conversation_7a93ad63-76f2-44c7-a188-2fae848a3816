<?php

declare(strict_types=1);

namespace App\DataProvider\AgencyActiveSearch\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\DataProvider\AgencyActiveSearch\ApiGtx\DataMapper\Geography\CityDataMapper;
use App\DataProvider\AgencyActiveSearch\ApiGtx\DataMapper\Geography\MacroZoneDataMapper;
use App\DataProvider\AgencyActiveSearch\ApiGtx\DataMapper\Geography\ProvinceDataMapper;
use App\Formatter\Shared\MapFormatter;
use App\Model\AgencyActiveSearch\Geography;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class GeographyDataMapper extends AbstractDataMapper
{
    /** @var MapFormatter */
    private $mapFormatter;

    /** @var MacroZoneDataMapper */
    private $macroZoneDataMapper;

    /** @var PolygonAreaDataMapper */
    private $polygonAreaDataMapper;

    /** @var CityDataMapper */
    private $cityDataMapper;

    /** @var ProvinceDataMapper */
    private $provinceDataMapper;

    public function __construct(
        MapFormatter $mapFormatter,
        MacroZoneDataMapper $macroZoneDataMapper,
        PolygonAreaDataMapper $polygonAreaDataMapper,
        CityDataMapper $cityDataMapper,
        ProvinceDataMapper $provinceDataMapper
    ) {
        $this->mapFormatter          = $mapFormatter;
        $this->macroZoneDataMapper   = $macroZoneDataMapper;
        $this->polygonAreaDataMapper = $polygonAreaDataMapper;
        $this->cityDataMapper        = $cityDataMapper;
        $this->provinceDataMapper    = $provinceDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return Geography::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var array $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('macroZones', function () use ($data) {
                return array_map(function (array $macroZone) {
                    return $this->macroZoneDataMapper->map($macroZone);
                }, $data['macroZones'] ?? []);
            })
            ->forMember('polygonArea', function () use ($data) {
                return !empty($data['areaPolygon']) ?
                    $this->polygonAreaDataMapper->map($data['areaPolygon']) :
                    null;
            })
            ->forMember('city', function () use ($data) {
                return !empty($data['city']) ?
                    $this->cityDataMapper->map($data['city']) :
                    null;
            })
            ->forMember('province', function () use ($data) {
                return !empty($data['province']) ?
                    $this->provinceDataMapper->map($data['province']) :
                    null;
            })
            ->forMember('imageMapUrl', function () use ($data) {
                return !empty($data['city']) && !empty($data['city']['id']) ?
                    $this->mapFormatter->getCityUrl($data['city']['id'], !empty($data['macroZones']) ? $data['macroZones'] : []) : (
                        !empty($data['areaPolygon']) && !empty($data['areaPolygon']['points']) ?
                            $this->mapFormatter->getPolygonUrl($data['areaPolygon']['points']) :
                            null
                    );
            })
        ;

        return $config;
    }
}
