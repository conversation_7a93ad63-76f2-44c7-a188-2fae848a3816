<?php

declare(strict_types=1);

namespace App\DataProvider\Configuration\ApiGtx\Proxy;

use App\DataProvider\AbstractMeasurementProxy;
use App\DataProvider\Configuration\ApiGtx\Configuration;
use App\DataProvider\Configuration\ConfigurationInterface;
use App\DTO\DataProvider\Configuration\ApiGtx\UpdateSearchesNotificationSettingsRequest;
use App\Model\Configuration\SearchesNotificationSettings;
use App\Performance\ProfilerInterface;
use App\Resolver\StatsNameResolver;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class ConfigurationProxy extends AbstractMeasurementProxy implements ConfigurationInterface
{
    public function __construct(
        EventDispatcherInterface $eventDispatcher,
        StatsNameResolver $statsNameResolver,
        Configuration $dataProvider,
        ProfilerInterface $profiler
    ) {
        parent::__construct($eventDispatcher, $statsNameResolver, $dataProvider, $profiler);
    }

    /**
     * @throws \Throwable
     */
    public function getSearchesNotificationSettings(
        int $agencyId
    ): SearchesNotificationSettings {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * @throws \Throwable
     */
    public function updateSearchesNotificationSettings(
        int $agencyId,
        UpdateSearchesNotificationSettingsRequest $updateSearchesNotificationSettingsRequest
    ): SearchesNotificationSettings {
        return $this->execute(__FUNCTION__, \func_get_args());
    }
}
