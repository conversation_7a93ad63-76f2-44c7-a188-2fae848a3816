<?php

declare(strict_types=1);

namespace App\DataProvider\Match\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Match\Search;
use Carbon\Carbon;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class SearchDataMapper extends AbstractDataMapper
{
    /**
     * @var SearchTypeDataMapper
     */
    private $searchTypeDataMapper;

    /**
     * @var CustomerDataMapper
     */
    private $customerDataMapper;

    /**
     * @var TypologyDataMapper
     */
    private $typologyDataMapper;

    /**
     * @var CategoryDataMapper
     */
    private $categoryDataMapper;

    /**
     * @var ContractDataMapper
     */
    private $contractDataMapper;

    /**
     * @var PropertyConditionDataMapper
     */
    private $propertyConditionDataMapper;

    /**
     * @var EnergyEfficiencyDataMapper
     */
    private $energyEfficiencyDataMapper;

    /**
     * @var GeographyDataMapper
     */
    private $geographyDataMapper;

    /**
     * @var IntRangeDataMapper
     */
    private $intRangeDataMapper;

    /**
     * @var LicenseDataMapper
     */
    private $licenseDataMapper;

    /**
     * @var FloorRangeDataMapper
     */
    private $floorRangeDataMapper;

    /**
     * @var GardenDataMapper
     */
    private $gardenDataMapper;

    /**
     * @var GarageDataMapper
     */
    private $garageDataMapper;

    /**
     * @var AlertFrequencyDataMapper
     */
    private $alertFrequencyDataMapper;

    /**
     * @var AuctionsDataMapper
     */
    private $auctionsDataMapper;

    /**
     * @var PropertyFeatureDataMapper
     */
    private $propertyFeatureDataMapper;

    public function __construct(
        SearchTypeDataMapper $searchTypeDataMapper,
        CustomerDataMapper $customerDataMapper,
        TypologyDataMapper $typologyDataMapper,
        CategoryDataMapper $categoryDataMapper,
        ContractDataMapper $contractDataMapper,
        PropertyConditionDataMapper $propertyConditionDataMapper,
        EnergyEfficiencyDataMapper $energyEfficiencyDataMapper,
        GeographyDataMapper $geographyDataMapper,
        IntRangeDataMapper $intRangeDataMapper,
        LicenseDataMapper $licenseDataMapper,
        FloorRangeDataMapper $floorRangeDataMapper,
        GardenDataMapper $gardenDataMapper,
        GarageDataMapper $garageDataMapper,
        AlertFrequencyDataMapper $alertFrequencyDataMapper,
        AuctionsDataMapper $auctionsDataMapper,
        PropertyFeatureDataMapper $propertyFeatureDataMapper
    ) {
        $this->searchTypeDataMapper        = $searchTypeDataMapper;
        $this->customerDataMapper          = $customerDataMapper;
        $this->typologyDataMapper          = $typologyDataMapper;
        $this->categoryDataMapper          = $categoryDataMapper;
        $this->contractDataMapper          = $contractDataMapper;
        $this->propertyConditionDataMapper = $propertyConditionDataMapper;
        $this->energyEfficiencyDataMapper  = $energyEfficiencyDataMapper;
        $this->geographyDataMapper         = $geographyDataMapper;
        $this->intRangeDataMapper          = $intRangeDataMapper;
        $this->licenseDataMapper           = $licenseDataMapper;
        $this->floorRangeDataMapper        = $floorRangeDataMapper;
        $this->gardenDataMapper            = $gardenDataMapper;
        $this->garageDataMapper            = $garageDataMapper;
        $this->alertFrequencyDataMapper    = $alertFrequencyDataMapper;
        $this->auctionsDataMapper          = $auctionsDataMapper;
        $this->propertyFeatureDataMapper   = $propertyFeatureDataMapper;
    }

    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return Search::class;
    }

    /**
     * @param array $data
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('type', function () use ($data) {
                return $this->searchTypeDataMapper->map($data['type']);
            })
            ->forMember('customer', function () use ($data) {
                return $this->customerDataMapper->map($data['customer']);
            })
            ->forMember('typologies', function () use ($data) {
                return array_map(function (array $typology) {
                    return $this->typologyDataMapper->map($typology);
                }, $data['typologies'] ?? []);
            })
            ->forMember('category', function () use ($data) {
                return !empty($data['category']) ?
                    $this->categoryDataMapper->map($data['category']) :
                    null;
            })
            ->forMember('contract', function () use ($data) {
                return !empty($data['contract']) ?
                    $this->contractDataMapper->map($data['contract']) :
                    null;
            })
            ->forMember('propertyCondition', function () use ($data) {
                return !empty($data['propertyCondition']) ?
                    $this->propertyConditionDataMapper->map($data['propertyCondition']) :
                    null;
            })
            ->forMember('energyEfficiency', function () use ($data) {
                return !empty($data['energyEfficiency']) ?
                    $this->energyEfficiencyDataMapper->map($data['energyEfficiency']) :
                    null;
            })
            ->forMember('geography', function () use ($data) {
                return !empty($data['geography']) ?
                    $this->geographyDataMapper->map($data['geography']) :
                    null;
            })
            ->forMember('rooms', function () use ($data) {
                return !empty($data['rooms']) ?
                    $this->intRangeDataMapper->map($data['rooms']) :
                    null;
            })
            ->forMember('price', function () use ($data) {
                return !empty($data['price']) ?
                    $this->intRangeDataMapper->map($data['price']) :
                    null;
            })
            ->forMember('bathrooms', function () use ($data) {
                return !empty($data['bathrooms']) ?
                    $this->intRangeDataMapper->map($data['bathrooms']) :
                    null;
            })
            ->forMember('surface', function () use ($data) {
                return !empty($data['surface']) ?
                    $this->intRangeDataMapper->map($data['surface']) :
                    null;
            })
            ->forMember('licenses', function () use ($data) {
                return !empty($data['licenses']) ? array_map(function (array $license) {
                    return $this->licenseDataMapper->map($license);
                }, $data['licenses']) : null;
            })
            ->forMember('floorRanges', function () use ($data) {
                return !empty($data['floorRanges']) ? array_map(function (array $floorRange) {
                    return $this->floorRangeDataMapper->map($floorRange);
                }, $data['floorRanges']) : null;
            })
            ->forMember('gardens', function () use ($data) {
                return !empty($data['gardens']) ? array_map(function (array $garden) {
                    return $this->gardenDataMapper->map($garden);
                }, $data['gardens']) : null;
            })
            ->forMember('garages', function () use ($data) {
                return !empty($data['garages']) ? array_map(function (array $garage) {
                    return $this->garageDataMapper->map($garage);
                }, $data['garages']) : null;
            })
            ->forMember('alertFrequency', function () use ($data) {
                return !empty($data['alertFrequency']) ? $this->alertFrequencyDataMapper->map($data['alertFrequency']) : null;
            })
            ->forMember('auctions', function () use ($data) {
                return !empty($data['auctions']) ? $this->auctionsDataMapper->map($data['auctions']) : null;
            })
            ->forMember('propertyFeatures', function () use ($data) {
                return !empty($data['propertyFeatures']) ? array_map(function (array $propertyFeature) {
                    return $this->propertyFeatureDataMapper->map($propertyFeature);
                }, $data['propertyFeatures']) : null;
            })
            ->forMember('modificationDate', function () use ($data) {
                return !empty($data['modificationDate']) ?
                    Carbon::parse($data['modificationDate']) :
                    null;
            })
            ->forMember('createdAt', function () use ($data) {
                return !empty($data['createdAt']) ?
                    Carbon::parse($data['createdAt']) :
                    null;
            })
            ->forMember('changedAt', function () use ($data) {
                return !empty($data['changedAt']) ?
                    Carbon::parse($data['changedAt']) :
                    null;
            })
        ;

        return $config;
    }
}
