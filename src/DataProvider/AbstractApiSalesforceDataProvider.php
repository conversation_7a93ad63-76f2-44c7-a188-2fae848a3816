<?php

declare(strict_types=1);

namespace App\DataProvider;

use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;

abstract class AbstractApiSalesforceDataProvider
{
    const DEFAULT_REQUEST_HEADERS = [
        'Accept'          => 'application/json',
        'connect_timeout' => 127,
    ];

    const JSON_REQUEST_HEADERS = [
        'Accept'          => 'application/json',
        'Content-Type'    => 'application/json',
        'connect_timeout' => 127,
    ];

    const MULTIPART_REQUEST_HEADERS = [
        'Accept'          => 'application/json',
        'Content-Type'    => 'multipart/form-data',
        'connect_timeout' => 127,
    ];

    protected $client;
    protected $parametersBag;
    protected $apiBaseurl;

    public function __construct(
        HttpClientHelper $client,
        ParametersBag $parametersBag
    ) {
        $this->client                 = $client;
        $this->parametersBag          = $parametersBag;
        $this->apiBaseurl             = $this->parametersBag->get('app.salesforce_offline_form_endpoint');
    }

    protected function getDefaultHeaders(): array
    {
        return self::DEFAULT_REQUEST_HEADERS;
    }

    protected function getDefaultJsonHeaders(): array
    {
        return self::JSON_REQUEST_HEADERS;
    }

    protected function getDefaultMultipartHeaders(): array
    {
        return self::MULTIPART_REQUEST_HEADERS;
    }
}
