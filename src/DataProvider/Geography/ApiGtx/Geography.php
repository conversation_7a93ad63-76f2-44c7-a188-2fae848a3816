<?php

declare(strict_types=1);

namespace App\DataProvider\Geography\ApiGtx;

use App\Constants\Base\MessagingConstants;
use App\Constants\PerformanceProfiler;
use App\DataProvider\AbstractApiGtxDataProvider;
use App\DataProvider\Geography\ApiGtx\DataMapper\CityListDataMapper;
use App\DataProvider\Geography\ApiGtx\DataMapper\SuggestionListDataMapper;
use App\DataProvider\Geography\GeographyInterface;
use App\DTO\DataProvider\Geography\ApiGtx\GetCitiesRequest;
use App\DTO\DataProvider\Geography\ApiGtx\GetSuggestionsRequest;
use App\DTO\DataProvider\Geography\ApiGtx\PaginationRequest;
use App\DTO\DataProvider\Geography\ApiGtx\SortingRequest;
use App\Exception\ApiException;
use App\Helper\ApiWsseHeaderGenerator;
use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;
use App\Model\Geography\CityList;
use App\Model\Geography\SuggestionList;
use App\Performance\ProfilerInterface;
use App\Utils\SessionUtils;
use Symfony\Component\HttpFoundation\Response;

class Geography extends AbstractApiGtxDataProvider implements GeographyInterface
{
    const RESOURCE_GEOGRAPHY = 'geography';
    const RESOURCE_CITIES = 'cities';
    const RESOURCE_SUGGESTIONS = 'suggestions';

    private CityListDataMapper $cityListDataMapper;
    private SuggestionListDataMapper $suggestionListDataMapper;

    public function __construct(
        ApiWsseHeaderGenerator $apiWsseHeaderGenerator,
        CityListDataMapper $cityListDataMapper,
        HttpClientHelper $client,
        ParametersBag $parametersBag,
        ProfilerInterface $performanceProfiler,
        SuggestionListDataMapper $suggestionListDataMapper,
        SessionUtils $sessionUtils
    ) {
        parent::__construct($apiWsseHeaderGenerator, $client, $parametersBag, $performanceProfiler, $sessionUtils);

        $this->cityListDataMapper = $cityListDataMapper;
        $this->suggestionListDataMapper = $suggestionListDataMapper;
    }

    /**
     * @throws ApiException
     */
    public function getCities(
        GetCitiesRequest $getCitiesRequest,
        PaginationRequest $paginationRequest,
        SortingRequest $sortingRequest
    ): CityList {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s',
            $this->getBaseUri(),
            self::RESOURCE_GEOGRAPHY,
            self::RESOURCE_CITIES
        );

        $response = $this->client->execRequestWithResponse(
            'GET',
            $url,
            $this->prepareRequestPayload([
                $getCitiesRequest,
                $paginationRequest,
                $sortingRequest,
            ]),
            $this->getDefaultHeaders()
        );

        if (null === $response || Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $headers = $response->getHeaders();
        $decoded = \json_decode($response->getBody()->getContents(), true);

        $totalCountKey = \array_key_exists('X-Total-Count', $headers) ? 'X-Total-Count' : 'x-total-count';
        $resultsCount = !empty($headers[$totalCountKey]) ? \reset($headers[$totalCountKey]) : 0;

        $this->performanceProfiler->stop(__METHOD__);

        return $this->cityListDataMapper
            ->withContextData([
                SuggestionListDataMapper::CONTEXT_RESULTS_COUNT => $resultsCount,
                SuggestionListDataMapper::CONTEXT_PAGINATION_REQUEST => $paginationRequest,
            ])
            ->map($decoded['data']);
    }

    /**
     * @throws ApiException
     */
    public function getSuggestions(
        GetSuggestionsRequest $getSuggestionsRequest,
        PaginationRequest $paginationRequest,
        SortingRequest $sortingRequest
    ): SuggestionList {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s',
            $this->getBaseUri(),
            self::RESOURCE_GEOGRAPHY,
            self::RESOURCE_SUGGESTIONS
        );

        $response = $this->client->execRequestWithResponse(
            'GET',
            $url,
            $this->prepareRequestPayload([
                $getSuggestionsRequest,
                $paginationRequest,
                $sortingRequest,
            ]),
            $this->getDefaultHeaders()
        );

        if (null === $response || Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $headers = $response->getHeaders();
        $decoded = \json_decode($response->getBody()->getContents(), true);

        $totalCountKey = \array_key_exists('X-Total-Count', $headers) ? 'X-Total-Count' : 'x-total-count';
        $resultsCount = !empty($headers[$totalCountKey]) ? \reset($headers[$totalCountKey]) : 0;

        $this->performanceProfiler->stop(__METHOD__);

        return $this->suggestionListDataMapper
            ->withContextData([
                SuggestionListDataMapper::CONTEXT_RESULTS_COUNT => $resultsCount,
                SuggestionListDataMapper::CONTEXT_PAGINATION_REQUEST => $paginationRequest,
            ])
            ->map($decoded['data']);
    }
}
