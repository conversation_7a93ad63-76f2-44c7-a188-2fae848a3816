<?php

declare(strict_types=1);

namespace App\DataProvider\Geography\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\DTO\DataProvider\Geography\ApiGtx\PaginationRequest;
use App\Model\Shared\Pagination;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class PaginationDataMapper extends AbstractDataMapper
{
    const CONTEXT_RESULTS_COUNT      = 'total_count';
    const CONTEXT_PAGINATION_REQUEST = 'pagination_request';

    protected function getDestinationClass(): string
    {
        return Pagination::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        /**
         * @var array $data
         */
        $config = $this->getObjectMapperConfig();

        $pagination   = $this->resolvePaginationRequestFromContext();
        $resultsCount = $this->resolveResultsCountFromContext();

        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('results', function () use ($pagination) {
                return $pagination->getLimit();
            })
            ->forMember('page', function () use ($pagination, $resultsCount) {
                return $pagination->getOffset() >= $resultsCount ? 1 : (int) ($pagination->getOffset() / $pagination->getLimit()) + 1;
            })
        ;

        return $config;
    }

    private function resolveResultsCountFromContext(): int
    {
        try {
            return (int) self::getContextDataByKey(self::CONTEXT_RESULTS_COUNT);
        } catch (\Throwable $throwable) {
            return 0;
        }
    }

    private function resolvePaginationRequestFromContext(): PaginationRequest
    {
        try {
            return self::getContextDataByKey(self::CONTEXT_PAGINATION_REQUEST);
        } catch (\Throwable $throwable) {
            return new PaginationRequest();
        }
    }
}
