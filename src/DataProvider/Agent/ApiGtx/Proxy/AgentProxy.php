<?php

declare(strict_types=1);

namespace App\DataProvider\Agent\ApiGtx\Proxy;

use App\DataProvider\AbstractMeasurementProxy;
use App\DataProvider\Agent\AgentInterface;
use App\DataProvider\Agent\ApiGtx\Agent;
use App\DTO\DataProvider\Agent\ApiGtx\ListAgentsRequest;
use App\Model\Agent\AgentsList;
use App\Performance\ProfilerInterface;
use App\Resolver\StatsNameResolver;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class AgentProxy extends AbstractMeasurementProxy implements AgentInterface
{
    public function __construct(
        EventDispatcherInterface $eventDispatcher,
        StatsNameResolver $statsNameResolver,
        Agent $dataProvider,
        ProfilerInterface $profiler
    ) {
        parent::__construct($eventDispatcher, $statsNameResolver, $dataProvider, $profiler);
    }

    /**
     * @throws \Throwable
     */
    public function listAgents(
        int $agencyId,
        ListAgentsRequest $listAgentsRequest
    ): AgentsList {
        return $this->execute(__FUNCTION__, \func_get_args());
    }
}
