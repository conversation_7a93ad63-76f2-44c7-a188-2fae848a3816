<?php

declare(strict_types=1);

namespace App\DataProvider\Search\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Property\PropertyOwnership;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class PropertyOwnershipDataMapper extends AbstractDataMapper
{
    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return PropertyOwnership::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var array $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
        ;

        return $config;
    }
}
