<?php

declare(strict_types=1);

namespace App\DataProvider\Search\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Searches\ProposedAdProperty;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class ProposedAdPropertyDataMapper extends AbstractDataMapper
{
    /**
     * @var \App\DataProvider\Property\ApiGtx\DataMapper\GeographyInformationDataMapper
     */
    private $geographyInformationDataMapper;

    public function __construct(
        \App\DataProvider\Property\ApiGtx\DataMapper\GeographyInformationDataMapper $geographyInformationDataMapper
    ) {
        $this->geographyInformationDataMapper = $geographyInformationDataMapper;
    }

    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return ProposedAdProperty::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var array $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('geographyInformation', function () use ($data) {
                return null !== $data['geographyInformation'] ?
                    $this->geographyInformationDataMapper->map($data['geographyInformation']) :
                    null
                ;
            })
        ;

        return $config;
    }
}
