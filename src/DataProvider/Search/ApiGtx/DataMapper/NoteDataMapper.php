<?php

declare(strict_types=1);

namespace App\DataProvider\Search\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Searches\Note;
use Carbon\Carbon;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class NoteDataMapper extends AbstractDataMapper
{
    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return Note::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var array $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('date', function () use ($data) {
                return null !== $data['date'] ? Carbon::parse($data['date'], 'UTC')->setTimezone('Europe/Rome') : null;
            })
        ;

        return $config;
    }
}
