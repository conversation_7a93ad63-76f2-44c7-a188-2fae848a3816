<?php

declare(strict_types=1);

namespace App\DataProvider\Property\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Property\CoWorkingTimetable;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class CoWorkingTimetableDataMapper extends AbstractDataMapper
{
    protected function getDestinationClass(): string
    {
        return CoWorkingTimetable::class;
    }

    protected function getConfiguration($data): ObjectMapperConfig
    {
        /**
         * @var array $data
         */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('openTime', function () use ($data) {
                return !empty($data['openTime']) ? (string) $data['openTime'] : null;
            })
            ->forMember('closeTime', function () use ($data) {
                return !empty($data['closeTime']) ? (string) $data['closeTime'] : null;
            });

        return $config;
    }
}
