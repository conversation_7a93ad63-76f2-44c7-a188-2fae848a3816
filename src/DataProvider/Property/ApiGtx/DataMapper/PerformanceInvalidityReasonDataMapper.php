<?php

declare(strict_types=1);

namespace App\DataProvider\Property\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Property\PerformanceInvalidityReason;

class PerformanceInvalidityReasonDataMapper extends AbstractDataMapper
{
    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return PerformanceInvalidityReason::class;
    }
}
