<?php

declare(strict_types=1);

namespace App\DataProvider\Lookup\ApiGtx\Proxy;

use App\DataProvider\AbstractMeasurementProxy;
use App\DataProvider\Lookup\ApiGtx\Lookup;
use App\DataProvider\Lookup\LookupInterface;
use App\Model\Lookup\InternationalPhonePrefixes\InternationalPhonePrefixResponse;
use App\Model\Lookup\LookupResult;
use App\Model\Lookup\PropertyBillboardColor\PropertyBillboardColorResponse;
use App\Model\Request\Lookup\CategoriesLookupRequest;
use App\Performance\ProfilerInterface;
use App\Resolver\StatsNameResolver;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class LookupProxy extends AbstractMeasurementProxy implements LookupInterface
{
    public function __construct(
        EventDispatcherInterface $eventDispatcher,
        StatsNameResolver $statsNameResolver,
        Lookup $dataProvider,
        ProfilerInterface $profiler
    ) {
        parent::__construct($eventDispatcher, $statsNameResolver, $dataProvider, $profiler);
    }

    /**
     * {@inheritDoc}
     */
    public function getFranchising(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getAssociations(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getCountries(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getProvincesByCountry(string $countryId): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getRegionsByCountry(string $countryId): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getProvincesByRegion(string $regionId): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getCitiesByProvince(string $provinceId): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getZonesByCity(string $cityId, string $sort = null): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getInternationalPhonePrefixes(): InternationalPhonePrefixResponse
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getTypologiesByCategoryId(int $categoryId, int $version = null): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getSubTypologiesByTypologyId(int $typologyId): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getEstimateTypologies(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getEstimateCities(int $agencyId): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getEstimateOutcomes(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getFloorRanges(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getAgencyProvinces(int $agencyId): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getPrivateAdsCategories(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getPrivateAdsPortals(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getSalesRequestOutcomes(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getPropertyOwnerships(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getPropertyConditions(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getHeatings(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getLicenses(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getAgencyLanguages(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    public function getAgencyDescriptionLanguages(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getPerformanceRelativeIndexRange(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getCategories(CategoriesLookupRequest $request): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getTypologies(int $version = null): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getContractsByCategoryId(int $categoryId): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getPropertyBillboardTypes(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getPropertyBillboardOrientations(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    public function getPropertyDescriptionLanguages(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getPropertyBillboardColors(): PropertyBillboardColorResponse
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getMultimediaFloors(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }

    /**
     * {@inheritDoc}
     */
    public function getImageTagLabels(): LookupResult
    {
        return $this->execute(__FUNCTION__, \func_get_args());
    }
}
