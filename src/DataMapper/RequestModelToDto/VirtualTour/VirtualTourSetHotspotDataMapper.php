<?php

declare(strict_types=1);

namespace App\DataMapper\RequestModelToDto\VirtualTour;

use App\DataMapper\AbstractDataMapper;
use App\DTO\VirtualTour\VirtualTourSetHotspot;
use ObjectMapper\Configuration\ObjectMapperConfig;

class VirtualTourSetHotspotDataMapper extends AbstractDataMapper
{
    /** @var CoordinateDataMapper */
    private $coordinatesDataMapper;

    public function __construct(CoordinateDataMapper $coordinatesDataMapper)
    {
        $this->coordinatesDataMapper = $coordinatesDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return VirtualTourSetHotspot::class;
    }

    /**
     * {@inheritdoc}
     *
     * @param \App\Model\Request\VirtualTour\VirtualTourSetHotspotRequest $data
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(\get_class($data), $this->getDestinationClass())
            ->forMember('targetImage360Id', function () use ($data) {
                return $data->getTargetImageId() ?? null;
            })
            ->forMember('coordinates', function () use ($data) {
                return $this->coordinatesDataMapper->map($data->getCoordinates());
            })
        ;

        return $config;
    }
}
