<?php

declare(strict_types=1);

namespace App\DataMapper\RequestDataToRequestModel\Property;

use App\DataMapper\AbstractDataMapper;
use App\Model\Request\Property\BillboardDescriptionRequest;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class BillboardDescriptionDataMapper extends AbstractDataMapper
{
    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return BillboardDescriptionRequest::class;
    }

    /**
     * {@inheritdoc}
     *
     * @param array $data
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('text', function () use ($data) {
                return $data['description']['text'];
            })
            ->forMember('alignment', function () use ($data) {
                return $data['description']['alignment'];
            })
            ->forMember('size', function () use ($data) {
                return $data['description']['size'];
            })
        ;

        return $config;
    }
}
