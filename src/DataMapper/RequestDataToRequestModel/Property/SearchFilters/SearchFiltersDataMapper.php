<?php

declare(strict_types=1);

namespace App\DataMapper\RequestDataToRequestModel\Property\SearchFilters;

use App\DataMapper\AbstractDataMapper;
use App\DataMapper\RequestDataToRequestModel\Shared\RangeDataMapper;
use App\Model\Request\Property\SearchFilters\SearchFiltersRequest;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class SearchFiltersDataMapper extends AbstractDataMapper
{
    /**
     * @var RangeDataMapper
     */
    private $rangeDataMapper;

    /**
     * @var PortalFiltersDataMapper
     */
    private $portalFiltersDataMapper;

    /**
     * @var TipologiesFiltersDataMapper
     */
    private $tipologiesFiltersDataMapper;

    public function __construct(
        RangeDataMapper $rangeDataMapper,
        PortalFiltersDataMapper $portalFiltersDataMapper,
        TipologiesFiltersDataMapper $tipologiesFiltersDataMapper
    ) {
        $this->rangeDataMapper             = $rangeDataMapper;
        $this->portalFiltersDataMapper     = $portalFiltersDataMapper;
        $this->tipologiesFiltersDataMapper = $tipologiesFiltersDataMapper;
    }

    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return SearchFiltersRequest::class;
    }

    /**
     * {@inheritdoc}
     *
     * @param array $data
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('price', function () use ($data) {
                return $this->rangeDataMapper->map([
                    'min' => $data['price_from'] ?? null,
                    'max' => $data['price_to'] ?? null,
                ]);
            })
            ->forMember('surface', function () use ($data) {
                return $this->rangeDataMapper->map([
                    'min' => $data['surface_from'] ?? null,
                    'max' => $data['surface_to'] ?? null,
                ]);
            })
            ->forMember('portals', function () use ($data) {
                return $this->portalFiltersDataMapper->map($data);
            })
            ->forMember('tipologies', function () use ($data) {
                return $this->tipologiesFiltersDataMapper->map($data);
            })
        ;

        return $config;
    }
}
