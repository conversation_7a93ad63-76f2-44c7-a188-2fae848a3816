<?php

declare(strict_types=1);

namespace App\DataMapper\RequestDataToRequestModel\Property;

use App\Constants\Base\AdStatsConstants;
use App\DataMapper\AbstractDataMapper;
use App\Model\Request\Property\ListAdsStatsRequest;
use InvalidArgumentException;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class ListAdsStatsRequestDataMapper extends AbstractDataMapper
{
    protected function getDestinationClass(): string
    {
        return ListAdsStatsRequest::class;
    }

    /**
     * {@inheritdoc}
     *
     * @param array $data
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('start', function () use ($data) {
                return self::resolveDateTime($data['start']);
            })
            ->forMember('end', function () use ($data) {
                return self::resolveDateTime($data['end']);
            })
            ->forMember('details', function () use ($data) {
                return isset($data['details']) ?
                    filter_var($data['details'], \FILTER_VALIDATE_BOOLEAN) :
                    null;
            })
            ->forMember('statTypes', function () use ($data) {
                return self::resolveStatsList($data['statTypes']);
            })
        ;

        return $config;
    }

    /**
     * @throws InvalidArgumentException
     */
    private static function resolveDateTime(
        string $data = null
    ): \DateTime {
        try {
            return new \DateTime($data);
        } catch (\Throwable $t) {
            throw new InvalidArgumentException(sprintf(AdStatsConstants::EXCEPTION_MESSAGES['invalid_datetime_supplied'], $data));
        }
    }

    /**
     * @throws InvalidArgumentException
     */
    private static function resolveStatsList(
        string $data = null
    ): array {
        if (empty($data)) {
            return [];
        }

        $fields = explode(',', $data);

        $fieldsList = array_map(static function ($field) {
            return trim($field);
        }, $fields);

        foreach ($fieldsList as $item) {
            if (!\in_array($item, AdStatsConstants::AVAILABLE_STAT_TYPES)) {
                throw new InvalidArgumentException(AdStatsConstants::EXCEPTION_MESSAGES['invalid_stat_supplied']);
            }
        }

        return $fieldsList;
    }
}
