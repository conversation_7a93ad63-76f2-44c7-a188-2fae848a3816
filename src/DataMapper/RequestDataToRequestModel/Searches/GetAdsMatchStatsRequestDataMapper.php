<?php

declare(strict_types=1);

namespace App\DataMapper\RequestDataToRequestModel\Searches;

use App\DataMapper\AbstractDataMapper;
use App\Model\Request\Searches\GetAdsMatchStatsRequest;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class GetAdsMatchStatsRequestDataMapper extends AbstractDataMapper
{
    protected function getDestinationClass(): string
    {
        return GetAdsMatchStatsRequest::class;
    }

    /**
     * @param array $data
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('propertyIds', function () use ($data) {
                if (empty($data['propertyIds'])) {
                    return null;
                }

                return \is_array($data['propertyIds']) ? $data['propertyIds'] : explode(',', $data['propertyIds']);
            })
        ;

        return $config;
    }
}
