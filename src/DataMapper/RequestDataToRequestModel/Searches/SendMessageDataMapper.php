<?php

declare(strict_types=1);

namespace App\DataMapper\RequestDataToRequestModel\Searches;

use App\DataMapper\AbstractDataMapper;
use App\Model\Request\Searches\SendMessageRequest;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class SendMessageDataMapper extends AbstractDataMapper
{
    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return SendMessageRequest::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var array $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
        ;

        return $config;
    }
}
