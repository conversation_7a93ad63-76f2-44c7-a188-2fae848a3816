<?php

declare(strict_types=1);

namespace App\DataMapper\RequestDataToRequestModel\VirtualTour;

use App\DataMapper\AbstractDataMapper;
use App\Model\Request\VirtualTour\VirtualTourSetImageRequest;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;

class VirtualTourSetImageDataMapper extends AbstractDataMapper
{
    protected function getDestinationClass(): string
    {
        return VirtualTourSetImageRequest::class;
    }

    /**
     * {@inheritdoc}
     *
     * @param Request $data
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        /** @var UploadedFile $file */
        $file = $data->files->get('image');

        $data = $data->request->all();

        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('adMultimediaFloorId', function () use ($data) {
                return $data['adMultimediaFloorId'] ?? null;
            })
            ->forMember('file', function () use ($file) {
                return $file;
            })
            ->forMember('caption', function () use ($data) {
                return $data['caption'] ?? null;
            })
        ;

        return $config;
    }
}
