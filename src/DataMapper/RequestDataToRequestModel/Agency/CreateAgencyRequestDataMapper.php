<?php

declare(strict_types=1);

namespace App\DataMapper\RequestDataToRequestModel\Agency;

use App\DataMapper\AbstractDataMapper;
use App\Model\Request\Agency\CreateAgencyRequest;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class CreateAgencyRequestDataMapper extends AbstractDataMapper
{
    protected function getDestinationClass(): string
    {
        return CreateAgencyRequest::class;
    }

    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('companyType', function () use ($data) {
                return isset($data['tipoimpresa']) ? $data['tipoimpresa'] : null;
            })
            ->forMember('firstName', function () use ($data) {
                return !empty($data['nome']) ? trim(strip_tags($data['nome'])) : null;
            })
            ->forMember('lastName', function () use ($data) {
                return !empty($data['cognome']) ? trim(strip_tags($data['cognome'])) : null;
            })
            ->forMember('companyName', function () use ($data) {
                return !empty($data['nomesocieta']) ? trim(strip_tags($data['nomesocieta'])) : null;
            })
            ->forMember('email', function () use ($data) {
                return !empty($data['email']) ? trim(strip_tags($data['email'])) : null;
            })
            ->forMember('cityId', function () use ($data) {
                return !empty($data['idComune']) ? (int) $data['idComune'] : null;
            })
            ->forMember('phoneNumber', function () use ($data) {
                return !empty($data['telefono']) ? trim(strip_tags($data['telefono'])) : null;
            })
            ->forMember('phonePrefix', function () use ($data) {
                return !empty($data['prefisso']) ? trim(strip_tags($data['prefisso'])) : null;
            })
            ->forMember('privacy', function () use ($data) {
                return isset($data['privacy']) ? (bool) $data['privacy'] : false;
            })
            ->forMember('vatNumber', function () use ($data) {
                return isset($data['piva']) ? trim(strip_tags((string) $data['piva'])) : null;
            })
        ;

        return $config;
    }
}
