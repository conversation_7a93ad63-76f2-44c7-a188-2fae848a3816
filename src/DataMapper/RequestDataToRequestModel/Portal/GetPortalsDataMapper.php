<?php

declare(strict_types=1);

namespace App\DataMapper\RequestDataToRequestModel\Portal;

use App\DataMapper\AbstractDataMapper;
use App\Model\Request\Portal\GetPortalsRequest;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class GetPortalsDataMapper extends AbstractDataMapper
{
    protected function getDestinationClass(): string
    {
        return GetPortalsRequest::class;
    }

    /**
     * {@inheritdoc}
     *
     * @param array $data
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('context', function () use ($data) {
                if (empty($data['context'])) {
                    return null;
                }

                return 'properties' === $data['context'] ? 'ads' : $data['context'];
            })
        ;

        return $config;
    }
}
