<?php

namespace App\DataMapper\EntityToResponse\Searches\Search\Mapping;

class SearchStatusResolver
{
    /**
     * @var SearchStatusMapping
     */
    private $mapping;

    public function __construct(
        SearchStatusMapping $mapping
    ) {
        $this->mapping = $mapping;
    }

    public function resolve(int $value): string
    {
        return $this->mapping
            ->getMapping($value);
    }

    /**
     * @return false|int
     */
    public function reverse(string $value)
    {
        return array_search($value, SearchStatusMapping::MAPPING, true);
    }
}
