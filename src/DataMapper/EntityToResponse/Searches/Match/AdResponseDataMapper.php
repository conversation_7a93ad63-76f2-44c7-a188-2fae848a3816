<?php

declare(strict_types=1);

namespace App\DataMapper\EntityToResponse\Searches\Match;

use App\Constants\Base\PropertyConstants;
use App\DataMapper\AbstractDataMapper;
use App\Formatter\PropertyImageFormatter;
use App\Model\Match\Ad;
use App\Model\Response\Searches\Match\AdResponse;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\MappingOperation\Operation;

class AdResponseDataMapper extends AbstractDataMapper
{
    const TAG_AD_PUBLISHED     = 'PUBLISHED';
    const TAG_AD_NOT_PUBLISHED = 'NOT_PUBLISHED';

    /** @var PropertyImageFormatter */
    private $propertyImageFormatter;

    /**
     * @var AdGeographyInformationResponseDataMapper
     */
    private $adGeographyInformationResponseDataMapper;

    public function __construct(
        PropertyImageFormatter $propertyImageFormatter,
        AdGeographyInformationResponseDataMapper $adGeographyInformationResponseDataMapper
    ) {
        $this->propertyImageFormatter                   = $propertyImageFormatter;
        $this->adGeographyInformationResponseDataMapper = $adGeographyInformationResponseDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return AdResponse::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        /** @var Ad $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(\get_class($data), $this->getDestinationClass())
            ->forMember('ref', function () use ($data) {
                return !empty($data->getCode()) ? $data->getCode() : $data->getId();
            })
            ->forMember('image', Operation::fromProperty('mainImage'))
            ->forMember('thumbUrl', function () use ($data) {
                return null !== $data->getMainImage() ?
                    $this->propertyImageFormatter->getThumbUrl($data->getMainImage()) :
                    null;
            })
            ->forMember('geographyInformation', function () use ($data) {
                return null !== $data->getGeographyInformation() ?
                    $this->adGeographyInformationResponseDataMapper->map($data->getGeographyInformation()) :
                    null;
            })
            ->forMember('tags', function () use ($data) {
                $adPublicationTag = (PropertyConstants::STATUS_IMMOBILIARE_ACTIVE === $data->getStatus()->getId())
                    ? self::TAG_AD_PUBLISHED
                    : self::TAG_AD_NOT_PUBLISHED;

                return [$adPublicationTag];
            })
        ;

        return $config;
    }
}
