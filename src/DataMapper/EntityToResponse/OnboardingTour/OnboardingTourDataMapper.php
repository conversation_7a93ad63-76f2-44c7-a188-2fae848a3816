<?php

declare(strict_types=1);

namespace App\DataMapper\EntityToResponse\OnboardingTour;

use App\DataMapper\AbstractDataMapper;
use App\Model\OnboardingTour\OnboardingTourStep;
use App\Model\Response\OnboardingTour\OnboardingTour;
use ObjectMapper\Configuration\ObjectMapperConfig;

class OnboardingTourDataMapper extends AbstractDataMapper
{
    private OnboardingTourStepDataMapper $onboardingTourStepDataMapper;

    public function __construct(
        OnboardingTourStepDataMapper $onboardingTourStepDataMapper
    ) {
        $this->onboardingTourStepDataMapper = $onboardingTourStepDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return OnboardingTour::class;
    }

    /**
     * {@inheritdoc}
     *
     * @param \App\Model\OnboardingTour\OnboardingTour $data
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(\get_class($data), $this->getDestinationClass())
            ->forMember('steps', function () use ($data) {
                return \array_map(function (OnboardingTourStep $step) {
                    return $this->onboardingTourStepDataMapper->map($step);
                }, $data->getSteps() ?? []);
            })
        ;

        return $config;
    }
}
