<?php

declare(strict_types=1);

namespace App\DataMapper\EntityToResponse\Messaging\Thread;

use App\DataMapper\AbstractDataMapper;
use App\Model\Messaging\Stats;
use App\Model\Response\Messaging\Stats\StatsResponse;
use ObjectMapper\Configuration\ObjectMapperConfig;

class StatsDataMapper extends AbstractDataMapper
{
    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return StatsResponse::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var Stats $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(\get_class($data), $this->getDestinationClass())
            ->forMember('lastReceivedMessageDate', function () use ($data) {
                return null !== $data->getLastReceivedMessageDate() ?
                    $data->getLastReceivedMessageDate()->format('Y-m-d H:i:s') :
                    null;
            })
            ->forMember('lastRepliedMessageDate', function () use ($data) {
                return null !== $data->getLastRepliedMessageDate() ?
                    $data->getLastRepliedMessageDate()->format('Y-m-d H:i:s') :
                    null;
            })
            ->forMember('firstReceivedMessageDate', function () use ($data) {
                return null !== $data->getFirstReceivedMessageDate() ?
                    $data->getFirstReceivedMessageDate()->format('Y-m-d H:i:s') :
                    null;
            })
            ->forMember('firstRepliedMessageDate', function () use ($data) {
                return null !== $data->getFirstRepliedMessageDate() ?
                    $data->getFirstRepliedMessageDate()->format('Y-m-d H:i:s') :
                    null;
            })
        ;

        return $config;
    }
}
