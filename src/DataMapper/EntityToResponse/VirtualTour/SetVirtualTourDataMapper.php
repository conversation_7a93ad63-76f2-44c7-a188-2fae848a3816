<?php

declare(strict_types=1);

namespace App\DataMapper\EntityToResponse\VirtualTour;

use App\DataMapper\AbstractDataMapper;
use App\Model\Response\VirtualTour\SetVirtualTourResponse;
use ObjectMapper\Configuration\ObjectMapperConfig;

class SetVirtualTourDataMapper extends AbstractDataMapper
{
    /** @var CoordinateDataMapper */
    private $coordinatesDataMapper;

    public function __construct(
        CoordinateDataMapper $coordinatesDataMapper
    ) {
        $this->coordinatesDataMapper = $coordinatesDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return SetVirtualTourResponse::class;
    }

    /**
     * @param \App\Model\VirtualTour\VirtualTourEntity $data
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(\get_class($data), $this->getDestinationClass())
            ->forMember('id', function () use ($data) {
                return $data->getId();
            })
            ->forMember('mainRaw360Id', function () use ($data) {
                return $data->getMainRaw360Id();
            })
            ->forMember('coordinates', function () use ($data) {
                return !empty($data->getCoordinates())
                    ? $this->coordinatesDataMapper->map($data->getCoordinates())
                    : null;
            })
        ;

        return $config;
    }
}
