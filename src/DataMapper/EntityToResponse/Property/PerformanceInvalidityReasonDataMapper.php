<?php

declare(strict_types=1);

namespace App\DataMapper\EntityToResponse\Property;

use App\DataMapper\AbstractDataMapper;
use App\Model\Response\Property\PerformanceInvalidityReasonResponse;

class PerformanceInvalidityReasonDataMapper extends AbstractDataMapper
{
    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return PerformanceInvalidityReasonResponse::class;
    }
}
