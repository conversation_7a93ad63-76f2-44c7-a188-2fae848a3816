<?php

declare(strict_types=1);

namespace App\DataMapper\EntityToResponse\Property;

use App\DataMapper\AbstractDataMapper;
use App\Formatter\Shared\DateFormatter;
use App\Model\Response\Property\SharedApartmentInformationResponse;
use App\Utils\TranslatorTrait;
use App\Utils\ValuteTrait;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;
use Symfony\Component\Translation\TranslatorInterface;

class SharedApartmentInformationDataMapper extends AbstractDataMapper
{
    use TranslatorTrait;
    use ValuteTrait;

    const FURNISHED_PROPERTY = 1;
    const NOT_FURNISHED_PROPERTY = 2;
    const LAUNDRY_PROPERTY = 3;
    const PRIVATE_BATHROOM_PROPERTY = 4;
    const SHARED_BATHROOM_PROPERTY = 5;
    const PARQUERT_PROPERTY = 6;
    const PHONE_PROPERTY = 7;
    const TV_PROPERTY = 8;
    const AIR_CONDITIONED_PROPERTY = 9;
    const PRIVATE_ENTRANCE_PROPERTY = 10;
    const WARDROBE_PROPERTY = 11;
    const MOQUETTE_PROPERTY = 12;

    const ALARM_ACCESSORY = 1;
    const INTERNET_ACCESSORY = 2;
    const FIREPLACE_ACCESSORY = 3;
    const UTILITY_ROOM_ACCESSORY = 4;
    const GARDEN_ACCESSORY = 5;
    const SWIMMING_POOL_ACCESSORY = 6;
    const TENNIS_COURT_ACCESSORY = 7;
    const GYM_ACCESSORY = 8;
    const WASHING_MACHINE_ACCESSORY = 9;
    const DISHWASHER_ACCESSORY = 10;
    const MICROWAVE_ACCESSORY = 11;
    const SATELLITE_TV_ACCESSORY = 12;
    const BALCONY_ACCESSORY = 13;
    const ELEVATOR_ACCESSORY = 14;
    const CONCIERGE_SRVICE_ACCESSORY = 15;
    const KITCHEN_ACCESSORY = 16;
    const TERRACE_ACCESSORY = 17;

    const MALE_GENDER = 1;
    const FEMALE_GENDER = 2;

    const SMOKER_ALLOWED = 1;
    const SMOKER_NOT_ALLOWED = 2;
    const SMOKER_NOT_IMPORTANT = 3;

    const COUPLES_ALLOWED = 1;
    const COUPLES_NOT_ALLOWED = 2;
    const COUPLES_NOT_IMPORTANT = 3;

    const ANIMALS_ALLOWED = 1;
    const ANIMALS_NOT_ALLOWED = 2;
    const ANIMALS_NOT_IMPORTANT = 3;
    const CAT_ALLOWED = 4;
    const DOG_ALLOWED = 5;

    protected DateFormatter $dateFormatter;
    private TranslatorInterface $translator;

    public function __construct(
        DateFormatter $dateFormatter,
        TranslatorInterface $translator
    ) {
        $this->dateFormatter = $dateFormatter;
        $this->translator = $translator;
    }

    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return SharedApartmentInformationResponse::class;
    }

    protected function isFeatureActive(int $id, array $features = null): ?string
    {
        if (empty($features)) {
            return null;
        }

        foreach ($features as $item) {
            if ($item['id'] === $id) {
                return $this->translateCms('label.yes');
            }
        }

        return null;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var array $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('terrace', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::TERRACE_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('kitchen', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::KITCHEN_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('conciergeService', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::CONCIERGE_SRVICE_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('elevator', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::ELEVATOR_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('balcony', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::BALCONY_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('satelliteTv', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::SATELLITE_TV_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('microwave', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::MICROWAVE_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('dishwasher', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::DISHWASHER_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('washingMachine', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::WASHING_MACHINE_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('gym', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::GYM_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('tennisCourt', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::TENNIS_COURT_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('swimmingPool', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::SWIMMING_POOL_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('garden', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::GARDEN_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('utilityRoom', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::UTILITY_ROOM_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('firePlace', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::FIREPLACE_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('internet', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::INTERNET_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('alarmSystem', function () use ($data) {
                return !empty($data['roomAccessories'])
                    ? $this->isFeatureActive(self::ALARM_ACCESSORY, $data['roomAccessories'])
                    : null;
            })
            ->forMember('moquette', function () use ($data) {
                return !empty($data['roomFacilities'])
                    ? $this->isFeatureActive(self::MOQUETTE_PROPERTY, $data['roomFacilities'])
                    : null;
            })
            ->forMember('wardrobe', function () use ($data) {
                return !empty($data['roomFacilities'])
                    ? $this->isFeatureActive(self::WARDROBE_PROPERTY, $data['roomFacilities'])
                    : null;
            })
            ->forMember('privateEntrance', function () use ($data) {
                return !empty($data['roomFacilities'])
                    ? $this->isFeatureActive(self::PRIVATE_ENTRANCE_PROPERTY, $data['roomFacilities'])
                    : null;
            })
            ->forMember('airConditioned', function () use ($data) {
                return !empty($data['roomFacilities'])
                    ? $this->isFeatureActive(self::AIR_CONDITIONED_PROPERTY, $data['roomFacilities'])
                    : null;
            })
            ->forMember('tv', function () use ($data) {
                return !empty($data['roomFacilities'])
                    ? $this->isFeatureActive(self::TV_PROPERTY, $data['roomFacilities'])
                    : null;
            })
            ->forMember('phone', function () use ($data) {
                return !empty($data['roomFacilities'])
                    ? $this->isFeatureActive(self::PHONE_PROPERTY, $data['roomFacilities'])
                    : null;
            })
            ->forMember('parquet', function () use ($data) {
                return !empty($data['roomFacilities'])
                    ? $this->isFeatureActive(self::PARQUERT_PROPERTY, $data['roomFacilities'])
                    : null;
            })
            ->forMember('laundry', function () use ($data) {
                return !empty($data['roomFacilities'])
                    ? $this->isFeatureActive(self::LAUNDRY_PROPERTY, $data['roomFacilities'])
                    : null;
            })
            ->forMember('notFurnished', function () use ($data) {
                return !empty($data['roomFacilities'])
                    ? $this->isFeatureActive(self::NOT_FURNISHED_PROPERTY, $data['roomFacilities'])
                    : null;
            })
            ->forMember('furnished', function () use ($data) {
                return !empty($data['roomFacilities'])
                    ? $this->isFeatureActive(self::FURNISHED_PROPERTY, $data['roomFacilities'])
                    : null;
            })
            ->forMember('sharedBathroom', function () use ($data) {
                return !empty($data['roomFacilities']) ? $this->isFeatureActive(self::SHARED_BATHROOM_PROPERTY, $data['roomFacilities']) : null;
            })
            ->forMember('privateBathRoom', function () use ($data) {
                return !empty($data['roomFacilities']) ? $this->isFeatureActive(self::PRIVATE_BATHROOM_PROPERTY, $data['roomFacilities']) : null;
            })
            ->forMember('gender', function () use ($data) {
                if (empty($data['gender'])) {
                    return $this->translateCms('label.does_not_care');
                }

                return self::MALE_GENDER === $data['gender']
                    ? $this->translateCms('label.male_gender')
                    : $this->translateCms('label.female_gender');
            })
            ->forMember('animals', function () use ($data) {
                if (null === $data['animals']) {
                    return null;
                }

                switch ($data['animals']) {
                    case self::ANIMALS_ALLOWED:
                        return $this->translateCms('label.yes');
                    case self::ANIMALS_NOT_ALLOWED:
                        return $this->translateCms('label.no');
                    case self::ANIMALS_NOT_IMPORTANT:
                        return $this->translateCms('label.does_not_care');
                    case self::CAT_ALLOWED:
                        return $this->translateCms('label.yes_cat');
                    case self::DOG_ALLOWED:
                        return $this->translateCms('label.yes_dog');
                }
            })
            ->forMember('deposit', function () use ($data) {
                return !empty($data['deposit']) ? $this->formatValute($data['deposit'], 0) : null;
            })
            ->forMember('servicesCosts', function () use ($data) {
                return !empty($data['servicesCosts']) ? $this->formatValute($data['servicesCosts'], 0) : null;
            })
            ->forMember('otherCosts', function () use ($data) {
                return !empty($data['otherCosts']) ? $this->formatValute($data['otherCosts'], 0) : null;
            })
            ->forMember('minimumStay', function () use ($data) {
                if (null === $data['minimumStay']) {
                    return null;
                }
                $label = 1 === $data['minimumStay'] ? 'label.month' : 'label.months';

                return \sprintf('%s %s', $data['minimumStay'], $this->translateCms($label));
            })
            ->forMember('availableFrom', function () use ($data) {
                $date = null !== $data['availableFrom'] ? new \DateTime($data['availableFrom']) : null;

                return null !== $date ?
                    $this->dateFormatter->shortFormat($date) :
                    null;
            })
            ->forMember('smoker', function () use ($data) {
                if (null === $data['smoker']) {
                    return null;
                }

                if (self::SMOKER_NOT_IMPORTANT === $data['smoker']) {
                    return $this->translateCms('label.does_not_care');
                }

                return self::SMOKER_ALLOWED == $data['smoker']
                    ? $this->translateCms('label.yes')
                    : $this->translateCms('label.no');
            })
            ->forMember('gasCostsIncluded', function () use ($data) {
                if (null === $data['gasCostsIncluded']) {
                    return null;
                }

                return empty($data['gasCostsIncluded'])
                    ? $this->translateCms('label.not_inclusive_plural')
                    : $this->translateCms('label.inclusive_plural');
            })
            ->forMember('internetCostsIncluded', function () use ($data) {
                if (null === $data['internetCostsIncluded']) {
                    return null;
                }

                return empty($data['internetCostsIncluded'])
                    ? $this->translateCms('label.not_inclusive_plural')
                    : $this->translateCms('label.inclusive_plural');
            })
            ->forMember('phoneCostsIncluded', function () use ($data) {
                if (null === $data['phoneCostsIncluded']) {
                    return null;
                }

                return empty($data['phoneCostsIncluded'])
                    ? $this->translateCms('label.not_inclusive_plural')
                    : $this->translateCms('label.inclusive_plural');
            })
            ->forMember('electricityCostsIncluded', function () use ($data) {
                if (null === $data['electricityCostsIncluded']) {
                    return null;
                }

                return empty($data['electricityCostsIncluded'])
                    ? $this->translateCms('label.not_inclusive_plural')
                    : $this->translateCms('label.inclusive_plural');
            })
            ->forMember('couple', function () use ($data) {
                if (null === $data['couple']) {
                    return null;
                }

                if (self::COUPLES_NOT_IMPORTANT === $data['couple']) {
                    return $this->translateCms('label.does_not_care');
                }

                return self::COUPLES_ALLOWED === $data['couple']
                    ? $this->translateCms('label.yes')
                    : $this->translateCms('label.no');
            })
            ->forMember('roomType', function () use ($data) {
                return !empty($data['roomType']['id'])
                    ? $this->translateContent('db_room_typology.id_', (string) $data['roomType']['id'])
                    : null;
            })
        ;

        return $config;
    }
}
