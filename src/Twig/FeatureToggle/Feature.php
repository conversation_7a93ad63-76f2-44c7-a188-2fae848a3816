<?php

namespace App\Twig\FeatureToggle;

    use App\Component\FeatureToggle\Handler\FeatureToggleHandler;
    use Twig\Extension\AbstractExtension;

    class Feature extends AbstractExtension
    {
        /**
         * @var FeatureToggleHandler
         */
        private $featureToggleHandler;

        /**
         * Feature constructor.
         */
        public function __construct(FeatureToggleHandler $featureToggleHandler)
        {
            $this->featureToggleHandler = $featureToggleHandler;
        }

        /**
         * @return array|\Twig\TokenParser\AbstractTokenParser[]
         */
        public function getTokenParsers(): array
        {
            return [
               new FeatureToggleTokenParser(),
           ];
        }

        public function getFunctions(): array
        {
            return [
                new \Twig_SimpleFunction('feature_enabled', [$this, 'isFeatureEnabled']),
            ];
        }

        public function getName(): string
        {
            return 'feature_toggle';
        }

        /**
         * @param $featureName
         *
         * @throws \App\Component\FeatureToggle\Exception\FeatureNotExistException
         */
        public function isFeatureEnabled($featureName): bool
        {
            return $this->featureToggleHandler->featureIsEnabled($featureName) || $this->featureToggleHandler->featureIsMantainance($featureName);
        }
    }
