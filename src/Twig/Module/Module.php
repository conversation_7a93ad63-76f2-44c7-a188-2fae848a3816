<?php

namespace App\Twig\Module;

    use App\Component\Module\Handler\ModuleHandler;
    use Twig\Extension\AbstractExtension;

    class Module extends AbstractExtension
    {
        /**
         * @var ModuleHandler
         */
        private $moduleHandler;

        /**
         * Feature constructor.
         */
        public function __construct(ModuleHandler $moduleHandler)
        {
            $this->moduleHandler = $moduleHandler;
        }

        /**
         * @return array|\Twig\TokenParser\AbstractTokenParser[]
         */
        public function getTokenParsers(): array
        {
            return [
                new ModuleTokenParser(),
            ];
        }

        public function getFunctions(): array
        {
            return [
                new \Twig_SimpleFunction('module_enabled', [$this, 'isModuleEnabled']),
            ];
        }

        public function getName(): string
        {
            return 'module';
        }

        /**
         * @param $moduleName
         */
        public function isModuleEnabled($moduleName): bool
        {
            return $this->moduleHandler
                ->moduleIsEnabled($moduleName);
        }
    }
