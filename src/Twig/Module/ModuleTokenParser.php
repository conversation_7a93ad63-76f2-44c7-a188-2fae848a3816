<?php

namespace App\Twig\Module;

    use Twig\Error\SyntaxError;
    use Twig\Node\Expression\Binary\EqualBinary;
    use Twig\Node\Expression\ConstantExpression;
    use Twig\Node\Expression\FunctionExpression;
    use Twig\Node\IfNode;
    use Twig\Node\Node;
    use Twig\Token;
    use Twig\TokenParser\AbstractTokenParser;

    class ModuleTokenParser extends AbstractTokenParser
    {
        const TAG_NAME = 'module';

        /**
         * @return mixed
         *
         * @throws SyntaxError
         */
        public function parse(Token $token)
        {
            $moduleName   = null;
            $lineno       = $token->getLine();
            $stream       = $this->parser->getStream();
            while (!$stream->test(Token::BLOCK_END_TYPE)) {
                if (!$stream->test(Token::STRING_TYPE)) {
                    $token = $stream->getCurrent();
                    throw new SyntaxError(sprintf('Unexpected token "%s" of value %s".', Token::typeToEnglish($token->getType()), $token->getValue()), $token->getLine());
                }
                $moduleName = $stream->next()->getValue();
            }
            $stream->expect(/* Token::BLOCK_END_TYPE */ 3);
            $body = $this->parser->subparse([$this, 'decideModuleFork']);
            $else = null;

            $end = false;
            while (!$end) {
                switch ($stream->next()->getValue()) {
                    case 'else':
                        $stream->expect(/* Token::BLOCK_END_TYPE */ 3);
                        $else = $this->parser->subparse([$this, 'decideModuleEnd']);
                        break;
                    case 'endmodule':
                        $end = true;
                        break;
                    default:
                        throw new SyntaxError(sprintf('Unexpected end of template. Twig was looking for the following tags "else", or "endmodule" to close the "module_enabled" block started at line %d).', $lineno), $stream->getCurrent()->getLine(), $stream->getSourceContext());
                }
            }

            $stream->expect(/* Token::BLOCK_END_TYPE */ 3);

            $expression = new EqualBinary(
                new FunctionExpression(
                    'module_enabled',
                    new Node(
                        [
                            new ConstantExpression($moduleName, $token->getLine()),
                        ]
                    ), $token->getLine()),
                new ConstantExpression(true, $token->getLine()),
                $token->getLine()
            );

            return new IfNode(
                new Node([$expression, $body]),
                $else,
                $token->getLine(),
                $this->getTag()
            );

            //           return new IfNode(new Node($tests), $else, $lineno, $this->getTag());

//
//
//
//            $moduleName   = null;
//            $stream = $this->parser->getStream();
//            while (!$stream->test(Token::BLOCK_END_TYPE)) {
//                if (!$stream->test(Token::STRING_TYPE)) {
//                    $token = $stream->getCurrent();
//                    throw new SyntaxError(sprintf('Unexpected token "%s" of value %s".', Token::typeToEnglish($token->getType()), $token->getValue()), $token->getLine());
//                }
//                $moduleName = $stream->next()->getValue();
//            }
//            $stream->expect(Token::BLOCK_END_TYPE);
//            // Store the body of the feature.
//            $body = $this->parser->subparse([$this, 'decideModuleEnd'], true);
//            $stream->expect(Token::BLOCK_END_TYPE);
//
//            $expression = new EqualBinary(
//                new FunctionExpression(
//                    'module_enabled',
//                    new Node(
//                        array(
//                            new ConstantExpression($moduleName, $token->getLine())
//                        )
//                    ), $token->getLine()),
//                new ConstantExpression(true, $token->getLine()),
//                $token->getLine()
//            );
//
//            return new IfNode(
//                new Node(array($expression, $body)),
//                null,
//                $token->getLine()
//            );
        }

        /**
         * Test whether the module is ended or not.
         */
        public function decideModuleEnd(Token $token): bool
        {
            return $token->test($this->getEndTag());
        }

        public function decideModuleFork(Token $token): bool
        {
            return $token->test(['else', 'endmodule']);
        }

        /**
         * Return the tag that marks the beginning of a module.
         */
        public function getTag(): string
        {
            return self::TAG_NAME;
        }

        /**
         * Return the tag that marks the end of the module.
         */
        public function getEndTag(): string
        {
            return 'end' . $this->getTag();
        }
    }
