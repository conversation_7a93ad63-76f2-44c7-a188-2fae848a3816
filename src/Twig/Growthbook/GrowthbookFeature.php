<?php

namespace App\Twig\Growthbook;

use GetrixBe\GrowthbookBundle\GrowthbookProxy;
use Twig\Extension\AbstractExtension;

class GrowthbookFeature extends AbstractExtension
{
    /**
     * @var GrowthbookProxy
     */
    private $growthbookProxy;

    /**
     * Feature constructor.
     */
    public function __construct(GrowthbookProxy $growthbookProxy)
    {
        $this->growthbookProxy = $growthbookProxy;
    }

    /**
     * @return array|\Twig\TokenParser\AbstractTokenParser[]
     */
    public function getTokenParsers(): array
    {
        return [
            new GrowthbookFeatureToggleTokenParser(),
        ];
    }

    public function getFunctions(): array
    {
        return [
            new \Twig_SimpleFunction('growthbook_feature_enabled', [$this, 'isGrowthbookFeatureEnabled']),
        ];
    }

    public function getName(): string
    {
        return 'growthbook_feature_toggle';
    }

    /**
     * @param $featureName
     *
     * @throws \App\Component\FeatureToggle\Exception\FeatureNotExistException
     */
    public function isGrowthbookFeatureEnabled($featureName): bool
    {
        return $this->growthbookProxy->IsOn($featureName);
    }
}
