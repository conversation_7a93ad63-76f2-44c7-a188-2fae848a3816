<?php

namespace App\Helper;

use App\Constants\Base\ImmovisitaCostants;
use App\Constants\PerformanceProfiler;
use App\Exception\ImmovisitaException;
use App\Model\Property\Ad;
use Symfony\Component\HttpFoundation\Response;

class ImmovisitaScheduledApiClientHelper extends ApiClientHelper
{
    const VERSION = 'v2';

    protected function formatEndpoint(int $agencyId): string
    {
        return \sprintf('%s/api/%s/agency/%d', $this->apiBaseurl, self::VERSION, $agencyId);
    }

    /**
     * @throws ImmovisitaException
     */
    public function getRealEstateById(int $agencyId, int $realEstateId): Ad
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $fullEndpoint = \sprintf('%s/ads/%s', $this->formatEndpoint($agencyId), $realEstateId);

        $result = $this->httpClient->execRequestWithResponse('GET', $fullEndpoint, [], $this->getDefaultJsonHeaders());

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $detail = \App\Builder\Model\Property\AdBuilder::newBuilder()
            ->fromApiResponse($decoded['data'])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $detail;
    }
}
