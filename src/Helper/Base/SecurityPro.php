<?php

namespace App\Helper\Base;

use App\Utils\ArrayUtil;
use Symfony\Component\HttpFoundation\Request;

class SecurityPro extends Security
{
    /**
     * @param $data
     */
    public function register(Request $request, $data)
    {
        $data['firstName'] = $data['nome'];
        $data['lastName'] = $data['cognome'];
        $data['companyName'] = $data['nomesocieta'];
        $data['companyType'] = $data['tipoimpresa'];
        $data['phonePrefix'] = $data['prefisso'];
        $data['phoneNumber'] = $data['numero'];
        $data['cityName'] = $data['comune'];

        $this->mailer->sendRegistration($data);
        $this->mailer->sendRegistrationToAgency($data);
        $this->logInfo(\serialize($data), [$request->get('_route')]);
    }

    /**
     * @param $data
     */
    public function registerByRouteName($data, $routeName)
    {
        $this->mailer->sendRegistration($data);
        $this->mailer->sendRegistrationToAgency($data);
        $this->logInfo(\serialize($data), [$routeName]);
    }

    /**
     * @param $request
     *
     * @return array
     */
    public function formatRegisterRequest($request)
    {
        $data = ArrayUtil::arrayFilterByKeys($request->request->get('register_pro'), ['_token']);

        return ArrayUtil::trimAndStripValue($data);
    }

    public function logInfo($data, array $route = [])
    {
        if (!empty($data)) {
            $this->logger->addInfo($data, $route);
        }
    }
}
