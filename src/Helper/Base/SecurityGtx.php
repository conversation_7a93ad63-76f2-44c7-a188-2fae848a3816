<?php

namespace App\Helper\Base;

use App\Utils\ArrayUtil;
use Symfony\Component\HttpFoundation\Request;

class SecurityGtx extends Security
{
    public function register(Request $request)
    {
        $data = ArrayUtil::arrayFilterByKeys($request->request->get('register_gtx'), ['_token']);
        $data = ArrayUtil::trimAndStripValue($data);

        $this->mailer->sendRegistration($data);
        $this->logger->addInfo(\serialize($data), [$request->get('_route')]);
    }

    public function getRegistrationSuccessText()
    {
        $res = ['title' => '', 'message' => ''];

        $res['title'] = 'Messaggio inviato con successo.';
        $res['message'] = 'Sarai ricontattato al più presto.';

        return $res;
    }
}
