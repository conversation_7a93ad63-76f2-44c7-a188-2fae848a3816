<?php

namespace App\Helper;

use App\Constants\Base\DashboardConstants;
use App\Constants\Base\PropertyConstants;
use App\Constants\PerformanceProfiler;
use App\Exception\ApiException;
use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;
use App\Model\Dashboard\AdsByCategoryCount;
use App\Model\Dashboard\AdsByCategoryGroups;
use App\Performance\ProfilerInterface;
use GuzzleHttp\Promise\Utils;
use Symfony\Component\HttpFoundation\Response;

class ProjectApiClientHelper extends ApiClientHelper
{
    const VERSION = 'v1';
    const RESOURCE = 'projects';

    protected string $endpoint;

    public function __construct(
        HttpClientHelper $httpClient,
        ParametersBag $parametersBag,
        ApiWsseHeaderGenerator $apiWsseHeaderGenerator,
        ProfilerInterface $performanceProfiler
    ) {
        parent::__construct($httpClient, $performanceProfiler, $parametersBag, $apiWsseHeaderGenerator);

        $this->endpoint = \sprintf('%s/api/%s/agency', $this->apiBaseurl, parent::VERSION_2);
    }

    /**
     * @throws ApiException
     */
    public function getProjectsByCategoryGroups(
        int $agencyId,
        array $publishedStatusIds = [],
        array $archivedStatusIds = []
    ): AdsByCategoryGroups {
        $urls = [
            DashboardConstants::CATEGORY_GROUP_ACTIVE => \sprintf(
                '%s/%s/%s/count?status=%s',
                $this->endpoint,
                $agencyId,
                self::RESOURCE,
                \implode(',', $publishedStatusIds)
            ),
            DashboardConstants::CATEGORY_GROUP_PUBLISHED => \sprintf(
                '%s/%s/%s/count?status=%s&searchable=1',
                $this->endpoint,
                $agencyId,
                self::RESOURCE,
                \implode(',', $publishedStatusIds)
            ),
            DashboardConstants::CATEGORY_GROUP_ARCHIVED => \sprintf(
                '%s/%s/%s/count?status=%s',
                $this->endpoint,
                $agencyId,
                self::RESOURCE,
                \implode(',', $archivedStatusIds) // 3,4,5,6,7
            ),
        ];

        $client = new \GuzzleHttp\Client();

        $promises = [];
        foreach ($urls as $key => $url) {
            $promises[$key] = $client->getAsync($url, [
                'headers' => $this->getDefaultJsonHeaders(),
            ]);
        }

        // Wait for the requests to complete, even if some of them fail
        $responses = Utils::settle($promises)->wait();

        $decoded = [];
        foreach ($urls as $key => $url) {
            $response = $responses[$key]['value'] ?? null;

            $decoded[$key] = $response instanceof \GuzzleHttp\Psr7\Response ?
                \json_decode($response->getBody()->getContents(), true) :
                [];
        }

        return \App\Builder\Model\Dashboard\AdsByCategoryGroupsBuilder::newBuilder()
            ->withActive(\array_map(function (array $item) {
                return \App\Builder\Model\Dashboard\AdsByCategoryCountBuilder::newBuilder()
                    ->fromApiResponse($item)
                    ->build();
            }, $decoded[DashboardConstants::CATEGORY_GROUP_ACTIVE]['data'] ?? []))
            ->withPublished(\array_map(function (array $item) {
                return \App\Builder\Model\Dashboard\AdsByCategoryCountBuilder::newBuilder()
                    ->fromApiResponse($item)
                    ->build();
            }, $decoded[DashboardConstants::CATEGORY_GROUP_PUBLISHED]['data'] ?? []))
            ->withArchived(\array_map(function (array $item) {
                return \App\Builder\Model\Dashboard\AdsByCategoryCountBuilder::newBuilder()
                    ->fromApiResponse($item)
                    ->build();
            }, $decoded[DashboardConstants::CATEGORY_GROUP_ARCHIVED]['data'] ?? []))
            ->build();
    }

    /**
     * @throws ApiException
     * @return AdsByCategoryCount[]
     */
    public function getProjectsByCategoryCount(
        int $agencyId,
        array $adStatusIds,
        bool $searchable = null
    ): array {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s/count?status=%s',
            $this->endpoint,
            $agencyId,
            self::RESOURCE,
            \implode(',', $adStatusIds)
        );

        if (null !== $searchable) {
            $url .= \sprintf('&searchable=%s', $searchable);
        }

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ApiException(PropertyConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $data = \array_map(static function (array $item) {
            return \App\Builder\Model\Dashboard\AdsByCategoryCountBuilder::newBuilder()
                ->fromApiResponse($item)
                ->build();
        }, $decoded['data'] ?? []);

        $this->performanceProfiler->stop(__METHOD__);

        return $data;
    }
}
