<?php

namespace App\Helper;

use App\Builder\Model\Lookup\LookupResultBuilder;
use App\Constants\Base\GeographyConstants;
use App\Constants\Multisend\PageConstants;
use App\Constants\PerformanceProfiler;
use App\Exception\ApiException;
use App\Exception\MultisendException;
use App\Formatter\ImmobiliareUrlFormatter;
use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;
use App\Model\Lookup\LookupResult;
use App\Performance\ProfilerInterface;
use Symfony\Component\HttpFoundation\Response;

class MultisendApiClientHelper extends ApiClientHelper
{
    const VERSION = 'v2';
    const PORTAL_RESOURCE = 'portal-publication';
    const PROPERTY_RESOURCE = 'property-publication';

    const ALREADY_PUBLISHED_API_CODE = '400-0009';
    const QUOTA_LIMIT_REACHED_API_CODE = '400-0010';

    protected string $endpoint;
    protected GeographyApiClientHelper $geographyApiClientHelper;
    protected ImmobiliareUrlFormatter $immobiliareUrlFormatter;

    public function __construct(
        HttpClientHelper $httpClient,
        GeographyApiClientHelper $geographyApiClientHelper,
        ParametersBag $parametersBag,
        ApiWsseHeaderGenerator $apiWsseHeaderGenerator,
        ImmobiliareUrlFormatter $immobiliareUrlFormatter,
        ProfilerInterface $performanceProfiler
    ) {
        parent::__construct($httpClient, $performanceProfiler, $parametersBag, $apiWsseHeaderGenerator);

        $this->endpoint = \sprintf('%s/api/%s/agency', $this->apiBaseurl, self::VERSION);
        $this->geographyApiClientHelper = $geographyApiClientHelper;
        $this->immobiliareUrlFormatter = $immobiliareUrlFormatter;
    }

    /**
     * @throws MultisendException
     */
    public function getCategoriesLookup(int $agencyId, string $context): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s/%s/categories',
            $this->endpoint,
            $agencyId,
            self::PROPERTY_RESOURCE,
            $context
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new MultisendException(PageConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $categories = LookupResultBuilder::newBuilder()
            ->fromTypeAndApiResponse('categories', $decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $categories;
    }

    /**
     * @throws MultisendException
     */
    public function getTypologiesByCategoryIdLookup(int $agencyId, string $context, int $categoryId): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s/%s/typologies?categoryId=%s',
            $this->endpoint,
            $agencyId,
            self::PROPERTY_RESOURCE,
            $context,
            $categoryId
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new MultisendException(PageConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $typologies = LookupResultBuilder::newBuilder()
            ->fromTypeAndApiResponse('typologies', $decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $typologies;
    }

    /**
     * @throws MultisendException
     */
    public function getCountriesLookup(int $agencyId, string $context, string $adStatusIds): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s/%s/countries?adStatusIds=%s',
            $this->endpoint,
            $agencyId,
            self::PROPERTY_RESOURCE,
            $context,
            $adStatusIds
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new MultisendException(PageConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $countries = LookupResultBuilder::newBuilder()
            ->fromTypeAndApiResponse('countries', $decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $countries;
    }

    /**
     * @throws MultisendException
     */
    public function getRegionsByCountryIdLookup(int $agencyId, string $context, string $countryId, string $adStatusIds): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s/%s/regions?countryId=%s&adStatusIds=%s',
            $this->endpoint,
            $agencyId,
            self::PROPERTY_RESOURCE,
            $context,
            $countryId,
            $adStatusIds
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new MultisendException(PageConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $regions = LookupResultBuilder::newBuilder()
            ->fromTypeAndApiResponse('regions', $decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $regions;
    }

    /**
     * @throws MultisendException
     */
    public function getProvincesByRegionIdLookup(int $agencyId, string $context, string $regionId, string $adStatusIds): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s/%s/provinces?regionId=%s&adStatusIds=%s',
            $this->endpoint,
            $agencyId,
            self::PROPERTY_RESOURCE,
            $context,
            $regionId,
            $adStatusIds
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new MultisendException(PageConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $provinces = LookupResultBuilder::newBuilder()
            ->fromTypeAndApiResponse('provinces', $decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $provinces;
    }

    /**
     * @throws MultisendException
     */
    public function getCitiesByProvinceIdLookup(int $agencyId, string $context, string $provinceId, string $adStatusIds): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s/%s/cities?provinceId=%s&adStatusIds=%s',
            $this->endpoint,
            $agencyId,
            self::PROPERTY_RESOURCE,
            $context,
            $provinceId,
            $adStatusIds
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new MultisendException(PageConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $cities = LookupResultBuilder::newBuilder()
            ->fromTypeAndApiResponse('cities', $decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $cities;
    }

    /**
     * @throws MultisendException
     * @throws ApiException
     */
    public function getZonesByCityIdLookup(int $agencyId, string $context, int $cityId, string $adStatusIds): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s/%s/zones?cityId=%s&adStatusIds=%s',
            $this->endpoint,
            $agencyId,
            self::PROPERTY_RESOURCE,
            $context,
            $cityId,
            $adStatusIds
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new MultisendException(PageConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $lookupData = $decoded['data'] ?? [];

        $lookupTypeId = $this->geographyApiClientHelper
            ->getCityMacroZoneTypeId($cityId);

        $lookupType = GeographyConstants::TIPO_MACROZONE_COMUNE_MACROZONE_ID === $lookupTypeId ?
            GeographyConstants::TIPO_MACROZONE_COMUNE_MACROZONE :
            GeographyConstants::TIPO_MACROZONE_COMUNE_AREE_URBANE;

        $lookup = LookupResultBuilder::newBuilder()
            ->fromTypeAndApiResponse($lookupType, $lookupData)
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $lookup;
    }
}
