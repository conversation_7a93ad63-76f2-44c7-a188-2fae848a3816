<?php

namespace App\Helper;

use App\Constants\Base\ImmovisitaCostants;
use App\Constants\PerformanceProfiler;
use App\Exception\ImmovisitaException;
use App\Model\Immovisita\ImmovisitaGuest;
use App\Model\Immovisita\ImmovisitaRoom;
use App\Model\Immovisita\ListingImmovisitaAdsResponse;
use App\Model\Immovisita\ListingImmovisitaRoomsResponse;
use App\Model\Immovisita\SearchFilters\ListingImmovisitaAdsFilters;
use App\Model\Immovisita\SearchFilters\ListingImmovisitaRoomsFilters;
use App\Model\Shared\Order;
use App\Model\Shared\PaginationRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ImmovisitaApiClientHelper extends ApiClientHelper
{
    const VERSION = 'v1';
    const RESOURCE = 'immovisita';

    const RESOURCE_GUESTS = 'guests';
    const RESOURCE_ROOMS = 'rooms';
    const RESOURCE_ADS = 'ads';

    protected function formatEndpoint(int $agencyId): string
    {
        return \sprintf('%s/api/%s/agency/%d/%s', $this->apiBaseurl, parent::VERSION_2, $agencyId, self::RESOURCE);
    }

    /**
     * @throws ImmovisitaException
     */
    public function createGuest(
        int $agencyId,
        int $roomId,
        Request $request
    ): ImmovisitaGuest {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $guestRequest = \App\Builder\Model\ApiModel\Immovisita\ImmovisitaGuestRequestBuilder::newBuilder()
            ->fromRequest($request->request->all())
            ->build();

        $url = \sprintf(
            '%s/%s/%d/%s',
            $this->formatEndpoint($agencyId),
            self::RESOURCE_ROOMS,
            $roomId,
            self::RESOURCE_GUESTS
        );

        $result = $this->httpClient->execRequestWithResponse(
            'POST',
            $url,
            $guestRequest,
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_CREATED !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $guest = \App\Builder\Model\Immovisita\ImmovisitaGuestBuilder::newBuilder()
            ->fromApiResponse($decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $guest;
    }

    /**
     * @throws ImmovisitaException
     */
    public function updateGuest(
        int $agencyId,
        int $roomId,
        int $guestId,
        Request $request
    ): ImmovisitaGuest {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $guestRequest = \App\Builder\Model\ApiModel\Immovisita\ImmovisitaGuestRequestBuilder::newBuilder()
            ->fromRequest($request->request->all())
            ->build();

        $url = \sprintf(
            '%s/%s/%d/%s/%d',
            $this->formatEndpoint($agencyId),
            self::RESOURCE_ROOMS,
            $roomId,
            self::RESOURCE_GUESTS,
            $guestId
        );

        $result = $this->httpClient->execRequestWithResponse(
            'PUT',
            $url,
            $guestRequest,
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $guest = \App\Builder\Model\Immovisita\ImmovisitaGuestBuilder::newBuilder()
            ->fromApiResponse($decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $guest;
    }

    /**
     * @throws ImmovisitaException
     */
    public function getGuest(
        int $agencyId,
        int $roomId,
        int $guestId
    ): ImmovisitaGuest {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d',
            $this->formatEndpoint($agencyId),
            self::RESOURCE_ROOMS,
            $roomId,
            self::RESOURCE_GUESTS,
            $guestId
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $guest = \App\Builder\Model\Immovisita\ImmovisitaGuestBuilder::newBuilder()
            ->fromApiResponse($decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $guest;
    }

    /**
     * @throws ImmovisitaException
     * @return ImmovisitaGuest[]
     */
    public function getGuests(
        int $agencyId,
        int $roomId
    ): array {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s',
            $this->formatEndpoint($agencyId),
            self::RESOURCE_ROOMS,
            $roomId,
            self::RESOURCE_GUESTS
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $guest = \array_map(static function (array $item) {
            return \App\Builder\Model\Immovisita\ImmovisitaGuestBuilder::newBuilder()
                ->fromApiResponse($item)
                ->build();
        }, $decoded['data'] ?? []);

        $this->performanceProfiler->stop(__METHOD__);

        return $guest;
    }

    /**
     * @throws ImmovisitaException
     */
    public function createRoom(
        int $agencyId,
        Request $request
    ): ImmovisitaRoom {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $roomRequest = \App\Builder\Model\ApiModel\Immovisita\ImmovisitaRoomRequestBuilder::newBuilder()
            ->fromRequest(\json_decode($request->getContent(), true))
            ->build();

        $url = \sprintf(
            '%s/%s',
            $this->formatEndpoint($agencyId),
            self::RESOURCE_ROOMS
        );

        $result = $this->httpClient->execRequestWithResponse(
            'POST',
            $url,
            $roomRequest,
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_CREATED !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $room = \App\Builder\Model\Immovisita\ImmovisitaRoomBuilder::newBuilder()
            ->fromApiResponse($decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $room;
    }

    /**
     * @throws ImmovisitaException
     */
    public function updateRoom(
        int $agencyId,
        int $roomId,
        Request $request
    ): ImmovisitaRoom {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $roomRequest = \App\Builder\Model\ApiModel\Immovisita\ImmovisitaRoomRequestBuilder::newBuilder()
            ->fromRequest(\json_decode($request->getContent(), true))
            ->build();
        // dump($roomRequest);
        $url = \sprintf(
            '%s/%s/%d',
            $this->formatEndpoint($agencyId),
            self::RESOURCE_ROOMS,
            $roomId
        );

        $result = $this->httpClient->execRequestWithResponse(
            'PUT',
            $url,
            $roomRequest,
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $room = \App\Builder\Model\Immovisita\ImmovisitaRoomBuilder::newBuilder()
            ->fromApiResponse($decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $room;
    }

    /**
     * @throws ImmovisitaException
     */
    public function deleteRoom(int $agencyId, int $roomId)
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d',
            $this->formatEndpoint($agencyId),
            self::RESOURCE_ROOMS,
            $roomId
        );

        $result = $this->httpClient->execRequestWithResponse(
            'DELETE',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $this->performanceProfiler->stop(__METHOD__);
    }

    /**
     * @throws ImmovisitaException
     */
    public function getRoom(
        int $agencyId,
        int $roomId
    ): ImmovisitaRoom {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d',
            $this->formatEndpoint($agencyId),
            self::RESOURCE_ROOMS,
            $roomId
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $room = \App\Builder\Model\Immovisita\ImmovisitaRoomBuilder::newBuilder()
            ->fromApiResponse($decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $room;
    }

    /**
     * @throws ImmovisitaException
     */
    public function listingRooms(
        int $agencyId,
        ListingImmovisitaRoomsFilters $filter,
        Order $order,
        PaginationRequest $pagination
    ): ListingImmovisitaRoomsResponse {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $params = [
            'offset' => $pagination->getStart(),
            'limit' => $pagination->getResults(),
            'sort' => \sprintf('%s%s', $order->getDirection(), $order->getField()),
        ];

        $this->applyFiltersToListingRoomsRequestParameters($filter, $params);

        $url = \sprintf(
            '%s/%s?%s',
            $this->formatEndpoint($agencyId),
            self::RESOURCE_ROOMS,
            \http_build_query($params)
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $paginationResponse = \App\Builder\Model\Shared\PaginationResponseBuilder::newBuilder()
            ->withStart($pagination->getStart())
            ->withResults($pagination->getResults())
            ->withTotal($result->getHeaderLine('X-Total-Count'))
            ->build();

        $rooms = \array_map(function (array $item) {
            return \App\Builder\Model\Immovisita\ListingImmovisitaRoomBuilder::newBuilder()
                ->fromApiResponse($item)
                ->build();
        }, $decoded['data'] ?? []);

        $listing = \App\Builder\Model\Immovisita\ListingImmovisitaRoomsResponseBuilder::newBuilder()
            ->withRooms($rooms)
            ->withPagination($paginationResponse)
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $listing;
    }

    /**
     * @throws ImmovisitaException
     */
    public function getAds(
        int $agencyId,
        ListingImmovisitaAdsFilters $filter,
        Order $order,
        PaginationRequest $pagination
    ): ListingImmovisitaAdsResponse {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $params = [
            'offset' => $pagination->getStart(),
            'limit' => $pagination->getResults(),
            'sort' => \sprintf('%s%s', $order->getDirection(), $order->getField()),
        ];

        $this->applyFiltersToListingAdsRequestParameters($filter, $params);

        $url = \sprintf(
            '%s/%s?%s',
            $this->formatEndpoint($agencyId),
            self::RESOURCE_ADS,
            \http_build_query($params)
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $paginationResponse = \App\Builder\Model\Shared\PaginationResponseBuilder::newBuilder()
            ->withStart($pagination->getStart())
            ->withResults($pagination->getResults())
            ->withTotal($result->getHeaderLine('X-Total-Count'))
            ->build();

        $ads = \array_map(function (array $item) {
            return \App\Builder\Model\Immovisita\ListingImmovisitaAdBuilder::newBuilder()
                ->fromApiResponse($item)
                ->build();
        }, $decoded['data'] ?? []);

        $listing = \App\Builder\Model\Immovisita\ListingImmovisitaAdsResponseBuilder::newBuilder()
            ->withAds($ads)
            ->withPagination($paginationResponse)
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $listing;
    }

    private function applyFiltersToListingRoomsRequestParameters(
        ListingImmovisitaRoomsFilters $filter,
        array &$parameters
    ) {
        if (!empty($filter->getWithScheduledTime())) {
            $parameters['withScheduledTime'] = $filter->getWithScheduledTime();
        }

        if (!empty($filter->getWithoutEndTime())) {
            $parameters['withoutEndTime'] = $filter->getWithoutEndTime();
        }

        if (!empty($filter->getQuery())) {
            $parameters['q'] = $filter->getQuery();
            $parameters['fields'] = \implode(',', $filter->getFields());
        }
    }

    private function applyFiltersToListingAdsRequestParameters(
        ListingImmovisitaAdsFilters $filter,
        array &$parameters
    ) {
        if (!empty($filter->getQuery())) {
            $parameters['q'] = $filter->getQuery();
            $parameters['fields'] = \implode(',', $filter->getFields());
        }

        if (null !== $filter->getTypologies() && null !== $filter->getTypologies()->getCategory()) {
            $parameters['categories'] = $filter->getTypologies()->getCategory();
        }

        if (null !== $filter->getTypologies()
            && \is_array($filter->getTypologies()->getTypologies())
            && \array_key_exists(0, $filter->getTypologies()->getTypologies())
        ) {
            $parameters['typologyIds'] = $filter->getTypologies()->getTypologies()[0];
        }

        if (!empty($filter->getExcludedCategories())) {
            $parameters['excludedCategories'] = \implode(',', $filter->getExcludedCategories());
        }

        if (null !== $filter->getContractId()) {
            $parameters['contractId'] = $filter->getContractId();
        }

        if (null !== $filter->getPrice() && null !== $filter->getPrice()->getMin()) {
            $parameters['priceMin'] = $filter->getPrice()->getMin();
        }

        if (null !== $filter->getPrice() && null !== $filter->getPrice()->getMax()) {
            $parameters['priceMax'] = $filter->getPrice()->getMax();
        }

        if (null !== $filter->getSurface() && null !== $filter->getSurface()->getMin()) {
            $parameters['surfaceMin'] = $filter->getSurface()->getMin();
        }

        if (null !== $filter->getSurface() && null !== $filter->getSurface()->getMax()) {
            $parameters['surfaceMax'] = $filter->getSurface()->getMax();
        }

        if (null !== $filter->getWithVirtualTour()) {
            $parameters['withVirtualTour'] = $filter->getWithVirtualTour();
        }
    }
}
