<?php

namespace App\Helper\Adapter;

use App\Exception\ApiException;
use App\Helper\SalesRequestsApiClientHelper;
use App\Utils\ISalesRequestsApiClientHelperInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class AuthenticatedSalesRequestsApiClientHelper
{
    /** @var SalesRequestsApiClientHelper */
    private $apiClientHelper;

    /** @var TokenStorageInterface */
    private $tokenStorage;

    public function __construct(
        ISalesRequestsApiClientHelperInterface $apiClientHelper,
        TokenStorageInterface $tokenStorage
    ) {
        $this->apiClientHelper = $apiClientHelper;
        $this->tokenStorage = $tokenStorage;
    }

    protected function getAgencyId()
    {
        return $this->tokenStorage->getToken()->getUser()->getAgenzia()->idAgenzia;
    }

    public function listSalesRequests($results, $page)
    {
        return $this->apiClientHelper->getListSalesRequests($results, $page, $this->getAgencyId());
    }

    public function getSalesRequestsItem(string $resourceId)
    {
        return $this->apiClientHelper->getSalesRequestItem($resourceId, $this->getAgencyId());
    }

    /** @throws ApiException */
    public function getSalesRequestsOutcomesLookup()
    {
        return $this->apiClientHelper->getSalesRequestOutcomesLookup();
    }

    public function setSalesRequestStatus(string $resourceId, array $payload)
    {
        return $this->apiClientHelper->setSalesRequestStatus(
            $this->getAgencyId(),
            $resourceId,
            $payload
        );
    }
}
