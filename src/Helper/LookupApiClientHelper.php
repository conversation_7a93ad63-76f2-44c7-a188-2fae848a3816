<?php

namespace App\Helper;

use App\Constants\Base\LanguagesConstants;
use App\Constants\Base\LookupConstants;
use App\Constants\PerformanceProfiler;
use App\Exception\LookupException;
use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;
use App\Model\Lookup\InternationalPhonePrefixes\InternationalPhonePrefixResponse;
use App\Model\Lookup\LookupResult;
use App\Model\Lookup\LookupResultItem;
use App\Performance\ProfilerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Translation\TranslatorInterface;

class LookupApiClientHelper extends ApiClientHelper
{
    const VERSION = 'v2';
    const LOOKUP_NAMESPACE_AGENCY = 'agency';
    const LOOKUP_NAMESPACE_GEOGRAPHY = 'geography';
    const LOOKUP_NAMESPACE_ESTIMATES = 'estimates';
    const LOOKUP_NAMESPACE_PRIVATE_AD = 'private-ads';
    const LOOKUP_NAMESPACE_PROPERTY = 'properties';

    protected string $lookupEndpoint;
    private TranslatorInterface $translator;

    public function __construct(
        HttpClientHelper $httpClient,
        ParametersBag $parametersBag,
        ApiWsseHeaderGenerator $apiWsseHeaderGenerator,
        ProfilerInterface $performanceProfiler,
        TranslatorInterface $translator
    ) {
        parent::__construct($httpClient, $performanceProfiler, $parametersBag, $apiWsseHeaderGenerator);

        $this->lookupEndpoint = \sprintf('%s/api/%s', $this->apiBaseurl, self::VERSION);
        $this->translator = $translator;
    }

    /**
     * @throws LookupException
     */
    private function doLookup($fullEndpoint, $type = 'data', $params = [])
    {
        $result = $this->httpClient->execRequestWithResponse('GET', $fullEndpoint, $params, $this->getDefaultHeaders());

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new LookupException(LookupConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        return \App\Builder\Model\Lookup\LookupResultBuilder::newBuilder()
            ->fromTypeAndApiResponse($type, $decoded['data'] ?? [])
            ->build();
    }

    /**
     * @throws LookupException
     */
    public function getFranchising(): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'franchising';
        $fullEndpoint = \sprintf('%s/%s/%s', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_AGENCY, $type);
        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getAssociations(): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'associations';
        $fullEndpoint = \sprintf('%s/%s/%s', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_AGENCY, $type);
        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getCountries(): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'countries';
        $fullEndpoint = \sprintf('%s/%s/%s', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_GEOGRAPHY, $type);
        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getProvincesByCountry(string $countryId): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'provinces';
        $fullEndpoint = \sprintf('%s/%s/countries/%s/%s', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_GEOGRAPHY, $countryId, $type);
        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getRegionsByCountry(string $countryId): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'regions';
        $fullEndpoint = \sprintf('%s/%s/countries/%s/%s', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_GEOGRAPHY, $countryId, $type);
        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getProvincesByRegion(string $regionId): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'provinces';
        $fullEndpoint = \sprintf('%s/%s/regions/%s/%s', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_GEOGRAPHY, $regionId, $type);
        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getCitiesByProvince(string $provinceId): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'cities';
        $fullEndpoint = \sprintf('%s/%s/provinces/%s/%s', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_GEOGRAPHY, $provinceId, $type);
        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getZonesByCity(string $cityId, string $sort = null): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'zones';
        $fullEndpoint = \sprintf('%s/%s/cities/%s/%s', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_GEOGRAPHY, $cityId, $type);

        if (!empty($sort)) {
            $fullEndpoint .= \sprintf('?sort=%s', $sort);
        }

        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getInternationalPhonePrefixes(): InternationalPhonePrefixResponse
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'international-phone-prefixes';
        $fullEndpoint = \sprintf('%s/%s', $this->lookupEndpoint, $type);

        $result = $this->httpClient->execRequestWithResponse('GET', $fullEndpoint, [], $this->getDefaultHeaders());

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new LookupException(LookupConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $internationalPhonePrefixResponse = \App\Builder\Model\Lookup\InternationalPhonePrefixes\InternationalPhonePrefixResponseBuilder::newBuilder()
            ->fromTypeAndApiResponse($decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $internationalPhonePrefixResponse;
    }

    /**
     * @throws LookupException
     */
    public function getTypologiesByCategoryId(int $categoryId): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'typology';
        $fullEndpoint = \sprintf('%s/%s/categories/%s/typologies', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_PROPERTY, $categoryId);
        $result = $this->doLookup($fullEndpoint, $type);

        $data = !empty($result->getData())
            ? \array_map(function (LookupResultItem $resultItem) {
                $resultItem->setLabel($this->translator->trans('db_typology.id_' . $resultItem->getValue(), [], LanguagesConstants::DOMAIN_CONTENTS));

                return $resultItem;
            }, $result->getData())
            : null;

        $result->setData($data);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getSubTypologiesByTypologyId(int $typologyId): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'subtypology';
        $fullEndpoint = \sprintf('%s/%s/sub-typologies?parentTypologyId=%s', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_PROPERTY, $typologyId);
        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getEstimateTypologies(): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'zones';
        $fullEndpoint = \sprintf('%s/%s/typologies', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_ESTIMATES);
        $result = $this->doLookup($fullEndpoint, $type);

        $data = !empty($result->getData())
            ? \array_map(function (LookupResultItem $resultItem) {
                $resultItem->setLabel($this->translator->trans('appraisal_typology_' . $resultItem->getValue(), [], LanguagesConstants::DOMAIN_CONTENTS));

                return $resultItem;
            }, $result->getData())
            : null;

        $result->setData($data);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getEstimateCities(int $agencyId): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'zones';
        $fullEndpoint = \sprintf('%s/%s/%s/cities', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_ESTIMATES, $agencyId);
        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getFloorRanges(): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'floor-type';
        $fullEndpoint = \sprintf('%s/%s/floor-ranges', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_PROPERTY);
        $result = $this->doLookup($fullEndpoint, $type);

        $data = !empty($result->getData())
            ? \array_map(function (LookupResultItem $resultItem) {
                $resultItem->setLabel($this->translator->trans('db_floor_category.id_' . $resultItem->getValue(), [], LanguagesConstants::DOMAIN_CONTENTS));

                return $resultItem;
            }, $result->getData())
            : null;

        $result->setData($data);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getAgencyProvinces(int $agencyId): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'agency-province';
        $fullEndpoint = \sprintf('%s/%s/%s/provinces', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_AGENCY, $agencyId);
        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getPrivateAdsCategories(): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'category';
        $fullEndpoint = \sprintf('%s/%s/categories', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_PRIVATE_AD);
        $result = $this->doLookup($fullEndpoint, $type);

        $data = !empty($result->getData())
            ? \array_map(function (LookupResultItem $resultItem) {
                $resultItem->setLabel($this->translator->trans('db_category.id_' . $resultItem->getValue(), [], LanguagesConstants::DOMAIN_CONTENTS));

                return $resultItem;
            }, $result->getData())
            : null;

        $result->setData($data);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    /**
     * @throws LookupException
     */
    public function getPrivateAdsPortals(): LookupResult
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $type = 'portal';
        $fullEndpoint = \sprintf('%s/%s/portals', $this->lookupEndpoint, self::LOOKUP_NAMESPACE_PRIVATE_AD);
        $result = $this->doLookup($fullEndpoint, $type);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }
}
