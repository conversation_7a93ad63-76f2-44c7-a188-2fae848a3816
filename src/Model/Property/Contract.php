<?php

namespace App\Model\Property;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\AnnunciTypes\Contratto")
 */
class Contract
{
    /**
     * @Mapping\Id
     * @Mapping\Property(name="idContratto", type="integer")
     */
    public $id;

    /**
     * @Mapping\Property(name="nome", type="string")
     */
    public $name;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }
}
