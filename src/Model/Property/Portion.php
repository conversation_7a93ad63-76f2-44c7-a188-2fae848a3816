<?php

declare(strict_types=1);

namespace App\Model\Property;

class Portion
{
    /** @var Image[]|null */
    private ?array $images = null;
    private WorkSpace $workspace;

    public function getImages(): ?array
    {
        return $this->images;
    }

    public function setImages(?array $images): void
    {
        $this->images = $images;
    }

    public function getWorkspace(): WorkSpace
    {
        return $this->workspace;
    }

    public function setWorkspace(WorkSpace $workspace): void
    {
        $this->workspace = $workspace;
    }
}
