<?php

declare(strict_types=1);

namespace App\Model\Property\Auction;

class BatchCategory
{
    private ?int $id = null;
    private ?string $name = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): BatchCategory
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): BatchCategory
    {
        $this->name = $name;

        return $this;
    }
}
