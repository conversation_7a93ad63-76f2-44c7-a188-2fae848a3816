<?php

declare(strict_types=1);

namespace App\Model\Property\SearchFilters;

class AdStatsFilter
{
    protected ?\DateTime $start;
    protected ?\DateTime $end;
    protected ?bool $details;

    /** @var string[]|null */
    protected ?array $statTypes;

    public function getStart(): ?\DateTime
    {
        return $this->start;
    }

    public function setStart(?\DateTime $start = null)
    {
        $this->start = $start;
    }

    public function getEnd(): ?\DateTime
    {
        return $this->end;
    }

    public function setEnd(\DateTime $end = null)
    {
        $this->end = $end;
    }

    public function getDetails(): ?bool
    {
        return $this->details;
    }

    public function setDetails(bool $details = null)
    {
        $this->details = $details;
    }

    public function getStatTypes(): ?array
    {
        return $this->statTypes;
    }

    /**
     * @param string[]|null $statTypes
     */
    public function setStatTypes(?array $statTypes = null)
    {
        $this->statTypes = $statTypes;
    }
}
