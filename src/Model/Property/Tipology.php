<?php

declare(strict_types=1);

namespace App\Model\Property;

class Tipology
{
    /**
     * @var int
     */
    public $id;

    /**
     * @Mapping\Property(name="nome", type="string")
     */
    public $name;

    /**
     * @Mapping\OneToOne(name="categoria", type="App\Model\Property\Category")
     *
     * @var Category
     */
    public $category;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    /**
     * @return Category
     */
    public function getCategory()
    {
        return $this->category;
    }

    /**
     * @param Category $category
     */
    public function setCategory($category)
    {
        $this->category = $category;
    }
}
