<?php

namespace App\Model\Property;

use App\DataTransformer\Mapping;
use App\Model\Shared\City;
use Services\Getrix\AnnunciTypes\Comune;
use Services\Getrix\AnnunciTypes\Contratto;
use Services\Getrix\AnnunciTypes\StatoAnnuncio;

/**
 * @Mapping\Struct(name="\Services\Getrix\AnnunciTypes\ListaAnnunci")
 */
class PropertiesListItem
{
    /**
     * @Mapping\Id
     * @Mapping\Property(name="idAnnuncio", type="integer")
     */
    public $id;
    /**
     * @Mapping\Property(name="codice", type="string")
     */
    public $code;
    /**
     * @Mapping\Property(name="superficie", type="integer")
     */
    public $surface;
    /**
     * @Mapping\OneToOne(name="stato", type="App\Model\Property\PropertyStatus")
     *
     * @var PropertyStatus
     */
    public $propertyStatus;
    /**
     * @Mapping\OneToMany(name="prezzi", type="App\Model\Property\Price")
     *
     * @var Price
     */
    public $prices;
    /**
     * @Mapping\OneToOne(name="contratto", type="App\Model\Property\Contract")
     *
     * @var Contract
     */
    public $contract;
    /**
     * @Mapping\Property(name="indirizzo", type="string")
     */
    public $address;
    /**
     * @Mapping\Property(name="numeroCivico", type="string")
     */
    public $addressNumber;
    /**
     * @Mapping\Property(name="indirizzoVisibile", type="integer")
     */
    public $isAddressVisible;
    /**
     * @Mapping\Property(name="flagIndirizzo", type="integer")
     */
    public $addressFlag;
    /**
     * @Mapping\OneToOne(name="tipologia", type="App\Model\Property\Tipology")
     *
     * @var Tipology
     */
    public $tipology;
    /**
     * @Mapping\OneToOne(name="comune", type="App\Model\Shared\City")
     *
     * @var City
     */
    public $city;
    /**
     * @Mapping\Property(name="dataModifica", type="date")
     *
     * @var \DateTime|null
     */
    public $editDate;
    /**
     * @Mapping\OneToOne(name="statoAnnuncio", type="App\Model\Property\Status")
     *
     * @var Status
     */
    public $status;
    /**
     * @Mapping\Property(name="ranking", type="string")
     */
    public $ranking;
    /**
     * @Mapping\Property(name="preferito", type="boolean")
     */
    public $preferred;
    /**
     * @Mapping\Property(name="fotoPrincipale", type="string")
     */
    public $mainThumbId;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * @param mixed $code
     */
    public function setCode($code)
    {
        $this->code = $code;
    }

    /**
     * @return mixed
     */
    public function getSurface()
    {
        return $this->surface;
    }

    /**
     * @param mixed $surface
     */
    public function setSurface($surface)
    {
        $this->surface = $surface;
    }

    /**
     * @return PropertyStatus
     */
    public function getPropertyStatus()
    {
        return $this->propertyStatus;
    }

    /**
     * @param PropertyStatus $propertyStatus
     */
    public function setPropertyStatus($propertyStatus)
    {
        $this->propertyStatus = $propertyStatus;
    }

    /**
     * @return mixed
     */
    public function getPrices()
    {
        return $this->prices;
    }

    /**
     * @param mixed $prices
     */
    public function setPrices($prices)
    {
        $this->prices = $prices;
    }

    /**
     * @return Contract
     */
    public function getContract()
    {
        return $this->contract;
    }

    /**
     * @param Contract $contract
     */
    public function setContract($contract)
    {
        $this->contract = $contract;
    }

    /**
     * @return mixed
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * @param mixed $address
     */
    public function setAddress($address)
    {
        $this->address = $address;
    }

    /**
     * @return mixed
     */
    public function getAddressNumber()
    {
        return $this->addressNumber;
    }

    /**
     * @param mixed $addressNumber
     */
    public function setAddressNumber($addressNumber)
    {
        $this->addressNumber = $addressNumber;
    }

    /**
     * @return mixed
     */
    public function getisAddressVisible()
    {
        return $this->isAddressVisible;
    }

    /**
     * @param mixed $isAddressVisible
     */
    public function setIsAddressVisible($isAddressVisible)
    {
        $this->isAddressVisible = $isAddressVisible;
    }

    /**
     * @return mixed
     */
    public function getAddressFlag()
    {
        return $this->addressFlag;
    }

    /**
     * @param mixed $addressFlag
     */
    public function setAddressFlag($addressFlag)
    {
        $this->addressFlag = $addressFlag;
    }

    /**
     * @return Tipology
     */
    public function getTipology()
    {
        return $this->tipology;
    }

    /**
     * @param Tipology $tipology
     */
    public function setTipology($tipology)
    {
        $this->tipology = $tipology;
    }

    /**
     * @return City
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * @param City $city
     */
    public function setCity($city)
    {
        $this->city = $city;
    }

    /**
     * @return \DateTime|null
     */
    public function getEditDate()
    {
        return $this->editDate;
    }

    /**
     * @param \DateTime|null $editDate
     */
    public function setEditDate($editDate)
    {
        $this->editDate = $editDate;
    }

    /**
     * @return Status
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param Status $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getRanking()
    {
        return $this->ranking;
    }

    /**
     * @param mixed $ranking
     */
    public function setRanking($ranking)
    {
        $this->ranking = $ranking;
    }

    /**
     * @return mixed
     */
    public function getPreferred()
    {
        return $this->preferred;
    }

    /**
     * @param mixed $preferred
     */
    public function setPreferred($preferred)
    {
        $this->preferred = $preferred;
    }

    /**
     * @return mixed
     */
    public function getMainThumbId()
    {
        return $this->mainThumbId;
    }

    /**
     * @param mixed $mainThumbId
     */
    public function setMainThumbId($mainThumbId)
    {
        $this->mainThumbId = $mainThumbId;
    }
}
