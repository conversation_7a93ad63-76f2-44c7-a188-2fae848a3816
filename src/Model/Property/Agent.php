<?php

declare(strict_types=1);

namespace App\Model\Property;

class Agent
{
    public int $id;
    public ?string $firstName;
    public ?string $lastName;
    public ?string $email;
    public ?int $profileImageId;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(?string $firstName)
    {
        $this->firstName = $firstName;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(?string $lastName)
    {
        $this->lastName = $lastName;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email)
    {
        $this->email = $email;
    }

    public function getProfileImageId(): ?int
    {
        return $this->profileImageId;
    }

    public function setProfileImageId(?int $profileImageId)
    {
        $this->profileImageId = $profileImageId;
    }
}
