<?php

namespace App\Model\Property;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="Services\Getrix\PortaliTypes\ImmobiliPubblicatiByPortale")
 */
class PropertyPublishInfo
{
    /**
     * @Mapping\Id
     * @Mapping\Property(name="idAnnuncio", type="integer")
     */
    public $id;
    /**
     * @Mapping\Property(name="published", type="bool")
     */
    public $published;
    /**
     * @Mapping\OneToOne(name="badgeZone", type="App\Model\Portal\PortalZonesBadge", nullable="true")
     */
    public $zonesBadge;
    /**
     * @Mapping\OneToOne(name="badgeRicercabile", type="App\Model\Portal\SearchablePortalBadge", nullable="true")
     */
    public $searchableBadge;
    /**
     * @Mapping\OneToOne(name="badgeErrore", type="App\Model\Portal\PortalErrorBadge", nullable="true")
     */
    public $errorBadge;
    /**
     * @Mapping\OneToMany(name="badgeLink", type="App\Model\Portal\PortalLinkBadge", nullable="true")
     */
    public $linkBadge;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return bool
     */
    public function getPublished()
    {
        return $this->published;
    }

    /**
     * @param bool $published
     */
    public function setPublished($published)
    {
        $this->published = $published;
    }

    /**
     * @return mixed
     */
    public function getZonesBadge()
    {
        return $this->zonesBadge;
    }

    /**
     * @param mixed $zonesBadge
     */
    public function setZonesBadge($zonesBadge)
    {
        $this->zonesBadge = $zonesBadge;
    }

    /**
     * @return mixed
     */
    public function getSearchableBadge()
    {
        return $this->searchableBadge;
    }

    /**
     * @param mixed $searchableBadge
     */
    public function setSearchableBadge($searchableBadge)
    {
        $this->searchableBadge = $searchableBadge;
    }

    /**
     * @return mixed
     */
    public function getErrorBadge()
    {
        return $this->errorBadge;
    }

    /**
     * @param mixed $errorBadge
     */
    public function setErrorBadge($errorBadge)
    {
        $this->errorBadge = $errorBadge;
    }

    /**
     * @return mixed
     */
    public function getLinkBadge()
    {
        return $this->linkBadge;
    }

    /**
     * @param mixed $linkBadge
     */
    public function setLinkBadge($linkBadge)
    {
        $this->linkBadge = $linkBadge;
    }
}
