<?php

declare(strict_types=1);

namespace App\Model\Searches;

class AdditionalInformation
{
    /** @var bool|null */
    private $withBox;

    /** @var bool|null */
    private $withTerraceBalcony;

    /** @var bool|null */
    private $toSell;

    public function isWithBox()
    {
        return $this->withBox;
    }

    public function setWithBox(bool $withBox = null)
    {
        $this->withBox = $withBox;
    }

    public function isWithTerraceBalcony()
    {
        return $this->withTerraceBalcony;
    }

    public function setWithTerraceBalcony(bool $withTerraceBalcony = null)
    {
        $this->withTerraceBalcony = $withTerraceBalcony;
    }

    public function isToSell()
    {
        return $this->toSell;
    }

    public function setToSell(bool $toSell = null)
    {
        $this->toSell = $toSell;
    }
}
