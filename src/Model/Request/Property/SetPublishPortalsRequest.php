<?php

declare(strict_types=1);

namespace App\Model\Request\Property;

class SetPublishPortalsRequest
{
    /**
     * @var int|null
     */
    private $propertyId;

    /**
     * @var int[]|null
     */
    private $portalIds;

    public function getPropertyId()
    {
        return $this->propertyId;
    }

    public function setPropertyId(
        int $propertyId = null
    ) {
        $this->propertyId = $propertyId;
    }

    public function getPortalIds()
    {
        return $this->portalIds;
    }

    public function setPortalIds(
        array $portalIds = null
    ) {
        $this->portalIds = $portalIds;
    }
}
