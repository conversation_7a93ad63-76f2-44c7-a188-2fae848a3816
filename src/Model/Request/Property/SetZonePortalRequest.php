<?php

declare(strict_types=1);

namespace App\Model\Request\Property;

class SetZonePortalRequest
{
    /**
     * @var int|null
     */
    private $portalId;

    /**
     * @var int|null
     */
    private $propertyId;

    /**
     * @var int|null
     */
    private $zoneId;

    public function getPortalId()
    {
        return $this->portalId;
    }

    public function setPortalId(
        int $portalId = null
    ) {
        $this->portalId = $portalId;
    }

    public function getPropertyId()
    {
        return $this->propertyId;
    }

    public function setPropertyId(
        int $propertyId = null
    ) {
        $this->propertyId = $propertyId;
    }

    public function getZoneId()
    {
        return $this->zoneId;
    }

    public function setZoneId(
        int $zoneId = null
    ) {
        $this->zoneId = $zoneId;
    }
}
