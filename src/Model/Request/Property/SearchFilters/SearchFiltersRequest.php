<?php

declare(strict_types=1);

namespace App\Model\Request\Property\SearchFilters;

use App\Model\Request\Shared\RangeRequest;

class SearchFiltersRequest
{
    /**
     * @var string|null
     */
    public $country;

    /**
     * @var string|null
     */
    public $region;

    /**
     * @var string|null
     */
    public $province;

    /**
     * @var int|null
     */
    public $city;

    /**
     * @var int[]|null
     */
    public $zones;

    /**
     * @var string|null
     */
    public $code;

    /**
     * @var PortalFiltersRequest|null
     */
    public $portals;

    /**
     * @var TipologiesFiltersRequest|null
     */
    public $tipologies;

    /**
     * @var int|null
     */
    public $rooms;

    /**
     * @var int|null
     */
    public $contract;

    /**
     * @var RangeRequest|null
     */
    public $price;

    /**
     * @var RangeRequest|null
     */
    public $surface;

    /**
     * @return string|null
     */
    public function getCountry()
    {
        return $this->country;
    }

    public function setCountry(
        string $country = null
    ) {
        $this->country = $country;
    }

    /**
     * @return string|null
     */
    public function getRegion()
    {
        return $this->region;
    }

    public function setRegion(
        string $region = null
    ) {
        $this->region = $region;
    }

    /**
     * @return string|null
     */
    public function getProvince()
    {
        return $this->province;
    }

    public function setProvince(
        string $province = null
    ) {
        $this->province = $province;
    }

    /**
     * @return int|null
     */
    public function getCity()
    {
        return $this->city;
    }

    public function setCity(
        int $city = null
    ) {
        $this->city = $city;
    }

    /**
     * @return int[]|null
     */
    public function getZones()
    {
        return $this->zones;
    }

    /**
     * @param int[]|null $zones
     */
    public function setZones(
        array $zones = null
    ) {
        $this->zones = $zones;
    }

    /**
     * @return string|null
     */
    public function getCode()
    {
        return $this->code;
    }

    public function setCode(
        string $code = null
    ) {
        $this->code = $code;
    }

    /**
     * @return PortalFiltersRequest|null
     */
    public function getPortals()
    {
        return $this->portals;
    }

    public function setPortals(
        PortalFiltersRequest $portals = null
    ) {
        $this->portals = $portals;
    }

    /**
     * @return TipologiesFiltersRequest|null
     */
    public function getTipologies()
    {
        return $this->tipologies;
    }

    public function setTipologies(
        TipologiesFiltersRequest $tipologies = null
    ) {
        $this->tipologies = $tipologies;
    }

    /**
     * @return int|null
     */
    public function getRooms()
    {
        return $this->rooms;
    }

    public function setRooms(
        int $rooms = null
    ) {
        $this->rooms = $rooms;
    }

    /**
     * @return int|null
     */
    public function getContract()
    {
        return $this->contract;
    }

    public function setContract(
        int $contract = null
    ) {
        $this->contract = $contract;
    }

    /**
     * @return RangeRequest|null
     */
    public function getPrice()
    {
        return $this->price;
    }

    public function setPrice(
        RangeRequest $price = null
    ) {
        $this->price = $price;
    }

    /**
     * @return RangeRequest|null
     */
    public function getSurface()
    {
        return $this->surface;
    }

    public function setSurface(
        RangeRequest $surface = null
    ) {
        $this->surface = $surface;
    }
}
