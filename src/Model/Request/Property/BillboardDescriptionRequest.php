<?php

declare(strict_types=1);

namespace App\Model\Request\Property;

class BillboardDescriptionRequest
{
    /**
     * @var string
     */
    private $text;

    /**
     * @var int
     */
    private $alignment;

    /**
     * @var int
     */
    private $size;

    public function getText(): string
    {
        return $this->text;
    }

    public function setText(
        string $text
    ) {
        $this->text = $text;
    }

    public function getAlignment(): int
    {
        return $this->alignment;
    }

    public function setAlignment(
        int $alignment
    ) {
        $this->alignment = $alignment;
    }

    public function getSize(): int
    {
        return $this->size;
    }

    public function setSize(
        int $size
    ) {
        $this->size = $size;
    }
}
