<?php

declare(strict_types=1);

namespace App\Model\Request\Agency;

class CreateAgencyRequest
{
    /** @var string */
    private $companyType;

    /** @var string */
    private $firstName;

    /** @var string */
    private $lastName;

    /** @var string */
    private $companyName;

    /** @var string */
    private $email;

    /** @var int */
    private $cityId;

    /** @var string */
    private $phoneNumber;

    /** @var string */
    private $phonePrefix;

    /** @var bool */
    private $privacy;

    /** @var string|null */
    private $vatNumber = null;

    public function getCompanyType()
    {
        return $this->companyType;
    }

    public function setCompanyType(string $companyType)
    {
        $this->companyType = $companyType;
    }

    public function getFirstName()
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName)
    {
        $this->firstName = $firstName;
    }

    public function getLastName()
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName)
    {
        $this->lastName = $lastName;
    }

    public function getCompanyName()
    {
        return $this->companyName;
    }

    public function setCompanyName(string $companyName)
    {
        $this->companyName = $companyName;
    }

    public function getEmail()
    {
        return $this->email;
    }

    public function setEmail(string $email)
    {
        $this->email = $email;
    }

    public function getCityId()
    {
        return $this->cityId;
    }

    public function setCityId(int $cityId)
    {
        $this->cityId = $cityId;
    }

    public function getPhoneNumber()
    {
        return $this->phoneNumber;
    }

    public function setPhoneNumber(string $phoneNumber)
    {
        $this->phoneNumber = $phoneNumber;
    }

    public function getPhonePrefix()
    {
        return $this->phonePrefix;
    }

    public function setPhonePrefix(string $phonePrefix)
    {
        $this->phonePrefix = $phonePrefix;
    }

    public function isPrivacy()
    {
        return $this->privacy;
    }

    public function setPrivacy(bool $privacy)
    {
        $this->privacy = $privacy;
    }

    /** @return string|null */
    public function getVatNumber()
    {
        return $this->vatNumber;
    }

    public function setVatNumber(string $vatNumber = null)
    {
        $this->vatNumber = $vatNumber;
    }
}
