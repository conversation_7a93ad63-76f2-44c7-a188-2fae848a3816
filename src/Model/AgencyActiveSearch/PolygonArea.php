<?php

declare(strict_types=1);

namespace App\Model\AgencyActiveSearch;

use App\Model\AgencyActiveSearch\Geography\Coordinates;

class PolygonArea
{
    /** @var Coordinates[]|null */
    private $points;

    /**
     * @return Coordinates[]|null
     */
    public function getPoints()
    {
        return $this->points;
    }

    /**
     * @param Coordinates[]|null $points
     */
    public function setPoints(array $points = null)
    {
        $this->points = $points;
    }
}
