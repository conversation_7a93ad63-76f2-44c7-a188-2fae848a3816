<?php

namespace App\Model\ApiModel\Agency;

use App\Annotation\TaggedProperty;
use App\Model\ApiModel\AbstractApiModel;
use App\Model\ApiModel\Geography\AddressRequest;

class AgencyRegisteredOfficeRequest extends AbstractApiModel
{
    /**
     * @var int|null
     *
     * @TaggedProperty({\App\Helper\AgencyApiClientHelper::UPDATE_AGENCY_ADMINISTRATIVE_PROPERTY_TAG})
     */
    private $cityId;

    /**
     * @var AddressRequest|null
     *
     * @TaggedProperty({\App\Helper\AgencyApiClientHelper::UPDATE_AGENCY_ADMINISTRATIVE_PROPERTY_TAG})
     */
    private $address;

    /**
     * @var string|null
     *
     * @TaggedProperty({\App\Helper\AgencyApiClientHelper::UPDATE_AGENCY_ADMINISTRATIVE_PROPERTY_TAG})
     */
    private $cap;

    public function getCityId()
    {
        return $this->cityId;
    }

    public function setCityId(int $cityId = null)
    {
        $this->cityId = $cityId;
    }

    public function getAddress()
    {
        return $this->address;
    }

    public function setAddress(AddressRequest $address = null)
    {
        $this->address = $address;
    }

    public function getCap()
    {
        return $this->cap;
    }

    public function setCap(string $cap = null)
    {
        $this->cap = $cap;
    }
}
