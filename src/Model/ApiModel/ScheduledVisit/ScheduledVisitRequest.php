<?php

namespace App\Model\ApiModel\ScheduledVisit;

use App\Model\ApiModel\AbstractApiModel;

class ScheduledVisitRequest extends AbstractApiModel
{
    /**
     * @var int|null
     */
    private $id;

    /**
     * @var int|null
     */
    private $agentId;

    /**
     * @var string|null
     */
    private $startTime;

    /**
     * @var string|null
     */
    private $endTime;

    /**
     * @var string|null
     */
    private $scheduledTime;

    /**
     * @var string|null
     */
    private $externalId;

    /**
     * @var string|null
     */
    private $link;

    /**
     * @var string|null
     */
    private $note;

    /**
     * @var int|null
     */
    private $visitType;

    /**
     * @var \DateTime|null
     */
    private $created;

    /**
     * @var \DateTime|null
     */
    private $modified;

    /**
     * @var ScheduledVisitGuestRequest[]|null
     */
    private $guests;

    /**
     * @var int|null
     */
    private $adId;

    /**
     * @return int|null
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    /**
     * @return int|null
     */
    public function getAgentId()
    {
        return $this->agentId;
    }

    public function setAgentId(int $agent = null)
    {
        $this->agentId = $agent;
    }

    /**
     * @return string|null
     */
    public function getStartTime()
    {
        return $this->startTime;
    }

    public function setStartTime(string $startTime = null)
    {
        $this->startTime = $startTime;
    }

    /**
     * @return string|null
     */
    public function getEndTime()
    {
        return $this->endTime;
    }

    public function setEndTime(string $endTime = null)
    {
        $this->endTime = $endTime;
    }

    /**
     * @return string|null
     */
    public function getScheduledTime()
    {
        return $this->scheduledTime;
    }

    public function setScheduledTime(string $scheduledTime = null)
    {
        $this->scheduledTime = $scheduledTime;
    }

    /**
     * @return string|null
     */
    public function getExternalId()
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId = null)
    {
        $this->externalId = $externalId;
    }

    /**
     * @return string|null
     */
    public function getLink()
    {
        return $this->link;
    }

    public function setLink(string $link = null)
    {
        $this->link = $link;
    }

    /**
     * @return string|null
     */
    public function getNote()
    {
        return $this->note;
    }

    public function setNote(string $note = null)
    {
        $this->note = $note;
    }

    /**
     * @return \DateTime|null
     */
    public function getCreated()
    {
        return $this->created;
    }

    public function setCreated(\DateTime $created = null)
    {
        $this->created = $created;
    }

    /**
     * @return \DateTime|null
     */
    public function getModified()
    {
        return $this->modified;
    }

    public function setModified(\DateTime $modified = null)
    {
        $this->modified = $modified;
    }

    /**
     * @return ScheduledVisitGuestRequest[]|null
     */
    public function getGuests()
    {
        return $this->guests;
    }

    /** @param ScheduledVisitGuestRequest[]|null $guests */
    public function setGuests(array $guests = null)
    {
        $this->guests = $guests;
    }

    /**
     * @return int|null
     */
    public function getAdId()
    {
        return $this->adId;
    }

    public function setAdId(int $adId = null)
    {
        $this->adId = $adId;
    }

    /**
     * @return int|null
     */
    public function getVisitType(): int
    {
        return $this->visitType;
    }

    /**
     * @param int|null $visitType
     */
    public function setVisitType(int $visitType)
    {
        $this->visitType = $visitType;
    }
}
