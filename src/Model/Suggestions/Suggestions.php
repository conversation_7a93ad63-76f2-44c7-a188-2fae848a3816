<?php

declare(strict_types=1);

namespace App\Model\Suggestions;

class Suggestions
{
    private string $term = '';
    /** @var Suggestion[] */
    private array $ids = [];
    /** @var Suggestion[] */
    private array $cities = [];
    /** @var Suggestion[] */
    private array $codes = [];
    /** @var Suggestion[] */
    private array $address = [];
    /** @var Suggestion[] */
    private array $macroZones = [];
    /** @var Suggestion[] */
    private array $provinces = [];
    /** @var Suggestion[] */
    private array $regions = [];

    public function getTerm(): string
    {
        return $this->term;
    }

    public function setTerm(string $term): void
    {
        $this->term = $term;
    }

    public function getIds(): array
    {
        return $this->ids;
    }

    public function setIds(array $ids): self
    {
        $this->ids = $ids;

        return $this;
    }

    public function getCities(): array
    {
        return $this->cities;
    }

    public function setCities(array $cities): void
    {
        $this->cities = $cities;
    }

    public function getCodes(): array
    {
        return $this->codes;
    }

    public function setCodes(array $codes): void
    {
        $this->codes = $codes;
    }

    public function getAddress(): array
    {
        return $this->address;
    }

    public function setAddress(array $address): void
    {
        $this->address = $address;
    }

    public function getMacroZones(): array
    {
        return $this->macroZones;
    }

    public function setMacroZones(array $macroZones): void
    {
        $this->macroZones = $macroZones;
    }

    public function getProvinces(): array
    {
        return $this->provinces;
    }

    public function setProvinces(array $provinces): void
    {
        $this->provinces = $provinces;
    }

    public function getRegions(): array
    {
        return $this->regions;
    }

    public function setRegions(array $regions): void
    {
        $this->regions = $regions;
    }
}
