<?php

namespace App\Model\Agent;

class Contact
{
    /** @var int|null */
    private $id;

    /** @var string|null */
    private $number;

    /** @var string|null */
    private $type;

    /** @var bool|null */
    private $favorite;

    /** @var bool|null */
    private $public;

    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    public function getNumber()
    {
        return $this->number;
    }

    public function setNumber(string $number = null)
    {
        $this->number = $number;
    }

    public function getType()
    {
        return $this->type;
    }

    public function setType(string $type = null)
    {
        $this->type = $type;
    }

    public function getFavorite()
    {
        return $this->favorite;
    }

    public function setFavorite(bool $favorite = null)
    {
        $this->favorite = $favorite;
    }

    public function getPublic()
    {
        return $this->public;
    }

    public function setPublic(bool $public = null)
    {
        $this->public = $public;
    }
}
