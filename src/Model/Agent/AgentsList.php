<?php

declare(strict_types=1);

namespace App\Model\Agent;

class AgentsList
{
    /** @var int|null */
    public $resultsCount;

    /** @var Agent[]|null */
    public $agents;

    /** @return int|null */
    public function getResultsCount()
    {
        return $this->resultsCount;
    }

    public function setResultsCount(
        int $resultsCount = null
    ) {
        $this->resultsCount = $resultsCount;
    }

    /** @return Agent[]|null */
    public function getAgents()
    {
        return $this->agents;
    }

    public function setAgents(
        array $agents = null
    ) {
        $this->agents = $agents;
    }
}
