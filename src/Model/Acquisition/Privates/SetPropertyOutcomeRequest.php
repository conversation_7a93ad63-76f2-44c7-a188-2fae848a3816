<?php

namespace App\Model\Acquisition\Privates;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\AcquisizioneTypes\SetEsitoAnnuncioAcquisizioneMsg")
 */
class SetPropertyOutcomeRequest
{
    /**
     * @Mapping\Property(name="idAd", type="string")
     */
    public $propertyId;
    /**
     * @Mapping\OneToOne(name="esito", type="App\Model\Acquisition\Privates\Outcome")
     *
     * @var \App\Model\Acquisition\Privates\Outcome
     */
    public $outcome;

    /**
     * @return mixed
     */
    public function getPropertyId()
    {
        return $this->propertyId;
    }

    /**
     * @param mixed $propertyId
     */
    public function setPropertyId($propertyId)
    {
        $this->propertyId = $propertyId;
    }

    /**
     * @return \App\Model\Acquisition\Privates\Outcome
     */
    public function getOutcome()
    {
        return $this->outcome;
    }

    /**
     * @param \App\Model\Acquisition\Privates\Outcome $outcome
     */
    public function setOutcome($outcome)
    {
        $this->outcome = $outcome;
    }
}
