<?php

namespace App\Model\Acquisition\Privates;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\AcquisizioneTypes\GetListaAnnunciAcquisizioneMsg")
 */
class GetAcquisitionListRequest
{
    /**
     * @Mapping\OneToOne(name="filtri", type="App\Model\Acquisition\Privates\SearchFilters\AcquisitionFilters")
     *
     * @var \App\Model\Acquisition\Privates\SearchFilters\AcquisitionFilters
     */
    public $filters;
    /**
     * @Mapping\OneToOne(name="paginazione", type="App\Model\Shared\PaginationRequest")
     *
     * @var \App\Model\Shared\PaginationRequest
     */
    public $pagination;
    /**
     * @Mapping\OneToOne(name="ordinamento", type="App\Model\Shared\Order")
     *
     * @var \App\Model\Shared\Order
     */
    public $ordering;

    /**
     * @return \App\Model\Acquisition\Privates\SearchFilters\AcquisitionFilters
     */
    public function getFilters()
    {
        return $this->filters;
    }

    /**
     * @param \App\Model\Acquisition\Privates\SearchFilters\AcquisitionFilters $filters
     */
    public function setFilters($filters)
    {
        $this->filters = $filters;
    }

    /**
     * @return \App\Model\Shared\PaginationRequest
     */
    public function getPagination()
    {
        return $this->pagination;
    }

    /**
     * @param \App\Model\Shared\PaginationRequest $pagination
     */
    public function setPagination($pagination)
    {
        $this->pagination = $pagination;
    }

    /**
     * @return \App\Model\Shared\Order
     */
    public function getOrdering()
    {
        return $this->ordering;
    }

    /**
     * @param \App\Model\Shared\Order $ordering
     */
    public function setOrdering($ordering)
    {
        $this->ordering = $ordering;
    }
}
