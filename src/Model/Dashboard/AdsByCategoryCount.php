<?php

namespace App\Model\Dashboard;

use App\Model\Property\Category;

class AdsByCategoryCount
{
    /**
     * @var Category|null
     */
    private $category;

    /**
     * @var int|null
     */
    private $sell;

    /**
     * @var int|null
     */
    private $rent;

    /**
     * @var int|null
     */
    private $total;

    public function getCategory()
    {
        return $this->category;
    }

    public function setCategory(Category $category = null)
    {
        $this->category = $category;
    }

    public function getSell()
    {
        return $this->sell;
    }

    public function setSell(int $sell = null)
    {
        $this->sell = $sell;
    }

    public function getRent()
    {
        return $this->rent;
    }

    public function setRent(int $rent = null)
    {
        $this->rent = $rent;
    }

    public function getTotal()
    {
        return $this->total;
    }

    public function setTotal(int $total = null)
    {
        $this->total = $total;
    }
}
