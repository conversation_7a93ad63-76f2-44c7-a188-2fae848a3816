<?php

namespace App\Model\Messaging;

class Message
{
    /** @var string|null */
    public $id;

    /** @var string|null */
    public $authorUuid;

    /** @var string|null */
    public $textPlain;

    /** @var string|null */
    public $textHtml;

    /** @var string|null */
    public $createdAt;

    /** @var string|null */
    public $status;

    /** @var Attachment[]|null */
    public $attachments;

    /** @var MessageData|null */
    public $data;

    public function getId()
    {
        return $this->id;
    }

    public function setId(string $id = null)
    {
        $this->id = $id;
    }

    public function getAuthorUuid()
    {
        return $this->authorUuid;
    }

    public function setAuthorUuid(string $authorUuid = null)
    {
        $this->authorUuid = $authorUuid;
    }

    public function getTextPlain()
    {
        return $this->textPlain;
    }

    public function setTextPlain(string $textPlain = null)
    {
        $this->textPlain = $textPlain;
    }

    public function getTextHtml()
    {
        return $this->textHtml;
    }

    public function setTextHtml(string $textHtml = null)
    {
        $this->textHtml = $textHtml;
    }

    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    public function setCreatedAt(string $createdAt = null)
    {
        $this->createdAt = $createdAt;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function setStatus(string $status = null)
    {
        $this->status = $status;
    }

    public function getAttachments()
    {
        return $this->attachments;
    }

    public function setAttachments($attachments = null)
    {
        $this->attachments = $attachments;
    }

    public function getData()
    {
        return $this->data;
    }

    public function setData(MessageData $data = null)
    {
        $this->data = $data;
    }
}
