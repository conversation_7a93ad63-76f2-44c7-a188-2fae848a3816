<?php

namespace App\Model\Messaging;

use App\Model\Property\Agent;
use App\Model\Property\Contract;
use App\Model\Property\GeographyInformation;
use App\Model\Property\Status;
use App\Model\Property\TypologyV2;

class Property
{
    /** @var int */
    public $id;

    /** @var string|null */
    public $reference;

    /** @var TypologyV2|null */
    public $typology;

    /** @var GeographyInformation|null */
    public $geographyInformation;

    /** @var Agent|null */
    public $agent;

    /** @var array|null */
    public $prices;

    /** @var Status|null */
    public $status;

    /** @var Contract|null */
    public $contract;

    /** @var int|null */
    public $surface;

    /** @var int|null */
    public $rooms;

    /** @var int|null */
    public $imageId;

    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
    }

    public function getReference()
    {
        return $this->reference;
    }

    public function setReference(string $reference = null)
    {
        $this->reference = $reference;
    }

    public function getTypology()
    {
        return $this->typology;
    }

    public function setTypology(TypologyV2 $typology = null)
    {
        $this->typology = $typology;
    }

    public function getGeographyInformation()
    {
        return $this->geographyInformation;
    }

    public function setGeographyInformation(GeographyInformation $geographyInformation = null)
    {
        $this->geographyInformation = $geographyInformation;
    }

    public function getContract()
    {
        return $this->contract;
    }

    public function setContract(Contract $contract = null)
    {
        $this->contract = $contract;
    }

    public function getImageId()
    {
        return $this->imageId;
    }

    public function setImageId(int $imageId = null)
    {
        $this->imageId = $imageId;
    }

    public function getPrices()
    {
        return $this->prices;
    }

    public function setPrices(array $prices = null)
    {
        $this->prices = $prices;
    }

    public function getSurface()
    {
        return $this->surface;
    }

    public function setSurface(int $surface = null)
    {
        $this->surface = $surface;
    }

    public function getRooms()
    {
        return $this->rooms;
    }

    public function setRooms(int $rooms = null)
    {
        $this->rooms = $rooms;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function setStatus(Status $status = null)
    {
        $this->status = $status;
    }

    public function getAgent()
    {
        return $this->agent;
    }

    public function setAgent(Agent $agent = null)
    {
        $this->agent = $agent;
    }
}
