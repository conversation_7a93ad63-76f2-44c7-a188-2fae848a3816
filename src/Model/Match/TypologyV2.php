<?php

declare(strict_types=1);

namespace App\Model\Match;

class TypologyV2
{
    /** @var int|null */
    private $id;

    /** @var string|null */
    private $name;

    /** @var TypologyV2|null */
    private $parent;

    /**
     * @return int|null
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    /**
     * @return string|null
     */
    public function getName()
    {
        return $this->name;
    }

    public function setName(string $name = null)
    {
        $this->name = $name;
    }

    /**
     * @return TypologyV2|null
     */
    public function getParent()
    {
        return $this->parent;
    }

    public function setParent(self $parent = null)
    {
        $this->parent = $parent;
    }
}
