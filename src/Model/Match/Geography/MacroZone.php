<?php

declare(strict_types=1);

namespace App\Model\Match\Geography;

class MacroZone
{
    /** @var int */
    private $id;

    /** @var int|null */
    private $number;

    /** @var string|null */
    private $name;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
    }

    public function getNumber()
    {
        return $this->number;
    }

    public function setNumber(int $number = null)
    {
        $this->number = $number;
    }

    public function getName()
    {
        return null === $this->number ? sprintf(
            '%s%s - %s',
            $this->number < 10 ? ' ' : '',
            $this->number,
            $this->name
        ) : $this->name;
    }

    public function setName(string $name = null)
    {
        $this->name = $name;
    }
}
