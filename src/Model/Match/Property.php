<?php

declare(strict_types=1);

namespace App\Model\Match;

class Property
{
    /** @var int|null */
    private $id;

    /** @var bool|null */
    private $isMain;

    /** @var int|null */
    private $surface;

    /** @var int|null */
    private $rooms;

    /** @var int|null */
    private $bathrooms;

    /** @var Batch|null */
    public $batch;

    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    public function getIsMain(): bool
    {
        return $this->isMain;
    }

    public function setIsMain(bool $isMain)
    {
        $this->isMain = $isMain;
    }

    public function getSurface()
    {
        return $this->surface;
    }

    public function setSurface(int $surface = null)
    {
        $this->surface = $surface;
    }

    public function getRooms()
    {
        return $this->rooms;
    }

    public function setRooms(int $rooms = null)
    {
        $this->rooms = $rooms;
    }

    public function getBathrooms()
    {
        return $this->bathrooms;
    }

    public function setBathrooms(int $bathrooms = null)
    {
        $this->bathrooms = $bathrooms;
    }

    /** @return Batch|null */
    public function getBatch()
    {
        return $this->batch;
    }

    public function setBatch(
        Batch $batch = null
    ) {
        $this->batch = $batch;
    }
}
