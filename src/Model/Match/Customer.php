<?php

declare(strict_types=1);

namespace App\Model\Match;

class Customer
{
    /** @var int */
    private $id;

    /** @var string|null */
    private $firstname;

    /** @var string|null */
    private $lastname;

    /** @var string|null */
    private $email;

    /** @var string|null */
    private $mobilePhone;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
    }

    /**
     * @return string|null
     */
    public function getFirstname()
    {
        return $this->firstname;
    }

    public function setFirstname(string $firstname = null)
    {
        $this->firstname = $firstname;
    }

    /**
     * @return string|null
     */
    public function getLastname()
    {
        return $this->lastname;
    }

    public function setLastname(string $lastname = null)
    {
        $this->lastname = $lastname;
    }

    /**
     * @return string|null
     */
    public function getEmail()
    {
        return $this->email;
    }

    public function setEmail(string $email = null)
    {
        $this->email = $email;
    }

    /**
     * @return string|null
     */
    public function getMobilePhone()
    {
        return $this->mobilePhone;
    }

    public function setMobilePhone(string $mobilePhone = null)
    {
        $this->mobilePhone = $mobilePhone;
    }
}
