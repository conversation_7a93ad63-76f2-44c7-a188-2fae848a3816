<?php

namespace App\Model\Portal;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\PortaliTypes\BadgePortaleZone")
 */
class PortalZonesBadge
{
    /**
     * @Mapping\Property(name="selezionato", type="integer")
     */
    public $selected;
    /**
     * @Mapping\Property(name="associato", type="bool")
     */
    public $associated;
    /**
     * @Mapping\Property(name="zone", type="array")
     */
    public $zones;

    /**
     * @return mixed
     */
    public function getSelected()
    {
        return $this->selected;
    }

    /**
     * @param mixed $selected
     */
    public function setSelected($selected)
    {
        $this->selected = $selected;
    }

    /**
     * @return mixed
     */
    public function getAssociated()
    {
        return $this->associated;
    }

    /**
     * @param mixed $associated
     */
    public function setAssociated($associated)
    {
        $this->associated = $associated;
    }

    /**
     * @return mixed
     */
    public function getZones()
    {
        return $this->zones;
    }

    /**
     * @param mixed $zones
     */
    public function setZones($zones)
    {
        $this->zones = $zones;
    }
}
