<?php

namespace App\Model\Portal;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\AnnunciTypes\AnnullaPubblicazioneAnnuncioByPortaleResponse")
 */
class UnpublishResponse
{
    /**
     * @Mapping\Property(name="statoPubblicazioneImmobiliPortale", type="integer")
     */
    public $propertiesPublishStatus;
    /**
     * @Mapping\OneToMany(name="spaziPubblicitari", type="App\Model\Portal\PropertySendStatus")
     *
     * @var PropertySendStatus[]|null
     */
    public $advertisementSpaces;

    /**
     * @return mixed
     */
    public function getPropertiesPublishStatus()
    {
        return $this->propertiesPublishStatus;
    }

    /**
     * @param mixed $propertiesPublishStatus
     */
    public function setPropertiesPublishStatus($propertiesPublishStatus)
    {
        $this->propertiesPublishStatus = $propertiesPublishStatus;
    }

    /**
     * @return PropertySendStatus[]|null
     */
    public function getAdvertisementSpaces()
    {
        return $this->advertisementSpaces;
    }

    /**
     * @param PropertySendStatus[]|null $advertisementSpaces
     */
    public function setAdvertisementSpaces($advertisementSpaces)
    {
        $this->advertisementSpaces = $advertisementSpaces;
    }
}
