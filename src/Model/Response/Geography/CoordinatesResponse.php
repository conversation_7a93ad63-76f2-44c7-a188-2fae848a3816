<?php

declare(strict_types=1);

namespace App\Model\Response\Geography;

class CoordinatesResponse
{
    /**
     * @var float|null
     */
    private $latitude;

    /**
     * @var float|null
     */
    private $longitude;

    public function getLatitude()
    {
        return $this->latitude;
    }

    public function setLatitude(
        float $latitude = null
    ) {
        $this->latitude = $latitude;
    }

    public function getLongitude()
    {
        return $this->longitude;
    }

    public function setLongitude(
        float $longitude = null
    ) {
        $this->longitude = $longitude;
    }
}
