<?php

declare(strict_types=1);

namespace App\Model\Response\Searches\Match\Geography;

class AddressResponse
{
    /**
     * @var string|null
     */
    public $street;

    /**
     * @var string|null
     */
    public $number;

    /**
     * @return string|null
     */
    public function getStreet()
    {
        return $this->street;
    }

    public function setStreet(string $street = null)
    {
        $this->street = $street;
    }

    /**
     * @return string|null
     */
    public function getNumber()
    {
        return $this->number;
    }

    public function setNumber(string $number = null)
    {
        $this->number = $number;
    }
}
