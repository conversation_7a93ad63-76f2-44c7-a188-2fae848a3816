<?php

declare(strict_types=1);

namespace App\Model\Response\Searches\Search;

use App\Model\Response\Property\ListingAdResponse;
use Carbon\Carbon;

class ListProposedAdResponse
{
    /** @var Carbon */
    private $date;

    /** @var ListingAdResponse */
    private $property;

    public function getDate(): Carbon
    {
        return $this->date;
    }

    public function setDate(Carbon $date)
    {
        $this->date = $date;
    }

    public function getProperty(): ListingAdResponse
    {
        return $this->property;
    }

    public function setProperty(ListingAdResponse $ad)
    {
        $this->property = $ad;
    }
}
