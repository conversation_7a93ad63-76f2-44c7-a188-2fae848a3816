<?php

declare(strict_types=1);

namespace App\Model\Response\Searches\Search;

class PropertyTypeResponse
{
    /** @var int */
    private $id;

    /** @var string */
    private $name;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name)
    {
        $this->name = $name;
    }
}
