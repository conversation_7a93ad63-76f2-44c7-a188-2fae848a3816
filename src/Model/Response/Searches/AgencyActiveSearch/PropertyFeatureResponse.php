<?php

declare(strict_types=1);

namespace App\Model\Response\Searches\AgencyActiveSearch;

class PropertyFeatureResponse
{
    /** @var string */
    private $name;

    /** @var bool */
    private $value;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name)
    {
        $this->name = $name;
    }

    public function getValue(): bool
    {
        return $this->value;
    }

    public function setValue(bool $value)
    {
        $this->value = $value;
    }
}
