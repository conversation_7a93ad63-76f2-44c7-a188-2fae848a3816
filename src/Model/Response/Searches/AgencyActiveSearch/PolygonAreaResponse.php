<?php

declare(strict_types=1);

namespace App\Model\Response\Searches\AgencyActiveSearch;

use App\Model\Response\Searches\AgencyActiveSearch\Geography\CoordinatesResponse;

class PolygonAreaResponse
{
    /**
     * @var CoordinatesResponse[]|null
     */
    private $points;

    /**
     * @return CoordinatesResponse[]|null
     */
    public function getPoints()
    {
        return $this->points;
    }

    /**
     * @param CoordinatesResponse[]|null $points
     */
    public function setPoints(array $points = null)
    {
        $this->points = $points;
    }
}
