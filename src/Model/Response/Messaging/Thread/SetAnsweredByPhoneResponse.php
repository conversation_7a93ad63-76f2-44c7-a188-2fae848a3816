<?php

declare(strict_types=1);

namespace App\Model\Response\Messaging\Thread;

class SetAnsweredByPhoneResponse
{
    /** @var string|null */
    private $answeredByPhone;

    /** @var string|null */
    private $modified;

    /**
     * @return string|null
     */
    public function getAnsweredByPhone()
    {
        return $this->answeredByPhone;
    }

    public function setAnsweredByPhone(string $answeredByPhone = null)
    {
        $this->answeredByPhone = $answeredByPhone;
    }

    /**
     * @return string|null
     */
    public function getModified()
    {
        return $this->modified;
    }

    public function setModified(string $modified = null)
    {
        $this->modified = $modified;
    }
}
