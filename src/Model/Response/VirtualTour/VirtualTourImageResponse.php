<?php

declare(strict_types=1);

namespace App\Model\Response\VirtualTour;

class VirtualTourImageResponse
{
    /** @var int|null */
    private $idImage;

    /** @var int|null */
    private $idAdMultimediaFloor;

    /** @var string|null */
    private $caption;

    /** @var int|null */
    private $sorting;

    /** @var int|null */
    private $status;

    /** @var VirtualTourImageSources */
    private $src;

    /**
     * @return int|null
     */
    public function getIdImage()
    {
        return $this->idImage;
    }

    public function setIdImage(int $idImage = null)
    {
        $this->idImage = $idImage;
    }

    /**
     * @return int|null
     */
    public function getIdAdMultimediaFloor()
    {
        return $this->idAdMultimediaFloor;
    }

    public function setIdAdMultimediaFloor(int $idAdMultimediaFloor = null)
    {
        $this->idAdMultimediaFloor = $idAdMultimediaFloor;
    }

    /**
     * @return string|null
     */
    public function getCaption()
    {
        return $this->caption;
    }

    public function setCaption(string $caption = null)
    {
        $this->caption = $caption;
    }

    /**
     * @return int|null
     */
    public function getSorting()
    {
        return $this->sorting;
    }

    public function setSorting(int $sorting = null)
    {
        $this->sorting = $sorting;
    }

    /**
     * @return int|null
     */
    public function getStatus()
    {
        return $this->status;
    }

    public function setStatus(int $status = null)
    {
        $this->status = $status;
    }

    /**
     * @return VirtualTourImageSources
     */
    public function getSrc()
    {
        return $this->src;
    }

    /**
     * @param VirtualTourImageSources $src
     */
    public function setSrc(VirtualTourImageSources $src = null)
    {
        $this->src = $src;
    }
}
