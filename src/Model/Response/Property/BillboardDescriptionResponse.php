<?php

namespace App\Model\Response\Property;

class BillboardDescriptionResponse
{
    /**
     * @var string
     */
    private $text;

    /**
     * @var int
     */
    private $alignment;

    /**
     * @var int
     */
    private $size;

    public function getText(): string
    {
        return $this->text;
    }

    public function setText(
        string $text
    ) {
        $this->text = $text;
    }

    public function getAlignment(): int
    {
        return $this->alignment;
    }

    public function setAlignment(
        int $alignment
    ) {
        $this->alignment = $alignment;
    }

    public function getSize(): int
    {
        return $this->size;
    }

    public function setSize(
        int $size
    ) {
        $this->size = $size;
    }
}
