<?php

declare(strict_types=1);

namespace App\Model\Response\Property;

class ListingAdResponse
{
    /** @var int */
    private $id;

    /** @var ContractResponse */
    private $contract;

    /** @var string|null */
    private $code;

    /** @var TypologyV2Response|null */
    private $typologyV2;

    /** @var PropertyPriceResponse[]|null */
    private $prices;

    /** @var int|null */
    private $mainImage;

    /** @var string */
    private $mainThumbUrl;

    /** @var PropertyTypologyResponse|null */
    private $propertyTypology;

    /** @var PropertyResponse[] */
    private $properties;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
    }

    public function getContract(): ContractResponse
    {
        return $this->contract;
    }

    public function setContract(ContractResponse $contract)
    {
        $this->contract = $contract;
    }

    /**
     * @return string|null
     */
    public function getCode()
    {
        return $this->code;
    }

    public function setCode(string $code = null)
    {
        $this->code = $code;
    }

    /**
     * @return array|null
     */
    public function getPrices()
    {
        return $this->prices;
    }

    /**
     * @param array $prices
     */
    public function setPrices(array $prices = null)
    {
        $this->prices = $prices;
    }

    /**
     * @return int|null
     */
    public function getMainImage()
    {
        return $this->mainImage;
    }

    /**
     * @param int $mainImage
     */
    public function setMainImage(int $mainImage = null)
    {
        $this->mainImage = $mainImage;
    }

    /**
     * @return PropertyResponse[]
     */
    public function getProperties()
    {
        return $this->properties;
    }

    /**
     * @param PropertyResponse[] $properties
     */
    public function setProperties(array $properties)
    {
        $this->properties = $properties;
    }

    /**
     * @return string
     */
    public function getMainThumbUrl()
    {
        return $this->mainThumbUrl;
    }

    /**
     * @param string $mainThumbUrl
     */
    public function setMainThumbUrl(string $mainThumbUrl = null)
    {
        $this->mainThumbUrl = $mainThumbUrl;
    }

    /**
     * @return TypologyV2Response|null
     */
    public function getTypologyV2(): TypologyV2Response
    {
        return $this->typologyV2;
    }

    /**
     * @param TypologyV2Response|null $typologyV2
     */
    public function setTypologyV2(TypologyV2Response $typologyV2)
    {
        $this->typologyV2 = $typologyV2;
    }

    /**
     * @return PropertyTypologyResponse|null
     */
    public function getPropertyTypology(): PropertyTypologyResponse
    {
        return $this->propertyTypology;
    }

    /**
     * @param PropertyTypologyResponse|null $propertyTypology
     */
    public function setPropertyTypology(PropertyTypologyResponse $propertyTypology)
    {
        $this->propertyTypology = $propertyTypology;
    }
}
