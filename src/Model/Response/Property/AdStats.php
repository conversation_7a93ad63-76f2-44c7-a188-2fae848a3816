<?php

declare(strict_types=1);

namespace App\Model\Response\Property;

class AdStats
{
    private ?AdStatsData $impressions;
    private ?AdStatsData $mailAlertImpressions;
    private ?AdStatsData $clientDetailViews;
    private ?AdStatsData $phoneClicks;
    private ?AdStatsData $leads;
    private ?AdStatsData $visitRequests;
    private ?AdStatsData $bookmarks;
    private ?AdStatsData $blacklists;
    private ?AdStatsData $shares;
    private ?AdStatsData $mapPinSelected;
    private ?AdStatsData $priceProposals;
    private ?AdStatsData $contacts;
    private ?AdStatsData $views;
    private ?AdStatsData $clicks;

    private ?array $performanceIndexes;

    public function getImpressions(): ?AdStatsData
    {
        return $this->impressions;
    }

    public function setImpressions(?AdStatsData $impressions = null)
    {
        $this->impressions = $impressions;
    }

    public function getMailAlertImpressions(): ?AdStatsData
    {
        return $this->mailAlertImpressions;
    }

    public function setMailAlertImpressions(?AdStatsData $mailAlertImpressions = null)
    {
        $this->mailAlertImpressions = $mailAlertImpressions;
    }

    public function getClientDetailViews(): ?AdStatsData
    {
        return $this->clientDetailViews;
    }

    public function setClientDetailViews(?AdStatsData $clientDetailViews = null)
    {
        $this->clientDetailViews = $clientDetailViews;
    }

    public function getPhoneClicks(): ?AdStatsData
    {
        return $this->phoneClicks;
    }

    public function setPhoneClicks(?AdStatsData $phoneClicks = null)
    {
        $this->phoneClicks = $phoneClicks;
    }

    public function getLeads(): ?AdStatsData
    {
        return $this->leads;
    }

    public function setLeads(?AdStatsData $leads = null)
    {
        $this->leads = $leads;
    }

    public function getVisitRequests(): ?AdStatsData
    {
        return $this->visitRequests;
    }

    public function setVisitRequests(?AdStatsData $visitRequests = null)
    {
        $this->visitRequests = $visitRequests;
    }

    public function getBookmarks(): ?AdStatsData
    {
        return $this->bookmarks;
    }

    public function setBookmarks(?AdStatsData $bookmarks = null)
    {
        $this->bookmarks = $bookmarks;
    }

    public function getBlacklists(): ?AdStatsData
    {
        return $this->blacklists;
    }

    public function setBlacklists(?AdStatsData $blacklists = null)
    {
        $this->blacklists = $blacklists;
    }

    public function getShares(): ?AdStatsData
    {
        return $this->shares;
    }

    public function setShares(?AdStatsData $shares = null)
    {
        $this->shares = $shares;
    }

    public function getMapPinSelected(): ?AdStatsData
    {
        return $this->mapPinSelected;
    }

    public function setMapPinSelected(?AdStatsData $mapPinSelected = null)
    {
        $this->mapPinSelected = $mapPinSelected;
    }

    public function getPriceProposals(): ?AdStatsData
    {
        return $this->priceProposals;
    }

    public function setPriceProposals(?AdStatsData $priceProposals = null)
    {
        $this->priceProposals = $priceProposals;
    }

    public function getContacts(): ?AdStatsData
    {
        return $this->contacts;
    }

    public function setContacts(?AdStatsData $contacts = null)
    {
        $this->contacts = $contacts;
    }

    public function getViews(): ?AdStatsData
    {
        return $this->views;
    }

    public function setViews(?AdStatsData $views = null)
    {
        $this->views = $views;
    }

    public function getClicks(): ?AdStatsData
    {
        return $this->clicks;
    }

    public function setClicks(?AdStatsData $clicks = null)
    {
        $this->clicks = $clicks;
    }

    public function getPerformanceIndexes(): ?array
    {
        return $this->performanceIndexes;
    }

    public function setPerformanceIndexes(?array $performanceIndexes = null)
    {
        $this->performanceIndexes = $performanceIndexes;
    }
}
