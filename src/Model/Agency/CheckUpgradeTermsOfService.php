<?php

namespace App\Model\Agency;

class CheckUpgradeTermsOfService
{
    /** @var bool|null */
    private $needsUpdate;

    /** @var string|null */
    private $label;

    /** @var string|null */
    private $description;

    /** @var string|null */
    private $versionText;

    /** @var \DateTime|null */
    private $acceptanceDate;

    public function getNeedsUpdate()
    {
        return $this->needsUpdate;
    }

    public function setNeedsUpdate(bool $needsUpdate = null)
    {
        $this->needsUpdate = $needsUpdate;
    }

    public function getLabel()
    {
        return $this->label;
    }

    public function setLabel(string $label = null)
    {
        $this->label = $label;
    }

    public function getDescription()
    {
        return $this->description;
    }

    public function setDescription(string $description = null)
    {
        $this->description = $description;
    }

    public function getVersionText()
    {
        return $this->versionText;
    }

    public function setVersionText(string $versionText = null)
    {
        $this->versionText = $versionText;
    }

    public function getAcceptanceDate()
    {
        return $this->acceptanceDate;
    }

    public function setAcceptanceDate(\DateTime $acceptanceDate = null)
    {
        $this->acceptanceDate = $acceptanceDate;
    }
}
