<?php

declare(strict_types=1);

namespace App\Model\Agency;

class AgencyEvaluationZoneType
{
    /** @var int|null */
    private $id;

    /** @var string|null */
    private $name;

    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    public function getName()
    {
        return $this->name;
    }

    public function setName(string $name = null)
    {
        $this->name = $name;
    }
}
