<?php

namespace App\Model\Geography;

class MacroZone
{
    /**
     * @var int|null
     */
    public $id;

    /**
     * @var string|null
     */
    public $name;

    /**
     * @var string|null
     */
    public $nameSn;

    /**
     * @var string|null
     */
    public $keyUrl;

    /**
     * @var City|null
     */
    public $city;

    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    public function getName()
    {
        return $this->name;
    }

    public function setName(string $name = null)
    {
        $this->name = $name;
    }

    public function getNameSn()
    {
        return $this->nameSn;
    }

    public function setNameSn(string $nameSn = null)
    {
        $this->nameSn = $nameSn;
    }

    public function getKeyUrl()
    {
        return $this->keyUrl;
    }

    public function setKeyUrl(string $keyUrl = null)
    {
        $this->keyUrl = $keyUrl;
    }

    public function getCity()
    {
        return $this->city;
    }

    public function setCity(City $city = null)
    {
        $this->city = $city;
    }
}
