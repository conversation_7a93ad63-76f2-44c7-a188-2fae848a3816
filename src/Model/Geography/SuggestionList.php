<?php

declare(strict_types=1);

namespace App\Model\Geography;

class SuggestionList
{
    /** @var int|null */
    public $resultsCount;

    /** @var Suggestion[]|null */
    public $suggestions;

    /** @return int|null */
    public function getResultsCount()
    {
        return $this->resultsCount;
    }

    public function setResultsCount(
        int $resultsCount = null
    ) {
        $this->resultsCount = $resultsCount;
    }

    /** @return Suggestion[]|null */
    public function getSuggestions()
    {
        return $this->suggestions;
    }

    /** @param Suggestion[]|null $suggestions */
    public function setSuggestions(
        array $suggestions = null
    ) {
        $this->suggestions = $suggestions;
    }
}
