<?php

declare(strict_types=1);

namespace App\Utils;

use App\Constants\ErrorMessage;
use App\Entity\Agency;
use App\Entity\Agent;
use App\Exception\SessionException;
use App\Security\User\GetrixUser;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;

class SessionUtils
{
    /** @var TokenStorageInterface */
    private $tokenStorage;

    public function __construct(
        TokenStorageInterface $tokenStorage
    ) {
        $this->tokenStorage = $tokenStorage;
    }

    /** @throws SessionException */
    public function getUser(): GetrixUser
    {
        $token = $this->tokenStorage->getToken();
        if (!$token instanceof TokenInterface) {
            throw new SessionException(ErrorMessage::SESSION_INVALID_TOKEN);
        }

        $user = $token->getUser();
        if (!$user instanceof GetrixUser) {
            throw new SessionException(ErrorMessage::SESSION_INVALID_USER);
        }

        return $user;
    }

    /** @throws SessionException */
    public function getAgency(): Agency
    {
        $agency = $this
            ->getUser()
            ->getAgenzia();

        if (!$agency instanceof Agency) {
            throw new SessionException(ErrorMessage::SESSION_INVALID_AGENCY);
        }

        return $agency;
    }

    /** @throws SessionException */
    public function getAgencyId(): int
    {
        $user = $this->getUser();

        $agency = $user->getAgenzia();
        if (!$agency instanceof Agency) {
            throw new SessionException(ErrorMessage::SESSION_INVALID_AGENCY);
        }

        $agencyId = $agency->idAgenzia;
        if (null === $agencyId) {
            throw new SessionException(ErrorMessage::SESSION_INVALID_AGENCY_ID);
        }

        return $agencyId;
    }

    /** @throws SessionException */
    public function getAgentId(): int
    {
        $user  = $this->getUser();

        $agent = $user->getAgente();
        if (!$agent instanceof Agent) {
            throw new SessionException(ErrorMessage::SESSION_INVALID_AGENT);
        }

        $agentId = $agent->idAgente;
        if (null === $agentId) {
            throw new SessionException(ErrorMessage::SESSION_INVALID_AGENT_ID);
        }

        return $agentId;
    }

    /** @throws SessionException */
    public function getAgentUuid(): string
    {
        $user  = $this->getUser();

        $agent = $user->getAgente();
        if (!$agent instanceof Agent) {
            throw new SessionException(ErrorMessage::SESSION_INVALID_AGENT);
        }

        $agentUuid = $agent->uuid;
        if (null === $agentUuid) {
            throw new SessionException(ErrorMessage::SESSION_INVALID_AGENT_ID);
        }

        return $agentUuid;
    }

    /** @throws SessionException */
    public function getAgent(): Agent
    {
        $user  = $this->getUser();

        $agent = $user->getAgente();
        if (!$agent instanceof Agent) {
            throw new SessionException(ErrorMessage::SESSION_INVALID_AGENT);
        }

        return $agent;
    }
}
