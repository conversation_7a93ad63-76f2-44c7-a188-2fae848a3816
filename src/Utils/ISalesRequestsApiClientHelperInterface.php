<?php

namespace App\Utils;

use stdClass;

interface ISalesRequestsApiClientHelperInterface
{
    public function getListSalesRequests(int $results, int $page, int $agencyId): array;

    public function getSalesRequestItem(string $resourceId, int $agencyId): array;

    public function getSalesRequestOutcomesLookup(): array;

    public function setSalesRequestStatus(int $agencyId, string $resourceId, array $payload): stdClass;
}
