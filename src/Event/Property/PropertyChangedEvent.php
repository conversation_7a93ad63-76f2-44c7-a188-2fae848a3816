<?php

declare(strict_types=1);

namespace App\Event\Property;

use Symfony\Component\EventDispatcher\Event;

class PropertyChangedEvent extends Event
{
    const NAME = 'app.property.changed';

    /** @var string */
    private $message;

    /** @var string */
    private $method;

    /** @var string */
    private $url;

    /** @var int */
    private $propertyId;

    /** @var array */
    private $payload;

    /** @var array */
    private $data;

    public function __construct(
        string $message,
        string $method,
        string $url,
        int $propertyId,
        array $payload,
        array $data
    ) {
        $this->message    = $message;
        $this->method     = $method;
        $this->url        = $url;
        $this->propertyId = $propertyId;
        $this->data       = $data;
        $this->payload    = $payload;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function getPropertyId(): int
    {
        return $this->propertyId;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function getPayload(): array
    {
        return $this->payload;
    }
}
