<?php

namespace App\Event\DataProvider;

use Symfony\Component\EventDispatcher\Event;

class CacheBeforeEvent extends Event
{
    const NAME = 'app.data_provider.cache_before';

    protected $strace;

    protected $key;

    protected $params;

    public function __construct($key, $params = null)
    {
        $this->key    = $key;
        $this->params = $params;
        $this->strace = array_map(function ($step) {
            $step['args'] = '*OMIT*';

            return $step;
        }, debug_backtrace(false));
    }

    public function getStackTrace()
    {
        return $this->strace;
    }

    public function getKey()
    {
        return $this->key;
    }

    public function getParams()
    {
        return $this->params;
    }
}
