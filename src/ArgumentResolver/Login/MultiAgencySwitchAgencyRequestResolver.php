<?php

declare(strict_types=1);

namespace App\ArgumentResolver\Login;

use App\DataMapper\RequestDataToRequestModel\Login\MultiAgencySwitchAgencyRequestDataMapper;
use App\Model\Request\Login\MultiAgencySwitchAgencyRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ArgumentValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;

class MultiAgencySwitchAgencyRequestResolver implements ArgumentValueResolverInterface
{
    /** @var MultiAgencySwitchAgencyRequestDataMapper */
    private $multiAgencySwitchAgencyRequestDataMapper;

    public function __construct(
        MultiAgencySwitchAgencyRequestDataMapper $multiAgencySwitchAgencyRequestDataMapper
    ) {
        $this->multiAgencySwitchAgencyRequestDataMapper = $multiAgencySwitchAgencyRequestDataMapper;
    }

    public function supports(
        Request $request,
        ArgumentMetadata $argument
    ): bool {
        return MultiAgencySwitchAgencyRequest::class === $argument->getType();
    }

    public function resolve(
        Request $request,
        ArgumentMetadata $argument
    ): \Generator {
        yield $this->multiAgencySwitchAgencyRequestDataMapper->map(
            $request->request->all()
        );
    }
}
