<?php

declare(strict_types=1);

namespace App\ArgumentResolver\Property;

use App\DataMapper\RequestDataToRequestModel\Property\CountriesDataMapper;
use App\Model\Request\Property\CountriesRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ArgumentValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;

class CountriesRequestResolver implements ArgumentValueResolverInterface
{
    /**
     * @var CountriesDataMapper
     */
    private $countriesDataMapper;

    public function __construct(
        CountriesDataMapper $countriesDataMapper
    ) {
        $this->countriesDataMapper = $countriesDataMapper;
    }

    public function supports(
        Request $request,
        ArgumentMetadata $argument
    ): bool {
        return CountriesRequest::class === $argument->getType();
    }

    public function resolve(
        Request $request,
        ArgumentMetadata $argument
    ): \Generator {
        yield $this->countriesDataMapper->map($request->query->all());
    }
}
