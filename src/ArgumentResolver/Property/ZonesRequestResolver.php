<?php

declare(strict_types=1);

namespace App\ArgumentResolver\Property;

use App\DataMapper\RequestDataToRequestModel\Property\ZonesDataMapper;
use App\Model\Request\Property\ZonesRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ArgumentValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;

class ZonesRequestResolver implements ArgumentValueResolverInterface
{
    /**
     * @var ZonesDataMapper
     */
    private $zonesDataMapper;

    public function __construct(
        ZonesDataMapper $zonesDataMapper
    ) {
        $this->zonesDataMapper = $zonesDataMapper;
    }

    public function supports(
        Request $request,
        ArgumentMetadata $argument
    ): bool {
        return ZonesRequest::class === $argument->getType();
    }

    public function resolve(
        Request $request,
        ArgumentMetadata $argument
    ): \Generator {
        yield $this->zonesDataMapper->map($request->query->all());
    }
}
