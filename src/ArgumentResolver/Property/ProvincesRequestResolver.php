<?php

declare(strict_types=1);

namespace App\ArgumentResolver\Property;

use App\DataMapper\RequestDataToRequestModel\Property\ProvincesDataMapper;
use App\Model\Request\Property\ProvincesRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ArgumentValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;

class ProvincesRequestResolver implements ArgumentValueResolverInterface
{
    /**
     * @var ProvincesDataMapper
     */
    private $provincesDataMapper;

    public function __construct(
        ProvincesDataMapper $provincesDataMapper
    ) {
        $this->provincesDataMapper = $provincesDataMapper;
    }

    public function supports(
        Request $request,
        ArgumentMetadata $argument
    ): bool {
        return ProvincesRequest::class === $argument->getType();
    }

    public function resolve(
        Request $request,
        ArgumentMetadata $argument
    ): \Generator {
        yield $this->provincesDataMapper->map($request->query->all());
    }
}
