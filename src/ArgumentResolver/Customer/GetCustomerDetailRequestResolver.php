<?php

declare(strict_types=1);

namespace App\ArgumentResolver\Customer;

use App\DataMapper\RequestDataToRequestModel\Customer\GetCustomerDetailRequestDataMapper;
use App\Model\Request\Customers\GetCustomerDetailRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ArgumentValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;

class GetCustomerDetailRequestResolver implements ArgumentValueResolverInterface
{
    /** @var GetCustomerDetailRequestDataMapper */
    private $dataMapper;

    public function __construct(GetCustomerDetailRequestDataMapper $dataMapper)
    {
        $this->dataMapper = $dataMapper;
    }

    public function supports(Request $request, ArgumentMetadata $argument)
    {
        return GetCustomerDetailRequest::class === $argument->getType();
    }

    public function resolve(Request $request, ArgumentMetadata $argument)
    {
        yield $this->dataMapper->map($request->query->all());
    }
}
