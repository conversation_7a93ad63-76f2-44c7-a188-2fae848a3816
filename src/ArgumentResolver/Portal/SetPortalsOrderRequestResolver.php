<?php

declare(strict_types=1);

namespace App\ArgumentResolver\Portal;

use App\DataMapper\RequestDataToRequestModel\Portal\SetPortalsOrderDataMapper;
use App\Model\Request\Portal\SetPortalsOrderRequest;
use App\Serializer\Serializer;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ArgumentValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;

class SetPortalsOrderRequestResolver implements ArgumentValueResolverInterface
{
    /**
     * @var SetPortalsOrderDataMapper
     */
    private $setPortalsOrderDataMapper;

    public function __construct(
        SetPortalsOrderDataMapper $setPortalsOrderDataMapper
    ) {
        $this->setPortalsOrderDataMapper = $setPortalsOrderDataMapper;
    }

    public function supports(
        Request $request,
        ArgumentMetadata $argument
    ): bool {
        return SetPortalsOrderRequest::class === $argument->getType();
    }

    public function resolve(
        Request $request,
        ArgumentMetadata $argument
    ): \Generator {
        yield $this->setPortalsOrderDataMapper->map(Serializer::getSerializer()->decode($request->getContent(), 'json'));
    }
}
