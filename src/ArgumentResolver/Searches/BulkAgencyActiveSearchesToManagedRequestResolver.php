<?php

declare(strict_types=1);

namespace App\ArgumentResolver\Searches;

use App\DataMapper\RequestDataToRequestModel\Searches\BulkAgencyActiveSearchesToManagedRequestDataMapper;
use App\Model\Request\Searches\BulkAgencyActiveSearchesToManagedRequest;
use App\Serializer\Serializer;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ArgumentValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;

class BulkAgencyActiveSearchesToManagedRequestResolver implements ArgumentValueResolverInterface
{
    private BulkAgencyActiveSearchesToManagedRequestDataMapper $bulkAgencyActiveSearchesToManagedRequestDataMapper;

    public function __construct(BulkAgencyActiveSearchesToManagedRequestDataMapper $bulkAgencyActiveSearchesToManagedRequestDataMapper)
    {
        $this->bulkAgencyActiveSearchesToManagedRequestDataMapper = $bulkAgencyActiveSearchesToManagedRequestDataMapper;
    }

    public function supports(Request $request, ArgumentMetadata $argument): bool
    {
        return BulkAgencyActiveSearchesToManagedRequest::class === $argument->getType();
    }

    public function resolve(Request $request, ArgumentMetadata $argument): \Generator
    {
        yield $this->bulkAgencyActiveSearchesToManagedRequestDataMapper->map(
            Serializer::getSerializer()->decode($request->getContent(), 'json')
        );
    }
}
