<?php

namespace App\Repository;

use App\Exception\TwoFactorAuthentication\ClientErrorException;
use App\Exception\TwoFactorAuthentication\ServiceException;
use App\Security\User\GetrixUser;
use App\Service\Base\TwoFactorAuthenticationProvider;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Exception\AuthenticationException;

class AuthorizedDeviceRepository
{
    private $twoFactorAuthenticationProvider;
    private $tokenStorage;

    public function __construct(TokenStorageInterface $tokenStorage, TwoFactorAuthenticationProvider $twoFactorAuthenticationProvider)
    {
        $this->tokenStorage                    = $tokenStorage;
        $this->twoFactorAuthenticationProvider = $twoFactorAuthenticationProvider;
    }

    public function find($sessionUuid)
    {
        try {
            return $this->twoFactorAuthenticationProvider->getSession($sessionUuid);
        } catch (ClientErrorException $e) {
            return $this->getEmptySession();
        } catch (ServiceException $e) {
            return $this->getEmptySession();
        }
    }

    public function findAll()
    {
        $idAgenzia = $this->getIdAgenzia();

        try {
            return $this->twoFactorAuthenticationProvider->getSessionList($idAgenzia);
        } catch (ClientErrorException $e) {
            return $this->getEmptySessionList();
        } catch (ServiceException $e) {
            return $this->getEmptySessionList();
        }
    }

    public function delete($sessionUuid)
    {
        if (!$this->isMine($sessionUuid)) {
            throw new AccessDeniedException();
        }

        return $this->twoFactorAuthenticationProvider->deleteSession($sessionUuid);
    }

    private function isMine($sessionUuid)
    {
        $session = $this->find($sessionUuid);

        if (!isset($session->user) || !isset($session->user->user_extid)) {
            return false;
        }

        return 0 === strcmp($session->user->user_extid, $this->getIdAgenzia());
    }

    private function getIdAgenzia()
    {
        $token = $this->tokenStorage->getToken();

        if (empty($token)) {
            throw new AuthenticationException();
        }

        $user = $token->getUser();

        if (empty($user) || !($user instanceof GetrixUser)) {
            throw new AuthenticationException();
        }

        return $user->getIdAgenzia();
    }

    private function getEmptySessionList()
    {
        $list               = new \stdClass();
        $list->currentPage  = 0;
        $list->results      = [];
        $list->totalPage    = 0;
        $list->totalResults = 0;

        return $list;
    }

    private function getEmptySession()
    {
        $session        = new \stdClass();
        $session->valid = false;

        return $session;
    }
}
