<?php

declare(strict_types=1);

namespace App\DTO\ClientAppLogin;

class ClientAppLoginJwtToken
{
    private string $backurl;
    private string $clientId;
    private ?string $deviceFingerprint = null;

    public function getBackurl(): string
    {
        return $this->backurl;
    }

    public function setBackurl(
        string $backurl
    ): void {
        $this->backurl = $backurl;
    }

    public function getClientId(): string
    {
        return $this->clientId;
    }

    public function setClientId(
        string $clientId
    ): void {
        $this->clientId = $clientId;
    }

    public function getDeviceFingerprint(): ?string
    {
        return $this->deviceFingerprint;
    }

    public function setDeviceFingerprint(
        ?string $deviceFingerprint
    ): void {
        $this->deviceFingerprint = $deviceFingerprint;
    }
}
