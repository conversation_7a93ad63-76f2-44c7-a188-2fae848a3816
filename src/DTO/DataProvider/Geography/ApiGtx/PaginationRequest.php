<?php

declare(strict_types=1);

namespace App\DTO\DataProvider\Geography\ApiGtx;

class PaginationRequest
{
    /** @var int|null */
    private $offset;

    /** @var int|null */
    private $limit;

    /**
     * @return int|null
     */
    public function getOffset()
    {
        return $this->offset;
    }

    public function setOffset(
        int $offset = null
    ) {
        $this->offset = $offset;
    }

    /**
     * @return int|null
     */
    public function getLimit()
    {
        return $this->limit;
    }

    public function setLimit(
        int $limit = null
    ) {
        $this->limit = $limit;
    }
}
