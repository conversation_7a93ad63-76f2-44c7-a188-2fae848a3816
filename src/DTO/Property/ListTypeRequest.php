<?php

declare(strict_types=1);

namespace App\DTO\Property;

use App\Constants\Property\TypeConstants;
use App\Exception\InvalidArgumentException;
use Symfony\Component\HttpFoundation\Request;

class ListTypeRequest
{
    const VALID_TYPES = [
        TypeConstants::ACTIVE,
        TypeConstants::ARCHIVED,
        TypeConstants::DRAFT,
        TypeConstants::SOLD,
        TypeConstants::PROPERTY,
        TypeConstants::FAVOURITE,
    ];
    private string $type;

    /**
     * @throws InvalidArgumentException
     */
    public static function createFromRequest(Request $request): self
    {
        if (
            empty($request->attributes->get('type'))
            || !static::isValidType($request->attributes->get('type'))
        ) {
            throw new InvalidArgumentException('Invalid type');
        }

        return (new self())
            ->setType($request->attributes->get('type'));
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    private static function isValidType(string $type): bool
    {
        return \in_array($type, static::VALID_TYPES);
    }
}
