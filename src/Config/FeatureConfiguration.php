<?php

namespace App\Config;

    use Symfony\Component\Config\Definition\Builder\TreeBuilder;
    use Symfony\Component\Config\Definition\ConfigurationInterface;

    class FeatureConfiguration implements ConfigurationInterface
    {
        const FEATURE_DISABLED    = 0;
        const FEATURE_ENABLED     = 1;
        const FEATURE_MANTAINANCE = 2;

        /**
         * Generates the configuration tree builder.
         *
         * @return \Symfony\Component\Config\Definition\Builder\TreeBuilder The tree builder
         */
        public function getConfigTreeBuilder(): TreeBuilder
        {
            /** @var TreeBuilder $treeBuilder */
            $treeBuilder = new TreeBuilder();
            $rootNode    = $treeBuilder->root('feature_toggle');

            $rootNode
                ->performNoDeepMerging()
                ->children()
                    ->arrayNode('features')
                        ->canBeUnset()
                        ->treatNullLike([])
                        ->prototype('array')
                            ->children()
                                ->scalarNode('name')->defaultValue('')->end()
                                ->integerNode('enabled')->defaultValue(self::FEATURE_ENABLED)->end()
                                ->scalarNode('group')->defaultValue('')->end()
                        ->end()
                    ->end()
                ->end()
                    ->arrayNode('groups')
                        ->canBeUnset()
                        ->treatNullLike([])
                        ->prototype('array')
                            ->children()
                                ->scalarNode('name')->defaultValue('')->end()
                                ->integerNode('enabled')->defaultValue(self::FEATURE_ENABLED)->end()
                        ->end()
                    ->end();

            return $treeBuilder;
        }
    }
