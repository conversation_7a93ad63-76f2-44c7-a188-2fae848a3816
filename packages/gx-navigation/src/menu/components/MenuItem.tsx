import { Icon } from '@gx-design/icon';
import { useMediaMatch } from '@gx-design/use-media-match';
import { MenuConfig, Pathname } from '../../types';
import clsx from 'clsx';
import { FeaturesMenuAdapter } from '../FeaturesProviders';
import { NotificationBadge } from '@gx-design/notification-badge';
import { memo } from 'react';
import { isSubmenuHidden } from '../../shared/utils/type-guards';

type MenuItemProps = {
  handleSideMenuOpening: (menuItem: MenuConfig) => void;
  resetDelay: () => void;
  menuItem: MenuConfig;
  path: Pathname;
  showLabel: boolean;
  isSubmenuOpen: boolean;
  isActive?: boolean;
};

export const MenuItem = memo(function MenuItemMemo({
  handleSideMenuOpening,
  resetDelay,
  menuItem,
  path,
  showLabel,
  isSubmenuOpen,
  isActive,
}: MenuItemProps) {
  const isDesktop = useMediaMatch('largeDesktop');

  if (!menuItem.item) {
    return null;
  }

  /**
   * emitting a warning here because
   * the badge is still not supported for first level menu items,
   * it stll need to be designed in future if needed we will implement it
   */
  if (menuItem.item.badge) {
    console.warn('MenuItem: badge is not supported for first level menu items', menuItem.item);
  }
  return (
    <FeaturesMenuAdapter menu={menuItem} level={1} contexts={menuItem.item.contexts}>
      {(adaptedMenu) => {
        if (!adaptedMenu.item) {
          return null;
        }
        const { label, icon, href } = adaptedMenu.item;

        return (
          <li>
            <a
              href={
                !adaptedMenu.subMenu || isSubmenuHidden(adaptedMenu.subMenu)
                  ? href || path
                  : undefined
              }
              className={clsx('gx-navigation-sidemenu__item', {
                'gx-navigation-sidemenu__item--active': isActive,
                'gx-navigation-sidemenu__item--subMenuOpen': isSubmenuOpen,
              })}
              onMouseEnter={isDesktop ? () => handleSideMenuOpening(adaptedMenu) : undefined}
              onClick={!isDesktop ? () => handleSideMenuOpening(adaptedMenu) : undefined}
              onMouseLeave={isDesktop ? resetDelay : undefined}
            >
              <div>
                <div className='gx-navigation-sidemenu__itemIcon'>
                  {/* @ts-expect-error actually the icons used for this library, **should** have the --active suffix  */}
                  {icon && <Icon name={`${isActive ? icon + '--active' : icon}`} />}
                  {adaptedMenu.item?.notificationBadge &&
                    (isDesktop ? (
                      <NotificationBadge number={adaptedMenu.item.notificationBadge.number} />
                    ) : (
                      <NotificationBadge />
                    ))}
                </div>
                <span
                  className={clsx({
                    'gx-is-hidden-md-up': !showLabel,
                    'gx-navigation-sidemenu__itemLabel': true,
                  })}
                >
                  {label}
                </span>
              </div>
              {adaptedMenu.subMenu && (
                <Icon
                  className='gx-is-hidden-md-up gx-navigation-sidemenu__itemMore'
                  name='arrow-right'
                />
              )}
            </a>
          </li>
        );
      }}
    </FeaturesMenuAdapter>
  );
});
