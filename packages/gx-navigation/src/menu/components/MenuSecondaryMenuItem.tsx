import { Icon } from '@gx-design/icon';
import { NotificationBadge } from '@gx-design/notification-badge';
import { Tooltip } from '@gx-design/tooltip';
import clsx from 'clsx';
import { Ref, forwardRef, memo, useMemo } from 'react';
import { trans } from '../../shared/translations';
import { MenuConfig, Pathname } from '../../types';
import { FeaturesMenuAdapter } from '../FeaturesProviders';
import { useMenuCurrentOpenedSectionDispatcher } from './MenuCurrentOpenedSection';
import { MenuSecondaryMenuSubItem } from './MenuSecondaryMenuSubItem';
import { isSubmenuHidden } from '../../shared/utils/type-guards';
import { Badge } from '@gx-design/badge';

type MenuSecondaryMenuItemProps = {
  item: MenuConfig;
  href: string;
  isActive?: boolean;
  activePath?: Pathname;
  isOpen: boolean;
  expanded?: boolean;
};

type MenuSecondaryMenuItemLinkProps = {
  isSubmenuActive: boolean;
  isSubmenuHidden: boolean;
  item: MenuConfig;
  href: string;
  isActive?: boolean;
  subMenuOpen: boolean;
};

const MenuSecondaryMenuItemLink: React.FC<MenuSecondaryMenuItemLinkProps> = forwardRef(
  (
    { isSubmenuActive, isSubmenuHidden, item, isActive, href, subMenuOpen },
    ref: Ref<HTMLAnchorElement>,
  ) => {
    const setPathRecord = useMenuCurrentOpenedSectionDispatcher();

    if (!item || !item.item || item.item.hidden) {
      return null;
    }

    return (
      <a
        ref={ref}
        className={clsx('gx-navigation-sidemenu__menuItem', {
          'has--child-active': isSubmenuActive,
          'is-active': isActive && (!item.subMenu || isSubmenuHidden),
        })}
        onClick={
          item.subMenu &&
          (() => {
            setPathRecord((prevState) => ({
              ...Object.keys(prevState).reduce<typeof prevState>((acc, key) => {
                acc[key] = false;
                return acc;
              }, {}),
              [href]: !prevState[href],
            }));
          })
        }
        href={
          item.item.href ||
          (item.subMenu && isSubmenuHidden ? href : !item.subMenu ? href : undefined)
        }
      >
        <div>
          {item.item.icon && <Icon name={item.item.icon} />}
          <span>{item.item.label}</span>
        </div>
        {item.item?.notificationBadge && (
          <NotificationBadge number={item.item.notificationBadge.number} />
        )}
        {item.item?.badge && (
          <Badge style={item.item.badge.style} text={trans(item.item.badge.text)} />
        )}
        {item.subMenu && !isSubmenuHidden && (
          <Icon className={`${subMenuOpen ? 'rotate-icon' : ''}`} name='arrow-down' />
        )}
      </a>
    );
  },
);

MenuSecondaryMenuItemLink.displayName = 'MenuSecondaryMenuItemLink';

export const MenuSecondaryMenuItem: React.FC<MenuSecondaryMenuItemProps> = memo(
  function MenuSecondaryMenuItemMemo({
    item,
    isActive,
    href,
    isOpen: isSubmenuActiveOrExpanded,
    activePath,
  }: MenuSecondaryMenuItemProps) {
    const isEverySubmenuHidden = useMemo(
      () => (item.subMenu ? isSubmenuHidden(item.subMenu) : false),
      [item.subMenu],
    );

    const isSubmenuActive = useMemo(() => {
      if (item.subMenu && isEverySubmenuHidden) {
        return Object.entries(item.subMenu).some(([key]) => key === activePath);
      }
      return false;
    }, [item.subMenu, activePath, isEverySubmenuHidden]);

    if (!item || !item.item) {
      return null;
    }

    return (
      <li>
        {item.item.tooltip ? (
          <Tooltip position='right' text={item.item.tooltip}>
            <MenuSecondaryMenuItemLink
              isSubmenuActive={isSubmenuActive}
              isSubmenuHidden={isEverySubmenuHidden}
              href={href}
              item={item}
              isActive={isActive}
              subMenuOpen={isSubmenuActiveOrExpanded}
            />
          </Tooltip>
        ) : (
          <MenuSecondaryMenuItemLink
            isSubmenuActive={isSubmenuActive}
            isSubmenuHidden={isEverySubmenuHidden}
            href={href}
            item={item}
            isActive={isActive}
            subMenuOpen={isSubmenuActiveOrExpanded}
          />
        )}
        {item.subMenu && !isEverySubmenuHidden && isSubmenuActiveOrExpanded && (
          <ul className='gx-navigation-sidemenu__subMenu'>
            {Object.entries(item.subMenu).map(([key, value]: [Pathname, MenuConfig]) => {
              return (
                <FeaturesMenuAdapter
                  key={key}
                  menu={value}
                  contexts={value?.item?.contexts}
                  level={3}
                >
                  {(secondLevelAdaptedMenu) => {
                    if (!secondLevelAdaptedMenu || !secondLevelAdaptedMenu.item) {
                      return null;
                    }

                    return (
                      <>
                        {secondLevelAdaptedMenu.item?.tooltip ? (
                          <Tooltip position='right' text={secondLevelAdaptedMenu.item.tooltip}>
                            <MenuSecondaryMenuSubItem
                              isActive={key === activePath}
                              href={secondLevelAdaptedMenu.item.href || key}
                              subMenuItem={secondLevelAdaptedMenu.item}
                            />
                          </Tooltip>
                        ) : (
                          <MenuSecondaryMenuSubItem
                            isActive={key === activePath}
                            href={secondLevelAdaptedMenu.item.href || key}
                            subMenuItem={secondLevelAdaptedMenu.item}
                          />
                        )}
                      </>
                    );
                  }}
                </FeaturesMenuAdapter>
              );
            })}
          </ul>
        )}
      </li>
    );
  },
);
