services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false
    bind:
      $softwareOldAppVersion: false
      $softwareOldVersionBaseUrl: '%env(GETRIX_OLD_VERSION_BASEURL)%'
      $redirectImmoGtxStatus: '%env(bool:REDIRECT_IMMO_GTX_STATUS)%'
      $appLang: '%env(COUNTRY_TAG)%'
      $immovisitaMixpanelSecretKey: '%env(IMMOVISITA_MIXPANEL_CALLBACK_API_KEY)%'

  App\:
    resource: '../../src/*'
    exclude: '../../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php}'

  _instanceof:
    App\Component\SyncScheduler\Client\UrlGenerator\UrlGeneratorInterface:
      tags: ['sync_scheduler.url_resolver']

  App\Controller\:
    resource: '../../src/Controller'
    tags: ['controller.service_arguments']

  App\Service\Base\Jwt\Validator\JwtTokenValidator:
    arguments:
      $signatureKey: '%token_signature%'

  App\Service\Base\Jwt\Decoder\JwtTokenDecoder:
    arguments:
      $signatureKey: '%token_signature%'

  App\Service\Base\Jwt\Encoder\JwtTokenEncoder:
    arguments:
      $signatureKey: '%token_signature%'

  App\Service\Base\Jwt\JwtService:
      arguments:
          $signatureKey: '%token_signature%'

  Symfony\Component\DomCrawler\Crawler:

  Sentry\SentryBundle\SentrySymfonyClient:
    alias: 'sentry.client'

  App\Component\LandingPage\TelefonoSmart:
    arguments:
      $staticPageTelefonoSmart: '%getrix.static_page_telefono_smart%'
    tags: ['getrix_app.landing_page']

  App\Component\LandingPage\Messaging:
    arguments:
      $baseUrl: '%getrix.baseurl%'
    tags: ['getrix_app.landing_page']

  App\Component\Sso\Strategy\SsoPortalListGlobally:
    arguments:
      $config: '%ssoListGlobally%'
    tags: ['sso.portal.strategy']

  App\Component\Sso\Handler\SsoPortalHandler:
    arguments:
      $ssoPortals: '%ssoPortals%'

  App\Component\SyncScheduler\Logger\SyncSchedulerProcessor:
    tags:
      - { name: monolog.processor, handler: sync_scheduler }

  App\Logger\ReportSimilarPropertiesProcessor:
    tags:
      - { name: monolog.processor, handler: report_similar_properties }

  App\Logger\ReportLowNumberSearchesProcessor:
    tags:
      - { name: monolog.processor, handler: report_low_number_searches }

  App\Logger\ReportPropertyChangeProcessor:
    tags:
      - { name: monolog.processor, handler: report_property_change }

  App\Service\MigrationVerifier\Logger\MigrationVerifierProcessor:
    tags:
      - { name: monolog.processor, handler: migration_verifier }

  App\Logger\ImmovisitaTosProcessor:
    tags:
      - { name: monolog.processor, handler: immovisita_tos }

  App\Component\SyncScheduler\Client\SyncSchedulerClient:
    configurator: 'App\Component\SyncScheduler\Client\ClientConfigurator:configure'

  App\Service\MigrationVerifier\Client\MigrationVerifierClient:
    configurator: 'App\Service\MigrationVerifier\Client\ClientConfigurator:configure'

  App\Component\SyncScheduler\Client\ClientConfigurator:
    arguments:
      $serviceEndpointConfigs: '%sync_scheduler.service_endpoint%'
      $isSchedulingEnabled: '%sync_scheduler.enabled%'

  App\Service\MigrationVerifier\Client\ClientConfigurator:
    arguments:
      $serviceEndpointConfigs: '%migration_verifier.service_endpoint%'
      $isVerificationEnabled: '%migration_verifier.enabled%'

  App\Component\SyncScheduler\EventSubscriber\SyncSchedulerEventSubscriber:
    arguments:
      $logger: '@monolog.logger.sync_scheduler'

  App\Service\MigrationVerifier\EventSubscriber\MigrationVerifierEventSubscriber:
    arguments:
      $logger: '@monolog.logger.migration_verifier'

  App\Service\MigrationVerifier\Client\UrlGenerator:
    arguments:
      $serviceEndpointConfigs: '%migration_verifier.service_endpoint%'

  App\EventSubscriber\ImmovisitaEventSubscriber:
    arguments:
      $immobiliareBaseUrl: '%env(IMMOBILIARE_BASEURL)%'
      $immovisitaBaseUrl: '%env(IMMOVISITA_BASEURL)%'

  App\EventSubscriber\PropertyChangedEventSubscriber:
      arguments:
        $logger: '@monolog.logger.report_property_change'

  App\EventSubscriber\ScheduledVisitEventSubscriber:
    arguments:
      $immobiliareBaseUrl: '%env(IMMOBILIARE_BASEURL)%'
      $immovisitaBaseUrl: '%env(IMMOVISITA_BASEURL)%'

  App\EventSubscriber\SearchEventSubscriber:
    arguments:
      $immobiliareBaseUrl: '%env(IMMOBILIARE_BASEURL)%'

  App\Service\Base\Adapter\AgencyAdapter:
    arguments:
      $baseUrl: "%immobiliare.baseurl%"

  App\Component\Module\Authorizer\EstimatesAuthorizer:
    tags:
      - { name: 'module_authorizer', module: !php/const App\Constants\Base\GtxConstants::ESTIMATES_LABEL }

  App\Component\Module\Authorizer\AgencyEstimatesAuthorizer:
    tags:
      - { name: 'module_authorizer', module: !php/const App\Constants\Base\GtxConstants::AGENCY_ESTIMATES_LABEL }

  App\Component\Module\Authorizer\GetrixAuthorizer:
    tags:
      - { name: 'module_authorizer', module: !php/const App\Constants\Base\GtxConstants::GETRIX_MODULE_NAME }

  App\Component\Module\Authorizer\SalesRequestsAuthorizer:
    tags:
      - { name: 'module_authorizer', module: !php/const App\Constants\Base\GtxConstants::SALES_REQUESTS_LABEL }

  App\Component\Module\Authorizer\YoudomusAuthorizer:
    tags:
      - { name: 'module_authorizer', module: !php/const App\Constants\Base\GtxConstants::YOUDOMUS_LABEL }

  App\Component\Module\Authorizer\MatchesAuthorizer:
    tags:
      - { name: 'module_authorizer', module: !php/const App\Constants\Base\GtxConstants::MATCHES_MODULE_NAME }

  App\Component\Module\Authorizer\ZonesAuthorizer:
    tags:
      - { name: 'module_authorizer', module: !php/const App\Constants\Base\GtxConstants::ZONES_MODULE_NAME }

  App\Component\Module\Authorizer\PerformanceAuthorizer:
    tags:
      - { name: 'module_authorizer', module: !php/const App\Constants\Base\GtxConstants::PERFORMANCE_MODULE_NAME }

  App\Helper\ApiWsseHeaderGenerator:
    arguments:
      $apiWsseUsername: '%api_wsse_username%'
      $apiWssePassword: '%api_wsse_password%'

  App\Helper\AgencyEstimatesApiClientHelper:
    arguments:
      $logger: '@monolog.logger.report_similar_properties'
      $loggerReportSearches: '@monolog.logger.report_low_number_searches'
      $loggerExposurePois: '@monolog.logger.report_low_number_exposure_pois'

  getrix_app.helper.user:
    class: App\Helper\Base\User
    public: true

  ekbl.stats_bundle.annotation_driver:
    class: App\Component\Stats\Annotation\Driver
    tags:
      - {name: kernel.event_listener, event: kernel.controller, method: onKernelController}
      - {name: kernel.event_listener, event: kernel.terminate, method: onKernelTerminate}
      - {name: kernel.event_listener, event: kernel.exception, method: onKernelException}
    arguments:
      - '@annotation_reader'
      - '@ekbl.stats_bundle.stats_collector'
      - '%ekbl_stats.annotation_enabled%'
      - '%ekbl_stats.annotation_prefix_exceptions%'
      - '%ekbl_stats.annotation_strip_namespace%'

  getrix_app.service_helper.two_factor_authentication:
    class: App\Helper\Base\TwoFactorAuthentication
    public: true
    arguments:
      $defaultTargetPath: '%app.default_target_path%'
      $appTag: '%app.tag%'
      $secret: '%secret%'

  getrix_app.user_repository:
    class: App\Repository\UserRepository
    public: true

  security.authentication.success_handler:
    class: App\Security\Authentication\AuthenticationSuccessHandler
    arguments:
      $httpUtils: '@security.http_utils'

  getrix_app.user.provider:
    class: '%app.user_provider.class%'
    public: true

  getrix_app.helper.contract:
    alias: App\Helper\Base\Contract
    public: true

  getrix_rest.geo:
    alias: App\Service\Rest\Geo
    public: true

  getrix_app.helper.property:
    alias: App\Helper\Base\Property
    public: true

  getrix_app.helper.security:
    alias: App\Helper\Base\Security
    public: true

  getrix_app.service_helper.two_factor_authentication_authorized_device:
    alias: App\Helper\Base\TwoFactorAuthenticationAuthorizedDevice
    public: true

  getrix_app.authorized_device_repository:
    alias: App\Repository\AuthorizedDeviceRepository
    public: true

  getrix_app.helper.localization:
    alias: App\Helper\Localization
    public: true

  App\DataMapper\EntityToResponse\OnboardingTour\OnboardingTourStepDataMapper:
    arguments:
      $getrixBaseUrl: '%getrix.baseurl%'

  App\Formatter\Property\AdExportFormatter:
    arguments:
      $getrixName: '%getrix.name%'
      $portalName: '%app.ad_portal%'

  App\Formatter\Property\AdFormatter:
    arguments:
      $baseUrl: '%getrix.baseurl%'

  App\Formatter\Property\AuctionFormatter:
    arguments:
      $baseUrl: '%getrix.baseurl%'

  App\Formatter\Property\ListingAdFormatter:
    arguments:
      $baseUrl: '%getrix.baseurl%'

  App\Formatter\Property\PropertyFormatter:
    arguments:
      $baseUrl: '%getrix.baseurl%'

  App\Formatter\Property\PrivatePropertyFormatter:
    arguments:
      $baseUrl: '%getrix.baseurl%'

  App\Formatter\Messaging\PropertyFormatter:
    arguments:
      $baseUrl: '%getrix.baseurl%'

  App\Formatter\Messaging\ThreadFormatter:
    arguments:
      $baseUrl: '%getrix.baseurl%'

  App\Formatter\Messaging\ListThreadFormatter:
    arguments:
      $baseUrl: '%getrix.baseurl%'

  App\Service\Sdk\CacheStorage:
    arguments:
      $memcached: '@sdk.memcached'

  session.memcached:
    class: \Memcached
    calls:
    - method: addServer
      arguments:
      - "%env(MEMCACHED_HOST)%"
      - "%env(MEMCACHED_PORT)%"

  sdk.memcached:
    class: \Memcached
    calls:
      - method: addServer
        arguments:
          - "%env(MEMCACHED_HOST)%"
          - "%env(MEMCACHED_SDK_PORT)%"

  getrix_app.helper.invoices:
    alias: App\Helper\Base\Invoices
    public: true

  getrix_app.agency_adapter:
    alias: App\Service\Base\Adapter\AgencyAdapter
    public: true

  getrix_app.image_formatter:
    alias: App\Formatter\Image
    public: true

  getrix_app.youtube:
    alias: App\Service\Base\YouTube
    public: true

  getrix_app.request_proxy:
    alias: App\Service\Base\RequestProxy
    public: true

  getrix_app.acquisition_handler:
    alias: App\Component\Acquisition\Handler\AcquisitionHandler
    public: true

  getrix_app.builder.acquisition_response:
    alias: App\Service\Base\Builder\AcquisitionResponseBuilder
    public: true

  getrix_app.estimates_handler:
    alias: App\Component\Estimates\Handler\EstimatesHandler
    public: true

  getrix_app.session_getrix:
    alias: App\Service\Base\Session\Getrix
    public: true

  getrix_rest.sso_service:
    alias: App\Service\Rest\Sso
    public: true

  getrix_app.helper.security_gtx:
    alias: App\Helper\Base\SecurityGtx
    public: true

  getrix_app.helper.security_pro:
    alias: App\Helper\Base\SecurityPro
    public: true

  getrix_app.service_helper.check_status:
    alias: App\Helper\Base\CheckStatus
    public: true

  getrix_mail.mailer:
    alias: App\Service\Mail\Mailer
    public: true

  getrix_rest.security_helper:
    alias: App\Helper\Security
    public: true

  getrix_multisend.helper.portals:
    alias: App\Helper\PortalsHelper
    public: true

  App\Helper\Base\CheckStatus:
    arguments:
      $needsTosUpdate: '%app.needs_tos_update%'

  App\Helper\Agency:
    arguments:
      $immobiliareApiKey: '%immobiliare.api_key%'

  App\Helper\MfaHelper:
    arguments:
      $baseUrl: '%env(API_MFA_BASE_URL)%'
      $username: '%env(API_MFA_USERNAME)%'
      $password: '%env(API_MFA_PASSWORD)%'
      $timeOut: '%env(API_MFA_TIMEOUT)%'

  getrix_rest.agency_helper:
    alias: App\Helper\Agency
    public: true

  App\Controller\Base\TwoFactorAuthenticationController:
    arguments:
      $securityForceA3fOnSuspectedIp: '%env(bool:SECURITY_FORCE_A3F_ON_SUSPECTED_IP)%'

  App\Service\Base\AppInfo:
    arguments:
      $appTag: '%app.tag%'
      $getrixTag: '%app_tag_gtx%'
      $proTag: '%app_tag_pro%'

  Symfony\Component\HttpFoundation\Session\Storage\Handler\MemcachedSessionHandler:
    arguments: ['@session.memcached', { prefix: "%app.memcached.prefix%", expiretime: '%env(MEMCACHED_EXPIRE_TIME)%' }]

  App\Security\LogoutSuccessHandler:
    arguments: ['@session.memcached', '@security.authentication.rememberme.services.simplehash.secured_area', '%app.logout_target%', '%getrix.cookie_session_name%' ]

  App\Security\Authentication\Provider\LoginProvider:
    abstract: true
    arguments: [ 'secured_area', '%security.authentication.hide_user_not_found%', '%getrix.baseurl_internal%/ws/login_attempt_notify', '@getrix_app.user.provider']

  App\Service\Base\GetrixRequestProxy:
    arguments:
      $getrixBaseUrl: '%getrix.baseurl_internal%'

  App\Helper\Base\TwoFactorAuthentication:
    arguments:
      $defaultTargetPath: '%app.default_target_path%'
      $appTag: '%app.tag%'
      $secret: '%secret%'

  App\Service\Base\TwoFactorAuthenticationProvider:
    arguments:
      $apiBaseurl: '%env(A2F_API_BASEURL)%'
      $apiKey: '%app.a2f.api_key%'
      $apiSecret: '%env(A2F_API_SECRET)%'
      $appName: '%app.name%'
      $enableApiMfa: '%env(bool:API_MFA_ENABLED)%'

  app.session_handler:
    class: '%app.session_handler_class_name%'
    arguments:
      $getrixImmoGtxBaseUrl: '%env(GETRIX_IMMO_GTX_BASEURL)%'
      $sessionUserDataRetention: '%env(SESSION_USER_DATA_RETENTION)%'
      $securityCheckDataCenterIp: '%env(bool:SECURITY_CHECK_DATACENTER_IP)%'
      $securityCheckForeignIp: '%env(bool:SECURITY_CHECK_FOREIGN_IP)%'
      $securityCheckNewIp: '%env(bool:SECURITY_CHECK_NEW_IP)%'
      $securityCheckSharedIp: '%env(bool:SECURITY_CHECK_SHARED_IP)%'
      $securityCheckVpnIp: '%env(bool:SECURITY_CHECK_VPN_IP)%'
      $securityRemoveDeviceOnForeignIp: '%env(bool:SECURITY_REMOVE_DEVICE_ON_FOREIGN_IP)%'

  App\EventListener\FeatureToggleListener:
    tags:
    - { name: kernel.event_listener, event: kernel.request, method: onKernelRequest }

  App\EventListener\ModuleListener:
    tags:
    - { name: kernel.event_listener, event: kernel.controller, method: onKernelController }

  App\EventListener\GetrixSessionResponseListener:
    tags:
    - { name: kernel.event_listener, event: kernel.response, method: onKernelResponse }
    arguments: ['@app.session_handler', '%getrix.cookie_session_domain%']

  App\EventListener\GetrixSessionRequestListener:
    tags:
    - { name: kernel.event_listener, event: kernel.request, method: onKernelRequest }
    arguments:
      $getrixSession: '@app.session_handler'
      $serviceUnavailableFilepath: '%app.service_unavalaible_file%'
      $mainteinanceAccessCookieName: '%app.cookie_mainteinance_access_name%'
      $apptag: '%app.tag%'

  App\DataCollector\AppInfoDataCollector:
    tags:
      -
        name:     data_collector
        template: 'base/collector/app_info.html.twig'
        # must match the value returned by the getName() method
        id:       'app.app_info_collector'
        # optional priority
        # priority: 300
    public: false

  App\DataCollector\SdkDataCollector:
    tags:
      -
        name:     data_collector
        template: 'base/collector/sdk.html.twig'
        # must match the value returned by the getName() method
        id:       'app.backend_collector'
        # optional priority
        # priority: 300
    public: false

  App\EventListener\UserLoggedInListener:
    tags:
    - { name: kernel.event_listener, event: security.interactive_login, method: onSecurityInteractiveLogin }

  App\EventListener\ExceptionListener:
    tags:
    - { name: kernel.event_listener, event: kernel.exception, method: onKernelException }
    arguments:
      $environment: '%kernel.environment%'
      $apptag: '%app.tag%'

  App\Component\Module\LandingPage\UrlGenerator:
    arguments:
      $baseUrl: '%getrix.baseurl%'

  App\Formatter\ImmobiliareUrlFormatter:
    arguments:
      $baseUrl: '%immobiliare.baseurl%'
      $gestionaleBaseUrl: '%immobiliare.gestionale.baseurl%'
      $adPortalDetailUrl: '%app.ad_portal_detail_url%'
      $adPortalChildDetailUrl: '%app.ad_portal_child_detail_url%'
      $adPortalPreviewDetailUrl: '%app.ad_portal_preview_detail_url%'

  App\Formatter\PropertyImageFormatter:
    arguments:
      $mediaPrefix: '%env(GETRIX_CDN_URL)%'
      $imageEndpoint: '%app.ad.image.endpoint%'
      $imageNoResizeEndpoint: '%app.ad.imagenoresize.endpoint%'

  App\Formatter\PortalImageFormatter:
    arguments:
      $mediaPrefix: '%env(GETRIX_CDN_URL)%'
      $imageEndpoint: '%app.ad.image.endpoint%'
      $imageNoResizeEndpoint: '%app.ad.imagenoresize.endpoint%'

  App\Formatter\PropertyPlanFormatter:
      arguments:
          $mediaPrefix: '%env(GETRIX_CDN_URL)%'
          $imageEndpoint: '%app.ad.plan.endpoint%'
          $imageNoResizeEndpoint: '%app.ad.imagenoresize.endpoint%'

  App\Formatter\Image:
    arguments:
      $mediaPrefix: '%env(GETRIX_CDN_URL)%'
      $mediaAgentPrefix: '%env(GETRIX_MEDIA_AGENT_URL)%'
      $agencyLogoEndpoint: '%app.agency.logo.endpoint%'
      $imageEndpoint: '%app.ad.image.endpoint%'
      $imageNoResizeEndpoint: '%app.ad.imagenoresize.endpoint%'
      $agentEndpoint: '%app.agent.logo.endpoint%'

  App\Formatter\StaticMapImage:
    arguments:
      $cdnBaseUrl: '%getrix.staticMapsBaseUrl%'
      $baseUrl: '%getrix.baseurl%'

  App\Formatter\StaticCityZonesMapImage:
    arguments:
      $cdnBaseUrl: '%getrix.staticCityMapsBaseUrl%'
      $mapsTag: '%getrix.staticCityMapsTag%'

  App\Formatter\StaticCityMapImage:
    arguments:
      $cdnBaseUrl: '%getrix.staticCityMapsBaseUrl%'
      $mapsTag: '%getrix.staticCityMapsTag%'

  App\Formatter\StaticCityPolygonMapImage:
    arguments:
      $cdnBaseUrl: '%getrix.staticMapsBaseUrl%'

  App\Formatter\GtxUrlFormatter:
    arguments:
      $baseUrl: '%getrix.baseurl%'

  App\Formatter\Project\ProjectFormatter:
    arguments:
      $baseUrl: "%getrix.baseurl%"

  App\Formatter\ProjectImmobiliareUrlFormatter:
    arguments:
      $baseUrl: "%immobiliare.baseurl%"
      $gestionaleBaseUrl: "%immobiliare.gestionale.baseurl%"

  app.sdk_data_collector:
    class: App\DataCollector\SdkDataCollector
    tags:
    - name:     data_collector
      template: 'base/collector/layout.html.twig'
      id:       'app.sdk_data_collector'
      priority: 1

  app.menu_normalizer:
      class: App\Component\Menu\Normalizer\MenuNormalizer
      tags:
          - { name: serializer.normalizer }

  App\Helper\PortalsHelper:
    arguments:
      $cookieSessionDomain: '%app.cookie_session_domain%'

  App\Helper\Base\TwoFactorAuthenticationAuthorizedDevice:
    arguments:
      $appTag: '%app.tag%'
      $secret: '%secret%'

  App\Helper\Base\Contract:
    arguments:
      $baseUrl: "%immobiliare.baseurl%"

  App\Service\Rest\Sso:
    arguments:
      $userProvider: '@getrix_app.user.provider'
      $immobiliareApiKey: '%immobiliare.api_key%'
      $rememberMeServices: '@security.authentication.rememberme.services.simplehash.secured_area'
      $ssoJwtKey: '%env(SSO_JWT_KEY)%'
      $ssoProductPrivateKey: '%env(SSO_PRODUCT_PRIVATE_KEY)%'

  App\Service\Base\Session\Getrix:
    arguments:
      $getrixImmoGtxBaseUrl: '%env(GETRIX_IMMO_GTX_BASEURL)%'
      $sessionUserDataRetention: '%env(SESSION_USER_DATA_RETENTION)%'
      $securityCheckDataCenterIp: '%env(bool:SECURITY_CHECK_DATACENTER_IP)%'
      $securityCheckForeignIp: '%env(bool:SECURITY_CHECK_FOREIGN_IP)%'
      $securityCheckNewIp: '%env(bool:SECURITY_CHECK_NEW_IP)%'
      $securityCheckSharedIp: '%env(bool:SECURITY_CHECK_SHARED_IP)%'
      $securityCheckVpnIp: '%env(bool:SECURITY_CHECK_VPN_IP)%'
      $securityRemoveDeviceOnForeignIp: '%env(bool:SECURITY_REMOVE_DEVICE_ON_FOREIGN_IP)%'

  App\Helper\SsoHelper:
    arguments:
      $secretKey: '%new_getrix.api_key%'
      $newGetrixInternalBaseUrl: '%env(GETRIX_IMMO_GTX_BASEURL_INTERNAL)%'

  App\Helper\Base\ParametersBag:
    arguments:
      $parameters:
        google:
          googleTagManagerId: "%app.google.googleTagManagerId%"
          googleMapsKey: "%getrix.googleMapsKey%"
          googleMapsVersion: "%getrix.googleMapsVersion%"
        immobiliare:
          baseUrl: "%immobiliare.baseurl%"
          mediaUrl: "%immobiliare.mediaurl%"
          responsabile_avatar_url: "%immobiliare.responsabile_avatar_url%"
          responsabile_avatar_sapi_url: "%immobiliare.responsabile_avatar_sapi_url%"
          images_url: "%immobiliare.images_url%"
        app:
          autocomplete_city_suggest_nation: "%app.autocomplete.city_suggest_nation%"
          autocomplete_city_suggest_type: "%app.autocomplete.city_suggest_type%"
          baseUrl: "%app.baseurl%"
          dea_api_baseurl: '%env(string:DEA_API_BASEURL)%'
          dea_api_username: '%env(string:DEA_API_USERNAME)%'
          dea_api_password: '%env(string:DEA_API_PASSWORD)%'
          dea_api_timeout: '%env(int:DEA_API_TIMEOUT)%'
          dea_api_connect_timeout: '%env(int:DEA_API_CONNECT_TIMEOUT)%'
          client_app_login_enabled: "%app.client_app_login_enabled%"
          tag: "%app.tag%"
          country_tag: "%env(COUNTRY_TAG)%"
          name: "%app.name%"
          name_pro: "%app.name_pro%"
          subscribed_agencies: "%app.subscribed_agencies%"
          body_css_class_suffix: "%app.body_css_class_suffix%"
          email_from_address: "%app.email_from_address%"
          title: "%app.title%"
          title_pro: "%app.title_pro%"
          firma_email: "%app.firma_email%"
          debug: "%kernel.debug%"
          cookie_session_name: "%app.cookie_session_name%"
          cookie_session_domain: "%app.cookie_session_domain%"
          cookie_session_persist_name: "%app.cookie_session_persist_name%"
          cookie_loginevent_name: "%app.cookie_loginevent_name%"
          svg_icons_sprite_file_path: "%app.svg_icons_sprite_file_path%"
          common_image_base_url: "%getrix.baseurl%%app.common_image_base_url%"
          common_templates_path: "%app.common_templates_path%"
          whiteLabel: "%app.white_label%"
          ad_portal: "%app.ad_portal%"
          ad_portal_about_us_url: "%app.ad_portal_about_us_url%"
          ad_portal_contact_us_url: "%app.ad_portal_contact_us_url%"
          privacy_data_owner: "%app.privacy_data_owner%"
          phone_verification: "%app.phone_verification%"
          email_servizio_clienti: "%app.email_servizio_clienti%"
          email_assistenza_clienti: "%app.email_assistenza_clienti%"
          email_assistenza_contenuti: "%app.email_assistenza_contenuti%"
          email_responsabile_default: "%app.email_responsabile_default%"
          tel_servizio_clienti: "%app.tel_servizio_clienti%"
          email_responsabile_assistenza_clienti: "%app.email_responsabile_assistenza_clienti%"
          email_chat: "%app.email_chat%"
          service_smart_phone: "%app.service_smart_phone%"
          service_virtual_tour_360: "%app.service_virtual_tour_360%"
          service_photoplan: "%app.service_photoplan%"
          service_remote_visits: "%app.service_remote_visits%"
          visibility_premium_name: "%app.visibility.premium_name%"
          visibility_top_name: "%app.visibility.top_name%"
          visibility_star_name: "%app.visibility.star_name%"
          visibility_showcase_name: "%app.visibility.showcase_name%"
          visibility_sky_name: "%app.visibility.sky_name%"
          visibility_secret_property_name: "%app.visibility.secret_property_name%"
          facebook_url: "%app.facebook_url%"
          twitter_url: "%app.twitter_url%"
          beamer_news: "%app.beamer_news%"
          beamer_product_id: "%app.beamer_product_id%"
          onboarding_enabled: "%app.onboarding_enabled%"
          appcues_enabled: "%app.appcues_enabled%"
          visibilities_all_extra_visibilities_product: "%app.visibilities_all_extra_visibilities_product%"
          salesforce_offline_form_enabled: '%env(SALESFORCE_OFFLINE_FORM_ENABLED)%'
          salesforce_offline_form_endpoint: '%env(SALESFORCE_OFFLINE_FORM_ENDPOINT)%'
          salesforce_offline_form_agency_field: '%env(SALESFORCE_OFFLINE_FORM_AGENCY_FIELD)%'
          salesforce_offline_form_name_field: '%env(SALESFORCE_OFFLINE_FORM_NAME_FIELD)%'
          salesforce_offline_form_lastname_field: '%env(SALESFORCE_OFFLINE_FORM_LASTNAME_FIELD)%'
          salesforce_offline_form_subject_field: '%env(SALESFORCE_OFFLINE_FORM_SUBJECT_FIELD)%'
          salesforce_offline_form_text_field: '%env(SALESFORCE_OFFLINE_FORM_TEXT_FIELD)%'
          salesforce_offline_form_is_getrix_field: '%env(SALESFORCE_OFFLINE_FORM_IS_GETRIX_FIELD)%'
          salesforce_offline_form_org_id_field: '%env(SALESFORCE_OFFLINE_FORM_ORG_ID_FIELD)%'
          salesforce_live_chat_core_url: '%env(SALESFORCE_LIVE_CHAT_CORE_URL)%'
          salesforce_live_chat_enabled: '%env(SALESFORCE_LIVE_CHAT_ENABLED)%'
          salesforce_live_chat_id: '%env(SALESFORCE_LIVE_CHAT_ID)%'
          salesforce_live_chat_lang: '%env(SALESFORCE_LIVE_CHAT_LANG)%'
          salesforce_live_chat_name: '%env(SALESFORCE_LIVE_CHAT_NAME)%'
          salesforce_live_chat_scrt2url: '%env(SALESFORCE_LIVE_CHAT_SCRT2URL)%'
          locale: "%locale%"
          agency_language_selection_enabled: '%env(AGENCY_LANGUAGE_SELECTION_ENABLED)%'
          agency_logo_image_endpoint: '%app.agency.logo.endpoint%'
          virtual_tour_360_service_name: "%app.virtual_tour_360_service_name%"
          storage_responsible_images_api_enabled: "%app.storage_responsible_images_api_enabled%"
          pro_dashboard_stats_filters: "%app.pro_dashboard_stats_filters%"
          youdomus_switch: "%app.youdomusSwitch%"
          youdomus_endpoint: "%app.youdomusEndpoint%"
          mixpanel_token: "%app.mixpanel_token%"
          mixpanel_debug: "%app.mixpanel_debug%"
          user_events_tracking_toggle: "%app.user_events_tracking_toggle%"
          events_queue_ws_endpoint: "%app.events_queue_ws_endpoint%"
          events_queue_ws_auth_username: "%app.events_queue_ws_auth_username%"
          events_queue_ws_auth_password: "%app.events_queue_ws_auth_password%"
          settings_general_privacy_instrument_appointment: "%app.settings_general_privacy_instrument_appointment%"
          menu_counters_enabled: "%app.menu_counters_enabled%"
          url_cdn_img_cartine: "%app.url_cdn_img_cartine%"
          img_cartine_size: "%app.img_cartine_size%"
          img_cartine_version: "%app.img_cartine_version%"
          property_list_performance: "%app.property_list_performance%"
          local_timezone: "%app.local_timezone%"
          datetime_format:
            short: "%app.datetime_format.short%"
            medium: "%app.datetime_format.medium%"
            extended: "%app.datetime_format.extended%"
            only_time: "%app.datetime_format.only_time%"
            default: "%app.datetime_format.default%"
          logo: "%app.logo%"
          default_lang_code: "%default_lang_code%"
          sentry_enabled: "%app.sentry_enabled%"
          performance_price_proposal_enabled: "%env(PERFORMANCE_PRICE_PROPOSAL_ENABLED)%"
          performance_show_agent_card: '%env(PERFORMANCE_SHOW_AGENT_CARD)%'
          performance_show_absolute_data: '%env(PERFORMANCE_SHOW_ABSOLUTE_DATA)%'
          performance_show_improve_advices: "%env(PERFORMANCE_SHOW_IMPROVE_ADVICES)%"
          performance_show_perday_contacts_chart: '%env(PERFORMANCE_SHOW_PERDAY_CONTACTS_CHART)%'
          performance_show_previous_stats: "%env(PERFORMANCE_SHOW_PREVIOUS_STATS)%"
          performance_show_property_cta: '%env(PERFORMANCE_SHOW_PROPERTY_CTA)%'
          matches_feature_enabled: '%env(bool:MATCHES_FEATURE_ENABLED)%'
          zones_feature_enabled: '%env(bool:ZONES_FEATURE_ENABLED)%'
          zone_package_tier_low: '%app.zone_package_tier_low%'
          zone_package_tier_medium: '%app.zone_package_tier_medium%'
          zone_package_tier_high: '%app.zone_package_tier_high%'
          user_profile_url: '%app.user_profile_url%'
          fullstory_enabled: '%env(FULLSTORY_ENABLED)%'
          dynatrace_enabled: '%env(DYNATRACE_ENABLED)%'
          metrics_site: '%env(METRICS_SITE)%'
          property_list_match_column: "%app.property_list_match_column%"
          register_vat_field: "%app.register.vat_field%"
          api_section_remote_visits: "%app.api_section_remote_visits%"
          notification_settings_enabled: "%app.notification_settings_enabled%"
          ad_imagenoresize_endpoint: "%app.ad.imagenoresize.endpoint%"
          ad_image_endpoint: "%app.ad.image.endpoint%"
          agent_logo_endpoint: "%app.agent.logo.endpoint%"
          portal_logo_endpoint: "%app.portal.logo.endpoint%"
          responsible_logo_endpoint: "%app.responsible.logo.endpoint%"
          helpjuice_enabled: "%app.helpjuice_enabled%"
          helpjuice_account_url: "%app.helpjuice_account_url%"
          helpjuice_subdomain: "%app.helpjuice_subdomain%"
          braze_api_key: "%app.braze.api_key%"
          braze_in_app_messages: '%app.braze.in_app_messages%'
          braze_logging: '%app.braze.logging%'
          braze_logging_users: '%app.braze.logging_users%'
          braze_push_notifications: '%app.braze.push_notifications%'
          braze_sdk_endpoint: "%app.braze.sdk_endpoint%"
          favicon_path: "%app.favicon_path%"
          report_insights_enabled: "%app.report_insights_enabled%"
          report_insights_endpoint: "%app.report_insights_endpoint%"
          digital_wallet_enabled: "%app.digital_wallet_enabled%"
          show_cadastral_data: "%app.show_cadastral_data%"
          messaging_v2_enabled: "%app.messaging_v2_enabled%"
          messaging_v2_agency_enabled: "%app.messaging_v2_agency_enabled%"
          show_first_page_tag: "%app.show_first_page_tag%"
          first_page_tag_threshold: "%app.first_page_tag_threshold%"
          cockade_icon_threshold: "%app.cockade_icon_threshold%"
          add_property_enabled: "%app.add_property_enabled%"
          property_field_solar_water_heating_enabled: "%app.property_field_solar_water_heating_enabled%"
          property_field_available_for_students_enabled: "%app.property_field_available_for_students_enabled%"
          property_field_views: "%app.property_field_views%"
          property_distance_from_sea_feature_id: "%app.property_distance_from_sea_feature_id%"
          property_distance_from_sea_unit_of_measurement: "%app.property_distance_from_sea_unit_of_measurement%"
          property_field_view_enabled: "%app.property_field_view_enabled%"
          property_field_distance_from_sea_enabled: "%app.property_field_distance_from_sea_enabled%"
          max_ad_planimetry: "%app.max_ad_planimetry%"
          growthbook_api_host: "%app.growthbook_api_host%"
          growthbook_client_key: "%app.growthbook_client_key%"
          growthbook_decryption_key: "%app.growthbook_decryption_key%"
          growthbook_dev_mode_enabled: "%app.growthbook_dev_mode_enabled%"
          growthbook_enabled: "%app.growthbook_enabled%"
          mixpanel_identify_tracking: "%app.mixpanel_identify_tracking%"
          sidebar_left_enabled: "%app.sidebar_left_enabled%"
          sidebar_right_enabled: "%app.sidebar_right_enabled%"
          fotoplan_baseurl: "%app.fotoplan_baseurl%"
          getrix_media_url: "%app.getrix_media_url%"
          show_registration_business_type: "%app.show_registration_business_type%"
          languages_map_lookup_key: "%app.languages_map_lookup_key%"

        getrix:
          name: "%getrix.name%"
          baseurl: "%getrix.baseurl%"
          baseurl_internal: "%getrix.baseurl_internal%"
          media: "%getrix.media%"
          cdn: "%env(GETRIX_CDN_URL)%"
          cookie_session_domain: "%getrix.cookie_session_domain%"
          cookie_session_name: "%getrix.cookie_session_name%"
          cookie_session_persist_name: "%getrix.cookie_session_persist_name%"
          staticMapsBaseUrl: "%getrix.staticMapsBaseUrl%"
          api_baseurl: '%getrix.api_baseurl%'
          api_login_url: '%getrix.api_login_url%'
          use_getrix_api_login: '%getrix.use_getrix_api_login%'
          use_getrix_api_login_authentication_tokens: '%getrix.use_getrix_api_login_authentication_tokens%'
          get_news_content_url: '%getrix.get_news_content_url%'
          logo: "%getrix.logo%"
          favicon_path: "%getrix.favicon_path%"
          access_trace_enable: '%getrix.access_trace_enable%'
          access_trace_base_url: '%env(ACCESS_TRACE_BASE_URL)%'
          access_trace_username: '%env(ACCESS_TRACE_USERNAME)%'
          access_trace_password: '%env(ACCESS_TRACE_PASSWORD)%'
        maps:
          env: '%env(MAPS_ENV)%'
          consumerKey: "%maps.consumerKey%"
          attribution: "%maps.attribution%"
          tilesUrlTemplate: "%maps.tilesUrlTemplate%"
          geocoderUrlTemplate: "%maps.geocoderUrlTemplate%"
          language: "%maps.language%"
        chatbot:
          status: "%chat_bot.status%"
          groupID: "%chat_bot.groupID%"
          customerID: "%chat_bot.customerID%"
        hotjar:
          switch: "%getrix.hotjarSwitch%"
          id: "%getrix.hotjarId%"
          sv: "%getrix.hotjarSnippetVersion%"
        proxy_redirect:
          base_url: "%proxy_redirect_base_url%"
          secret: "%proxy_redirect_secret%"

  App\Helper\TranslationsHelper:
    arguments:
      $translationsPath: '%translator.default_path%'
      $locale: '%locale%'

  App\Controller\Base\PortalPropertiesController:
    arguments:
      $growthbookProxy: '@growthbook_bundle.growthbook_client'

  App\Controller\Base\PortalNewConstructionsController:
    arguments:
      $growthbookProxy: '@growthbook_bundle.growthbook_client'

  App\Controller\Base\PortalAuctionsController:
    arguments:
      $growthbookProxy: '@growthbook_bundle.growthbook_client'

  App\Helper\DashboardHelper:
    arguments:
      $adsStatsCategoriesAllowed: '%app.dashboard_ads_stats_categories_allowed%'
      $growthbookProxy: '@growthbook_bundle.growthbook_client'

  App\Constants\Base\LanguagesConstants:
    arguments:
      $locale: '%locale%'

  App\Helper\Localization:
      arguments:
        $locale: '%locale%'
        $telephoneShortCode: '%app.phone.short_code%'
        $telephoneInternationalPrefix: '%app.phone.international_prefix%'
        $telephoneMobileRegexp: '%app.phone.mobile_regexp%'

  App\Controller\Rest\DefaultController:
    arguments:
      $loggerImmovisitaTos: '@monolog.logger.immovisita_tos'

  App\Controller\EventsTracker\EventsTrackerController:
    arguments:
        $mlsDebuggerLogger: '@monolog.logger.mls_debugger'

  App\Controller\Administration\Base\InvoicingDataAdministrationController:
    arguments:
        $showLandingPage: '%app.settings_invoices_show_landing_page%'

  App\Controller\Base\PropertyController:
    arguments:
      $growthbookProxy: '@growthbook_bundle.growthbook_client'

  app.salesRequest.apiClientHelper:
    class: App\Helper\SalesRequestsApiClientHelper

  app.salesRequest.apiClientHelperMock:
    class: App\Helper\Mock\SalesRequestsApiClientHelperMock

  App\Utils\ISalesRequestsApiClientHelperInterface: '@app.salesRequest.apiClientHelper'

  App\Service\Base\Session\GetrixInterface: '@app.session_handler'

  App\Handler\Property\AdHandler:
    arguments:
      $visibilities:
        premiumId: '%app.visibility.premium%'
        topId: '%app.visibility.top%'
        starId: '%app.visibility.star%'
        showcaseId: '%app.visibility.showcase%'
      $qualityDetailsProperties: '%app.ad.quality-details.fields%'

  App\Handler\Login\ClientApp\ClientAppLoginHandler:
    arguments:
      $clientAppLoginEnabled: '%app.client_app_login_enabled%'

  App\Handler\Login\ClientApp\ClientAppLoginDeaProvider:
    arguments:
      $jwtKey: '%env(string:DEA_API_JWT_KEY)%'

  App\Handler\ReportInsights\ReportInsightsHandler:
    arguments:
      $reportInsightsGetrixApp: '%app.report_insights_getrix_app%'
      $reportInsightsProApp: '%app.report_insights_pro_app%'
      $reportInsightsEndpoint: '%app.report_insights_endpoint%'

  App\Handler\Searches\AgencyActiveSearchHandler:
    arguments:
      $searchesModificationDateDaysInterval: '%app.menu_counters_searches_modification_date_days_interval%'

  App\Handler\Searches\MatchHandler:
    arguments:
      $searchesModificationDateDaysInterval: '%app.menu_counters_searches_modification_date_days_interval%'

  App\DataMapper\EntityToResponse\Property\SearchFiltersDataMapper:
    arguments:
      $visibilities:
        premiumId: '%app.visibility.premium%'
        topId: '%app.visibility.top%'
        starId: '%app.visibility.star%'
        showcaseId: '%app.visibility.showcase%'

  App\Handler\Menu\MenuHandler:
    arguments:
      $configFileMenu: '%app.gx_navigation_menu%'
      $searchesModificationDateDaysInterval: '%app.menu_counters_searches_modification_date_days_interval%'
      $messagingV2Enabled: '%env(bool:MESSAGING_V2_ENABLED)%'
      $growthbookProxy: '@growthbook_bundle.growthbook_client'

  App\Component\Menu\Resolver\UrlResolver:
    arguments:
      $defaultLang: '%default_lang_code%'

  App\Component\Menu\Authorizer\BaseAuthorizer:
      arguments:
          $authorizationChecker: '@security.authorization_checker'
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.base }
  App\Component\Menu\Authorizer\AgencyEstimatesAuthorizer:
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.agency_estimates }
  App\Component\Menu\Authorizer\MortgageAdviceAuthorizer:
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.mortgage-advice }
  App\Component\Menu\Authorizer\RequestsAuthorizer:
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.requests }
  App\Component\Menu\Authorizer\SearchesAuthorizer:
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.searches }
  App\Component\Menu\Authorizer\TwoFactorAuthAuthorizer:
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.twofactor_auth }
  App\Component\Menu\Authorizer\YoudomusEnabledAuthorizer:
      arguments:
          $authorizationChecker: '@security.authorization_checker'
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.youdomus-enabled }
  App\Component\Menu\Authorizer\YoudomusDisabledAuthorizer:
      arguments:
          $authorizationChecker: '@security.authorization_checker'
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.youdomus-disabled }
  App\Component\Menu\Authorizer\GetrixDisabledAuthorizer:
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.getrix-disabled }
  App\Component\Menu\Authorizer\HasMessagingDisabledAuthorizer:
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.messaging-disabled }
  App\Component\Menu\Authorizer\HasMessagingEnabledAuthorizer:
     tags:
        - { name: menu_authorizer, id: app.menu.authorizer.messaging-enabled }
  App\Component\Menu\Authorizer\MatchesAuthorizer:
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.matches }
  App\Component\Menu\Authorizer\ReportInsightsAuthorizer:
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.report_insights }
  App\Component\Menu\Authorizer\ZonesAuthorizer:
      tags:
        - { name: menu_authorizer, id: app.menu.authorizer.zones }
  App\Twig\Webpack\WebpackIncludeChunksExtension:
    arguments:
      $projectDir: '%kernel.project_dir%'

  App\Helper\Youdomus\YoudomusUrlHelper:
    arguments:
      $baseUrl: '%app.youdomusEndpoint%'
      $obtainCadastralDataEndpoint: '%app.youdomus.ObtainCadastralData%'

  App\Handler\Youdomus\YoudomusHandler:
    arguments:
      $youdomusAuthCode: '%app.youdomusAuthCode%'
      $youdomusGetrixApp: '%app.youdomusGetrixApp%'
      $youdomusProApp: '%app.youdomusProApp%'

  App\ArgumentResolver\Agency\CreateAgencyRequestResolver:
      arguments:
          $csrfTokenId: '%app.csrf_token_registration_id%'

  App\Controller\Agency\API\CreateAgencyController:
      arguments:
          $csrfTokenId: '%app.csrf_token_registration_id%'

  App\Handler\Property\AdStatsHandler:
    arguments:
      $showAbsoluteData: '%app.performance_show_absolute_data%'

  App\Helper\Base\CsrfTokenValidatorHelper:
    arguments:
      $tokenManager: '@security.csrf.token_manager'

  App\Helper\Mail\MatchEmailHelper:
    arguments:
      $adPortalDetailUrl: "%app.ad_portal_detail_url%"
      $urlCdnImgCartine: "%app.url_cdn_img_cartine%"

  App\Handler\Zones\ZoneHandler:
    arguments:
      $evaluationZoneOnlyGetrix: '%app.agency.evaluation_zone.only_getrix%'

  App\Helper\TraceApiClientHelper:
    arguments:
      $baseUrl: '%getrix.access_trace_base_url%'
      $username: '%getrix.access_trace_username%'
      $password: '%getrix.access_trace_password%'
      $timeout: '%getrix.access_trace_timeout%'

  App\Service\Mail\Mailer:
    arguments:
        $a2fFromAddress: '%app.email_a2f_from_address%'
  App\Service\Mail\MailSender:
    arguments:
        $fromName: '%app.ad_portal%'
        $fromAddress: '%app.email_from_address%'

  App\Security\Service\AccessTraceService:
    arguments:
      $enableTrace: '%getrix.access_trace_enable%'

  App\DataProvider\Match\ApiGtx\MatchDataProvider:
      arguments:
          $messagingV2Enabled: '%env(bool:MESSAGING_V2_ENABLED)%'
          $growthbookProxy: '@growthbook_bundle.growthbook_client'

  App\Twig\Growthbook\GrowthbookFeature:
      arguments:
          $growthbookProxy: '@growthbook_bundle.growthbook_client'

  App\Component\Growthbook\CountryTagAttributeLoaders:
      arguments:
        $countryTag: '%env(COUNTRY_TAG)%'

