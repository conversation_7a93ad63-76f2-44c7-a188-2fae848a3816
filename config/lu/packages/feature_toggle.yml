feature_toggle:
  groups:
    agency:
      name: "agency"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
    multisend:
      name: "multisend"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    acquisition:
      name: "acquisition"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    agency_estimates:
      name: "agency_estimates"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    propertyfinder:
      name: "propertyfinder"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    extra_services:
      name: "extra_services"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
    virtual_tour_360:
      name: "virtual_tour_360"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    agenda:
      name: "agenda"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    dashboard:
      name: "dashboard"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    administration:
      name: "administration"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
    settings:
      name: "settings"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
    clients:
      name: "clients"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    customer_requests:
      name: "customer_requests"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
    telefono_smart:
      name: "telefono_smart"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
    ads:
      name: "ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
    auctions:
      name: "auctions"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    projects_ads:
      name: "projects_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    new_constructions_property:
      name: "new_constructions_property"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    sso:
      name: "sso"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    visibility:
      name: "visibility"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    immovisita:
      name: "immovisita"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    youdomus:
      name: "youdomus"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    searches:
      name: "searches"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
    zones:
      name: "zones"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
  features:
    multisend_property:
      name: "multisend_property"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "multisend"
    multisend_project:
      name: "multisend_project"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "multisend"
    multisend_portal:
      name: "multisend_portal"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "multisend"
    acquisition_privates:
      name: "acquisition_privates"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "acquisition"
    agency_estimates_add:
      name: "agency_estimates_add"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "agency_estimates"
    agency_estimates_list:
      name: "agency_estimates_list"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "agency_estimates"
    real_estate_evaluation:
      name: "real_estate_evaluation"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "acquisition"
    real_estate_sales_requests:
      name: "real_estate_sales_requests"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "acquisition"
    plans:
      name: "plans"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "extra_services"
    youdomus:
      name: "youdomus"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "extra_services"
    news:
      name: "news"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "extra_services"
    web_service:
      name: "web_service"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "extra_services"
    website:
      name: "website"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "extra_services"
    evaluation:
      name: "evaluation"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "extra_services"
    virtual_tour_360_property:
      name: "virtual_tour_360_property"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "virtual_tour_360"
    virtual_tour_360_project:
      name: "virtual_tour_360_project"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "virtual_tour_360"
    virtual_tour_360_new_constructions:
      name: "virtual_tour_360_new_constructions"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "virtual_tour_360"
    agenda:
      name: "agenda"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "agenda"
    dashboard:
      name: "dashboard"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "dashboard"
    invoices_info:
      name: "invoices_info"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "administration"
    contract_info:
      name: "contract_info"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "administration"
    invoices:
      name: "invoices"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "administration"
    general_settings:
      name: "general_settings"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "settings"
    office_place_settings:
      name: "office_place_settings"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "settings"
    images_video_settings:
      name: "images_video_settings"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "settings"
    users_settings:
      name: "users_settings"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "settings"
    immovisita_settings:
      name: "immovisita_settings"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "settings"
    security_settings:
      name: "security_settings"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "settings"
    zone_settings:
      name: "zone_settings"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "settings"
    propertyfinder_search:
      name: "propertyfinder_search"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "propertyfinder"
    propertyfinder_collaboration:
      name: "propertyfinder_collaboration"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "propertyfinder"
    clients:
      name: "clients"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "clients"
    general_customer_requests:
      name: "general_customer_requests"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "customer_requests"
    direct_customer_requests:
      name: "direct_customer_requests"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "customer_requests"
    managed_customer_requests:
      name: "managed_customer_requests"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "customer_requests"
    requests_new:
      name: "requests_new"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "customer_requests"
    telefono_smart_requests:
      name: "telefono_smart_requests"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "telefono_smart"
    requests_intersections:
      name: "requests_intersections"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "customer_requests"
    requests_messages:
      name: "requests_messages"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "customer_requests"
    property_ads:
      name: "property_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "ads"
    portal_property_ads:
      name: "portal_property_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "ads"
    portal_properties:
      name: "portal_properties"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "ads"
    project_ads:
      name: "project_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "projects_ads"
    projects_ads_list:
      name: "projects_ads_list"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "projects_ads"
    projects_ads_new:
      name: "projects_ads_new"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "projects_ads"
    portal_projects_ads:
      name: "portal_projects_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "projects_ads"
    new_constructions_ads:
      name: "new_constructions_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "new_constructions_property"
    agency_new_constructions_property:
      name: "agency_new_constructions_property"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "new_constructions_property"
    portal_new_constructions_property:
      name: "portal_new_constructions_property"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "new_constructions_property"
    new_constructions_property_add:
      name: "new_constructions_property_add"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "new_constructions_property"
    auctions_ads:
      name: "auctions_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "ads"
    land_ads:
      name: "land_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "ads"
    land_virtual_ads:
      name: "land_virtual_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "ads"
    residential_ads:
      name: "residential_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "ads"
    commercial_ads:
      name: "commercial_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "ads"
    room_ads:
      name: "room_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "ads"
    buildings_ads:
      name: "buildings_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "ads"
    warehouse_ads:
      name: "warehouse_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "ads"
    garage_ads:
      name: "garage_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "ads"
    sheds_ads:
      name: "sheds_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "ads"
    office_ads:
      name: "office_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "ads"
    shop_ads:
      name: "shop_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "ads"
    sold_rented_ads:
      name: "sold_rented_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "ads"
    luxury_ads:
      name: "luxury_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "visibility"
    foreign_ads:
      name: "foreign_ads"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "visibility"
    guaranteed_property:
      name: "guaranteed_property"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "visibility"
    convertible_ad:
      name: "convertible_ad"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "visibility"
    mortgage_advice:
      name: "mortgage_advice"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "extra_services"
    auction_add:
      name: "auction_add"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "auctions"
    auctions_catalogue:
      name: "auctions_catalogue"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "auctions"
    auctions_list:
      name: "auctions_list"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "auctions"
    auctions_list_portal:
      name: "auctions_list_portal"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "auctions"
    immovisita_realestate_list:
      name: "immovisita_realestate_list"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "immovisita"
    immovisita_scheduled_visits:
      name: "immovisita_scheduled_visits"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "immovisita"
    youdomus_services:
      name: "youdomus_services"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "youdomus"
    youdomus_monitoring:
      name: "youdomus_monitoring"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "youdomus"
    youdomus_documents:
      name: "youdomus_documents"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "youdomus"
    youdomus_customer:
      name: "youdomus_customer"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "youdomus"
    youdomus_cadastre_on_map:
      name: "youdomus_cadastre_on_map"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "youdomus"
    youdomus_ape_certifications:
      name: "youdomus_ape_certifications"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "youdomus"
    searches_list:
      name: "searches_list"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
      group: "searches"
    multi_agency:
      name: "multi_agency"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "agency"
    agency_services:
      name: "agency_services"
      enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
      group: "extra_services"
    matches:
        name: "matches"
        enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
        group: "clients"
    zones:
        name: "zones"
        enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
        group: "zones"
